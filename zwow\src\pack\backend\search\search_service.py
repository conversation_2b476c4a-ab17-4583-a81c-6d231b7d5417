"""
搜索服务
提供统一的搜索接口，整合各种搜索功能
"""

import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.settings_loader import get_setting
    from .bm25_searcher import BM25Searcher
    from .hybrid_searcher import HybridSearcher
    from ..knowledge.knowledge_search import KnowledgeSearcher
except ImportError as e:
    print(f"导入模块失败: {e}")
    def get_setting(key, default=None): return default
    class BM25Searcher:
        def __init__(self): pass
        def search(self, query, top_k=5): return []
    class HybridSearcher:
        def __init__(self): pass
        def search(self, query, kb_type="core", top_k=5): return []
    class KnowledgeSearcher:
        def __init__(self): pass
        def search_knowledge(self, query): return ""

# 配置日志
logger = logging.getLogger(__name__)

class SearchService:
    """搜索服务类"""
    
    def __init__(self):
        """初始化搜索服务"""
        # 初始化各种搜索器
        self.knowledge_searcher = KnowledgeSearcher()
        self.bm25_searcher = BM25Searcher()
        self.hybrid_searcher = HybridSearcher()
        
        # 搜索配置
        self.default_search_type = get_setting("search.default_type", "hybrid")
        self.default_kb_type = get_setting("search.default_kb_type", "core")
        self.default_top_k = get_setting("search.default_top_k", 5)
        
        logger.info("搜索服务已初始化")
    
    def search(self, query: str, search_type: str = None, kb_type: str = None, 
               top_k: int = None, **kwargs) -> Dict[str, Any]:
        """
        统一搜索接口
        
        Args:
            query: 查询文本
            search_type: 搜索类型，可选值：'vector', 'bm25', 'hybrid', 'knowledge'
            kb_type: 知识库类型，'core' 或 'help'
            top_k: 返回结果数量
            **kwargs: 其他搜索参数
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        # 设置默认值
        if search_type is None:
            search_type = self.default_search_type
        if kb_type is None:
            kb_type = self.default_kb_type
        if top_k is None:
            top_k = self.default_top_k
        
        try:
            logger.info(f"执行搜索，查询: '{query}', 类型: {search_type}, 知识库: {kb_type}")
            
            start_time = self._get_current_time()
            
            if search_type == "vector":
                results = self._vector_search(query, kb_type, top_k, **kwargs)
            elif search_type == "bm25":
                results = self._bm25_search(query, kb_type, top_k, **kwargs)
            elif search_type == "hybrid":
                results = self._hybrid_search(query, kb_type, top_k, **kwargs)
            elif search_type == "knowledge":
                return self._knowledge_search(query, **kwargs)
            else:
                raise ValueError(f"不支持的搜索类型: {search_type}")
            
            end_time = self._get_current_time()
            search_time = end_time - start_time
            
            # 构建响应
            response = {
                'query': query,
                'search_type': search_type,
                'kb_type': kb_type,
                'results': results,
                'total_results': len(results),
                'search_time_ms': search_time * 1000,
                'status': 'success'
            }
            
            logger.info(f"搜索完成，找到 {len(results)} 个结果，耗时: {search_time:.3f}秒")
            return response
            
        except Exception as e:
            logger.error(f"搜索时出错: {str(e)}")
            return {
                'query': query,
                'search_type': search_type,
                'kb_type': kb_type,
                'results': [],
                'total_results': 0,
                'error': str(e),
                'status': 'error'
            }
    
    def _vector_search(self, query: str, kb_type: str, top_k: int, **kwargs) -> List[Dict]:
        """执行向量搜索"""
        try:
            results = self.knowledge_searcher.search_vector_only(query, kb_type)
            return results[:top_k]
        except Exception as e:
            logger.error(f"向量搜索时出错: {str(e)}")
            return []
    
    def _bm25_search(self, query: str, kb_type: str, top_k: int, **kwargs) -> List[Dict]:
        """执行BM25搜索"""
        try:
            # 根据知识库类型选择向量数据库路径
            if kb_type == "core":
                vector_path = get_setting("vector_path_core", "data/vector/core")
            else:
                vector_path = get_setting("vector_path_help", "data/vector/help")
            
            # 加载文档到BM25搜索器
            if not self.bm25_searcher.load_from_vector_db(vector_path):
                logger.warning(f"无法从 {vector_path} 加载文档到BM25搜索器")
                return []
            
            # 执行搜索
            results = self.bm25_searcher.search(query, top_k)
            return results
            
        except Exception as e:
            logger.error(f"BM25搜索时出错: {str(e)}")
            return []
    
    def _hybrid_search(self, query: str, kb_type: str, top_k: int, **kwargs) -> List[Dict]:
        """执行混合搜索"""
        try:
            fusion_method = kwargs.get('fusion_method', None)
            results = self.hybrid_searcher.search(query, kb_type, top_k, fusion_method)
            return results
        except Exception as e:
            logger.error(f"混合搜索时出错: {str(e)}")
            return []
    
    def _knowledge_search(self, query: str, **kwargs) -> Dict[str, Any]:
        """执行知识搜索（返回格式化文本）"""
        try:
            start_time = self._get_current_time()
            formatted_result = self.knowledge_searcher.search_knowledge(query)
            end_time = self._get_current_time()
            search_time = end_time - start_time
            
            return {
                'query': query,
                'search_type': 'knowledge',
                'formatted_result': formatted_result,
                'search_time_ms': search_time * 1000,
                'status': 'success'
            }
        except Exception as e:
            logger.error(f"知识搜索时出错: {str(e)}")
            return {
                'query': query,
                'search_type': 'knowledge',
                'formatted_result': f"搜索出错: {str(e)}",
                'error': str(e),
                'status': 'error'
            }
    
    def batch_search(self, queries: List[str], search_type: str = None, 
                    kb_type: str = None, top_k: int = None) -> List[Dict[str, Any]]:
        """
        批量搜索
        
        Args:
            queries: 查询列表
            search_type: 搜索类型
            kb_type: 知识库类型
            top_k: 返回结果数量
            
        Returns:
            List[Dict[str, Any]]: 批量搜索结果
        """
        results = []
        for query in queries:
            result = self.search(query, search_type, kb_type, top_k)
            results.append(result)
        
        logger.info(f"批量搜索完成，处理了 {len(queries)} 个查询")
        return results
    
    def search_suggestions(self, query: str, max_suggestions: int = 5) -> List[str]:
        """
        搜索建议
        
        Args:
            query: 查询文本
            max_suggestions: 最大建议数量
            
        Returns:
            List[str]: 搜索建议列表
        """
        try:
            # 简单的搜索建议实现
            # 可以基于历史查询、热门关键词等生成建议
            suggestions = []
            
            # 基于查询词生成一些变体
            if len(query) > 2:
                # 添加一些常见的查询变体
                variations = [
                    f"{query}是什么",
                    f"如何{query}",
                    f"{query}的作用",
                    f"{query}怎么用",
                    f"{query}的特点"
                ]
                suggestions.extend(variations[:max_suggestions])
            
            return suggestions[:max_suggestions]
            
        except Exception as e:
            logger.error(f"生成搜索建议时出错: {str(e)}")
            return []
    
    def get_search_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取搜索历史
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            List[Dict[str, Any]]: 搜索历史记录
        """
        # 这里应该从数据库或缓存中获取搜索历史
        # 目前返回空列表
        return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取搜索服务统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            'default_search_type': self.default_search_type,
            'default_kb_type': self.default_kb_type,
            'default_top_k': self.default_top_k,
            'bm25_stats': self.bm25_searcher.get_statistics(),
            'hybrid_stats': self.hybrid_searcher.get_statistics()
        }
    
    def _get_current_time(self) -> float:
        """获取当前时间戳"""
        import time
        return time.time()
    
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            Dict[str, Any]: 健康状态
        """
        try:
            # 执行一个简单的测试搜索
            test_result = self.search("测试", "vector", "core", 1)
            
            return {
                'status': 'healthy',
                'services': {
                    'knowledge_searcher': 'ok',
                    'bm25_searcher': 'ok',
                    'hybrid_searcher': 'ok'
                },
                'test_search': test_result.get('status', 'unknown')
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
