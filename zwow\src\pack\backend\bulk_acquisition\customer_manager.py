"""
客户管理器
负责客户信息管理和获客策略
"""

import logging
import json
import csv
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass, asdict
from datetime import datetime
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.settings_loader import get_setting, get_absolute_path
    from src.pack.common_utils.database.app_db_manager import AppDBManager
except ImportError as e:
    print(f"导入模块失败: {e}")
    def get_setting(key, default=None): return default
    def get_absolute_path(path): return path
    class AppDBManager:
        def __init__(self): pass
        def initialize(self): return True

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class Customer:
    """客户信息数据类"""
    phone_number: str
    name: str = ""
    status: str = "potential"  # potential, contacted, interested, converted, blacklist
    source: str = ""  # 来源
    tags: List[str] = None
    notes: str = ""
    created_time: str = ""
    last_contact_time: str = ""
    contact_count: int = 0
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if not self.created_time:
            self.created_time = datetime.now().isoformat()

@dataclass
class MessageTemplate:
    """消息模板数据类"""
    id: str
    name: str
    content: str
    category: str = "general"
    tags: List[str] = None
    created_time: str = ""
    usage_count: int = 0
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if not self.created_time:
            self.created_time = datetime.now().isoformat()

class CustomerManager:
    """客户管理器类"""
    
    def __init__(self):
        """初始化客户管理器"""
        # 数据存储路径
        self.data_dir = get_absolute_path(get_setting("bulk_acquisition.data_dir", "data/customers"))
        self.customers_file = Path(self.data_dir) / "customers.json"
        self.templates_file = Path(self.data_dir) / "templates.json"
        self.blacklist_file = Path(self.data_dir) / "blacklist.json"
        
        # 确保目录存在
        Path(self.data_dir).mkdir(parents=True, exist_ok=True)
        
        # 数据缓存
        self.customers = {}  # phone_number -> Customer
        self.templates = {}  # template_id -> MessageTemplate
        self.blacklist = set()  # 黑名单电话号码
        
        # 数据库管理器
        self.db_manager = AppDBManager()
        self.db_manager.initialize()
        
        # 加载数据
        self._load_data()
        
        logger.info("客户管理器已初始化")
    
    def _load_data(self):
        """加载数据"""
        try:
            # 加载客户数据
            if self.customers_file.exists():
                with open(self.customers_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.customers = {
                        phone: Customer(**customer_data) 
                        for phone, customer_data in data.items()
                    }
                logger.info(f"加载了 {len(self.customers)} 个客户")
            
            # 加载模板数据
            if self.templates_file.exists():
                with open(self.templates_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.templates = {
                        template_id: MessageTemplate(**template_data)
                        for template_id, template_data in data.items()
                    }
                logger.info(f"加载了 {len(self.templates)} 个消息模板")
            
            # 加载黑名单
            if self.blacklist_file.exists():
                with open(self.blacklist_file, 'r', encoding='utf-8') as f:
                    self.blacklist = set(json.load(f))
                logger.info(f"加载了 {len(self.blacklist)} 个黑名单号码")
                
        except Exception as e:
            logger.error(f"加载数据时出错: {str(e)}")
    
    def _save_data(self):
        """保存数据"""
        try:
            # 保存客户数据
            customers_data = {
                phone: asdict(customer) 
                for phone, customer in self.customers.items()
            }
            with open(self.customers_file, 'w', encoding='utf-8') as f:
                json.dump(customers_data, f, ensure_ascii=False, indent=2)
            
            # 保存模板数据
            templates_data = {
                template_id: asdict(template)
                for template_id, template in self.templates.items()
            }
            with open(self.templates_file, 'w', encoding='utf-8') as f:
                json.dump(templates_data, f, ensure_ascii=False, indent=2)
            
            # 保存黑名单
            with open(self.blacklist_file, 'w', encoding='utf-8') as f:
                json.dump(list(self.blacklist), f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存数据时出错: {str(e)}")
    
    def add_customer(self, phone_number: str, name: str = "", source: str = "", 
                    tags: List[str] = None) -> bool:
        """
        添加客户
        
        Args:
            phone_number: 电话号码
            name: 客户姓名
            source: 来源
            tags: 标签列表
            
        Returns:
            bool: 是否添加成功
        """
        try:
            if phone_number in self.customers:
                logger.warning(f"客户已存在: {phone_number}")
                return False
            
            customer = Customer(
                phone_number=phone_number,
                name=name,
                source=source,
                tags=tags or []
            )
            
            self.customers[phone_number] = customer
            self._save_data()
            
            logger.info(f"添加客户成功: {phone_number}")
            return True
            
        except Exception as e:
            logger.error(f"添加客户时出错: {str(e)}")
            return False
    
    def update_customer(self, phone_number: str, **kwargs) -> bool:
        """
        更新客户信息
        
        Args:
            phone_number: 电话号码
            **kwargs: 要更新的字段
            
        Returns:
            bool: 是否更新成功
        """
        try:
            if phone_number not in self.customers:
                logger.warning(f"客户不存在: {phone_number}")
                return False
            
            customer = self.customers[phone_number]
            
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(customer, key):
                    setattr(customer, key, value)
            
            self._save_data()
            logger.info(f"更新客户成功: {phone_number}")
            return True
            
        except Exception as e:
            logger.error(f"更新客户时出错: {str(e)}")
            return False
    
    def get_customer(self, phone_number: str) -> Optional[Customer]:
        """
        获取客户信息
        
        Args:
            phone_number: 电话号码
            
        Returns:
            Optional[Customer]: 客户信息
        """
        return self.customers.get(phone_number)
    
    def get_customers_by_status(self, status: str) -> List[Customer]:
        """
        根据状态获取客户列表
        
        Args:
            status: 客户状态
            
        Returns:
            List[Customer]: 客户列表
        """
        return [customer for customer in self.customers.values() if customer.status == status]
    
    def get_customers_by_tag(self, tag: str) -> List[Customer]:
        """
        根据标签获取客户列表
        
        Args:
            tag: 标签
            
        Returns:
            List[Customer]: 客户列表
        """
        return [customer for customer in self.customers.values() if tag in customer.tags]
    
    def add_to_blacklist(self, phone_number: str) -> bool:
        """
        添加到黑名单
        
        Args:
            phone_number: 电话号码
            
        Returns:
            bool: 是否添加成功
        """
        try:
            self.blacklist.add(phone_number)
            
            # 如果是现有客户，更新状态
            if phone_number in self.customers:
                self.customers[phone_number].status = "blacklist"
            
            self._save_data()
            logger.info(f"添加到黑名单: {phone_number}")
            return True
            
        except Exception as e:
            logger.error(f"添加到黑名单时出错: {str(e)}")
            return False
    
    def remove_from_blacklist(self, phone_number: str) -> bool:
        """
        从黑名单移除
        
        Args:
            phone_number: 电话号码
            
        Returns:
            bool: 是否移除成功
        """
        try:
            if phone_number in self.blacklist:
                self.blacklist.remove(phone_number)
                
                # 如果是现有客户，更新状态
                if phone_number in self.customers:
                    self.customers[phone_number].status = "potential"
                
                self._save_data()
                logger.info(f"从黑名单移除: {phone_number}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"从黑名单移除时出错: {str(e)}")
            return False
    
    def is_blacklisted(self, phone_number: str) -> bool:
        """
        检查是否在黑名单中
        
        Args:
            phone_number: 电话号码
            
        Returns:
            bool: 是否在黑名单中
        """
        return phone_number in self.blacklist
    
    def filter_valid_numbers(self, phone_numbers: List[str]) -> List[str]:
        """
        过滤有效号码（排除黑名单）
        
        Args:
            phone_numbers: 电话号码列表
            
        Returns:
            List[str]: 有效号码列表
        """
        return [phone for phone in phone_numbers if not self.is_blacklisted(phone)]
    
    def add_message_template(self, name: str, content: str, category: str = "general", 
                           tags: List[str] = None) -> str:
        """
        添加消息模板
        
        Args:
            name: 模板名称
            content: 模板内容
            category: 模板分类
            tags: 标签列表
            
        Returns:
            str: 模板ID
        """
        try:
            import uuid
            template_id = str(uuid.uuid4())
            
            template = MessageTemplate(
                id=template_id,
                name=name,
                content=content,
                category=category,
                tags=tags or []
            )
            
            self.templates[template_id] = template
            self._save_data()
            
            logger.info(f"添加消息模板成功: {name}")
            return template_id
            
        except Exception as e:
            logger.error(f"添加消息模板时出错: {str(e)}")
            return ""
    
    def get_message_template(self, template_id: str) -> Optional[MessageTemplate]:
        """
        获取消息模板
        
        Args:
            template_id: 模板ID
            
        Returns:
            Optional[MessageTemplate]: 消息模板
        """
        return self.templates.get(template_id)
    
    def get_templates_by_category(self, category: str) -> List[MessageTemplate]:
        """
        根据分类获取模板列表
        
        Args:
            category: 模板分类
            
        Returns:
            List[MessageTemplate]: 模板列表
        """
        return [template for template in self.templates.values() if template.category == category]
    
    def import_customers_from_csv(self, csv_file_path: str) -> int:
        """
        从CSV文件导入客户
        
        Args:
            csv_file_path: CSV文件路径
            
        Returns:
            int: 导入的客户数量
        """
        try:
            imported_count = 0
            
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    phone_number = row.get('phone_number', '').strip()
                    if phone_number and phone_number not in self.customers:
                        customer = Customer(
                            phone_number=phone_number,
                            name=row.get('name', '').strip(),
                            source=row.get('source', 'csv_import').strip(),
                            tags=row.get('tags', '').split(',') if row.get('tags') else []
                        )
                        self.customers[phone_number] = customer
                        imported_count += 1
            
            self._save_data()
            logger.info(f"从CSV导入了 {imported_count} 个客户")
            return imported_count
            
        except Exception as e:
            logger.error(f"从CSV导入客户时出错: {str(e)}")
            return 0
    
    def export_customers_to_csv(self, csv_file_path: str, status_filter: str = None) -> bool:
        """
        导出客户到CSV文件
        
        Args:
            csv_file_path: CSV文件路径
            status_filter: 状态过滤器
            
        Returns:
            bool: 是否导出成功
        """
        try:
            customers_to_export = self.customers.values()
            
            if status_filter:
                customers_to_export = [c for c in customers_to_export if c.status == status_filter]
            
            with open(csv_file_path, 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['phone_number', 'name', 'status', 'source', 'tags', 'notes', 
                            'created_time', 'last_contact_time', 'contact_count']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                
                writer.writeheader()
                for customer in customers_to_export:
                    row = asdict(customer)
                    row['tags'] = ','.join(customer.tags)
                    writer.writerow(row)
            
            logger.info(f"导出了 {len(list(customers_to_export))} 个客户到CSV")
            return True
            
        except Exception as e:
            logger.error(f"导出客户到CSV时出错: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取客户统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        total_customers = len(self.customers)
        status_counts = {}
        
        for customer in self.customers.values():
            status_counts[customer.status] = status_counts.get(customer.status, 0) + 1
        
        return {
            'total_customers': total_customers,
            'status_counts': status_counts,
            'blacklist_count': len(self.blacklist),
            'template_count': len(self.templates)
        }
