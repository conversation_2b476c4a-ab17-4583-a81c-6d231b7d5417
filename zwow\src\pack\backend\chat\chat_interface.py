"""
聊天服务抽象基类
定义了聊天平台应该实现的通用接口
"""

import abc
import time
import threading
import logging
from collections import defaultdict
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class ChatInterface(abc.ABC):
    """
    聊天服务抽象基类
    
    定义了聊天平台应该实现的通用接口，包括消息处理、缓冲聚合和服务生命周期管理。
    继承此类的平台特定实现需要重写抽象方法并实现平台特定的通信逻辑。
    """
    
    def __init__(self, store_name: str, aggregation_timeout: int = 10):
        """
        初始化聊天服务接口
        
        Args:
            store_name: 店铺名称
            aggregation_timeout: 消息聚合超时时间（秒），默认10秒
        """
        self.store_name = store_name
        self.is_running = False
        
        # 消息聚合相关变量
        self.message_buffer = defaultdict(list)  # 每个发送者的消息缓冲区
        self.last_message_time = {}  # 每个发送者的最后消息时间
        self.aggregation_timers = {}  # 每个发送者的消息聚合计时器对象
        self.last_sent_messages = {}  # 每个发送者收到的最后一条回复
        self.message_lock = threading.Lock()  # 消息处理锁
        self.AGGREGATION_TIMEOUT = aggregation_timeout
        
        # 初始化方法，用于子类实现平台特定的初始化
        self._platform_initialize()
    
    @abc.abstractmethod
    def _platform_initialize(self) -> None:
        """
        平台特定的初始化操作
        
        子类必须实现此方法以完成特定平台的初始化，例如设置API客户端、配置等
        """
        pass
    
    @abc.abstractmethod
    def start(self) -> bool:
        """
        启动聊天服务
        
        Returns:
            bool: 启动是否成功
        """
        pass
    
    @abc.abstractmethod
    def stop(self) -> bool:
        """
        停止聊天服务
        
        Returns:
            bool: 停止是否成功
        """
        pass
    
    def buffer_message(self, message: Dict[str, Any]) -> None:
        """
        将接收到的消息放入缓冲区并设置计时器
        
        Args:
            message: 消息字典，应包含sender_id（发送者ID）和content（消息内容）
        """
        sender_id = self._get_sender_id(message)
        message_content = self._get_message_content(message)
        
        if not sender_id or not message_content:
            logger.warning("收到无效消息")
            return
        
        current_time = time.time()
        
        with self.message_lock:
            # 添加消息到缓冲区
            self.message_buffer[sender_id].append(message)
            self.last_message_time[sender_id] = current_time
            
            # 取消现有的计时器（如果存在）
            if sender_id in self.aggregation_timers and self.aggregation_timers[sender_id] is not None:
                try:
                    # 尝试取消计时器
                    self.aggregation_timers[sender_id].cancel()
                    logger.info(f"已取消现有计时器，发送者: {sender_id}")
                except:
                    # 忽略错误，即使无法取消也会创建新的计时器
                    pass
            
            # 创建新的计时器
            timer_thread = threading.Timer(
                self.AGGREGATION_TIMEOUT, 
                self.process_buffered_messages, 
                args=(sender_id,)
            )
            timer_thread.daemon = True
            self.aggregation_timers[sender_id] = timer_thread
            timer_thread.start()
            
            logger.info(f"消息已加入缓冲区，发送者: {sender_id}，重置计时器，将在 {self.AGGREGATION_TIMEOUT} 秒后处理（如无新消息）")
    
    def process_buffered_messages(self, sender_id: str) -> None:
        """
        处理缓冲区中的消息
        
        Args:
            sender_id: 发送者ID
        """
        with self.message_lock:
            # 检查是否有消息需要处理
            if sender_id not in self.message_buffer or not self.message_buffer[sender_id]:
                logger.info(f"发送者 {sender_id} 的缓冲区为空，跳过处理")
                return
            
            # 获取缓冲消息
            buffered_messages = self.message_buffer[sender_id]
            
            # 合并消息内容（由子类实现具体逻辑）
            combined_message = self._combine_messages(buffered_messages)
            
            # 清空缓冲区
            self.message_buffer[sender_id] = []
            self.aggregation_timers[sender_id] = None
        
        logger.info(f"处理来自 {sender_id} 的聚合消息，共 {len(buffered_messages)} 条消息")
        
        # 创建聚合后的消息
        aggregated_message = self._create_aggregated_message(sender_id, combined_message)
        
        # 处理聚合后的消息
        self.process_message(aggregated_message)
    
    @abc.abstractmethod
    def _get_sender_id(self, message: Dict[str, Any]) -> str:
        """
        从消息中提取发送者ID
        
        Args:
            message: 平台特定的消息对象
            
        Returns:
            str: 发送者ID
        """
        pass
    
    @abc.abstractmethod
    def _get_message_content(self, message: Dict[str, Any]) -> str:
        """
        从消息中提取内容
        
        Args:
            message: 平台特定的消息对象
            
        Returns:
            str: 消息内容
        """
        pass
    
    @abc.abstractmethod
    def _combine_messages(self, messages: List[Dict[str, Any]]) -> str:
        """
        合并多条消息内容
        
        Args:
            messages: 消息列表
            
        Returns:
            str: 合并后的消息内容
        """
        pass
    
    @abc.abstractmethod
    def _create_aggregated_message(self, sender_id: str, content: str) -> Dict[str, Any]:
        """
        创建聚合后的消息对象
        
        Args:
            sender_id: 发送者ID
            content: 合并后的消息内容
            
        Returns:
            Dict[str, Any]: 聚合后的消息对象
        """
        pass
    
    @abc.abstractmethod
    def process_message(self, message: Dict[str, Any]) -> None:
        """
        处理消息并生成回复
        
        Args:
            message: 消息对象，可能是单条消息或聚合后的消息
        """
        pass
    
    @abc.abstractmethod
    def send_reply(self, recipient_id: str, message: str) -> None:
        """
        发送回复消息
        
        Args:
            recipient_id: 接收者ID
            message: 回复内容
        """
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取服务状态
        
        Returns:
            Dict[str, Any]: 服务状态信息
        """
        return {
            "store_name": self.store_name,
            "is_running": self.is_running,
            "buffered_senders": list(self.message_buffer.keys()),
            "active_timers": len([t for t in self.aggregation_timers.values() if t is not None])
        }
    
    def clear_buffer(self, sender_id: str = None) -> None:
        """
        清空消息缓冲区
        
        Args:
            sender_id: 指定发送者ID，如果为None则清空所有缓冲区
        """
        with self.message_lock:
            if sender_id:
                if sender_id in self.message_buffer:
                    self.message_buffer[sender_id] = []
                if sender_id in self.aggregation_timers and self.aggregation_timers[sender_id]:
                    self.aggregation_timers[sender_id].cancel()
                    self.aggregation_timers[sender_id] = None
                logger.info(f"已清空发送者 {sender_id} 的缓冲区")
            else:
                # 取消所有计时器
                for timer in self.aggregation_timers.values():
                    if timer:
                        timer.cancel()
                
                # 清空所有缓冲区
                self.message_buffer.clear()
                self.aggregation_timers.clear()
                self.last_message_time.clear()
                logger.info("已清空所有缓冲区")
    
    def force_process_buffer(self, sender_id: str) -> None:
        """
        强制处理指定发送者的缓冲区
        
        Args:
            sender_id: 发送者ID
        """
        if sender_id in self.aggregation_timers and self.aggregation_timers[sender_id]:
            self.aggregation_timers[sender_id].cancel()
        
        self.process_buffered_messages(sender_id)
