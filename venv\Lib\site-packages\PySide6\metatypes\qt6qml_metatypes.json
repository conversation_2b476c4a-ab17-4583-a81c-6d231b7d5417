[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "TypeNotAvailable"}, {"name": "QML.AddedInVersion", "value": "527"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "Type not available."}], "className": "QQmlTypeNotAvailable", "lineNumber": 136, "object": true, "qualifiedClassName": "QQmlTypeNotAvailable", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqml.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}], "className": "QQmlLoggingCategoryBase", "lineNumber": 27, "object": true, "qualifiedClassName": "QQmlLoggingCategoryBase", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlloggingcategorybase_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "void"}, {"name": "QML.Extended", "value": "undefined"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "void"}], "className": "QQmlVoidForeign", "gadget": true, "lineNumber": 88, "qualifiedClassName": "QQmlVoidForeign"}, {"classInfos": [{"name": "QML.Element", "value": "var"}, {"name": "QML.Element", "value": "variant"}, {"name": "QML.Foreign", "value": "Q<PERSON><PERSON><PERSON>"}, {"name": "QML.Extended", "value": "QQmlVarForeign"}], "className": "QQmlVarForeign", "gadget": true, "lineNumber": 99, "qualifiedClassName": "QQmlVarForeign"}, {"classInfos": [{"name": "QML.Element", "value": "QtObject"}, {"name": "QML.Extended", "value": "Object"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "QObject"}, {"name": "QML.Root", "value": "true"}], "className": "QQmlQtObjectForeign", "gadget": true, "lineNumber": 108, "qualifiedClassName": "QQmlQtObjectForeign"}, {"classInfos": [{"name": "QML.Element", "value": "int"}, {"name": "QML.Extended", "value": "Number"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "int"}], "className": "QQmlIntForeign", "gadget": true, "lineNumber": 117, "qualifiedClassName": "QQmlIntForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "qint32"}, {"name": "QML.Using", "value": "int"}], "className": "QQmlQint32Foreign", "gadget": true, "lineNumber": 129, "qualifiedClassName": "QQmlQint32Foreign"}, {"classInfos": [{"name": "QML.Foreign", "value": "int32_t"}, {"name": "QML.Using", "value": "int"}], "className": "QQmlInt32TForeign", "gadget": true, "lineNumber": 136, "qualifiedClassName": "QQmlInt32TForeign"}, {"classInfos": [{"name": "QML.Element", "value": "real"}, {"name": "QML.Element", "value": "double"}, {"name": "QML.Extended", "value": "Number"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "double"}], "className": "QQmlDoubleForeign", "gadget": true, "lineNumber": 143, "qualifiedClassName": "QQmlDoubleForeign"}, {"classInfos": [{"name": "QML.Element", "value": "string"}, {"name": "QML.Extended", "value": "String"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "QString"}], "className": "QQmlStringForeign", "gadget": true, "lineNumber": 152, "qualifiedClassName": "QQmlStringForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Extended", "value": "String"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "QAnyStringView"}], "className": "QQmlAnyStringViewForeign", "gadget": true, "lineNumber": 160, "qualifiedClassName": "QQmlAnyStringViewForeign"}, {"classInfos": [{"name": "QML.Element", "value": "bool"}, {"name": "QML.Extended", "value": "Boolean"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "bool"}], "className": "QQmlBoolForeign", "gadget": true, "lineNumber": 168, "qualifiedClassName": "QQmlBoolForeign"}, {"classInfos": [{"name": "QML.Element", "value": "date"}, {"name": "QML.Extended", "value": "Date"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "QDateTime"}], "className": "QQmlDateForeign", "gadget": true, "lineNumber": 176, "qualifiedClassName": "QQmlDateForeign"}, {"classInfos": [{"name": "QML.Element", "value": "url"}, {"name": "QML.Extended", "value": "URL"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "QUrl"}], "className": "QQmlUrlForeign", "gadget": true, "lineNumber": 184, "qualifiedClassName": "QQmlUrlForeign"}, {"classInfos": [{"name": "QML.Element", "value": "regexp"}, {"name": "QML.Extended", "value": "RegExp"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "QRegularExpression"}], "className": "QQmlRegexpForeign", "gadget": true, "lineNumber": 193, "qualifiedClassName": "QQmlRegexpForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "std::nullptr_t"}, {"name": "QML.Extended", "value": "QQmlNullForeign"}], "className": "QQmlNullForeign", "gadget": true, "lineNumber": 202, "qualifiedClassName": "QQmlNullForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QVariantMap"}, {"name": "QML.Extended", "value": "Object"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}], "className": "QQmlQVariantMapForeign", "gadget": true, "lineNumber": 210, "qualifiedClassName": "QQmlQVariantMapForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QVariantHash"}, {"name": "QML.Extended", "value": "Object"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}], "className": "QQmlQVariantHashForeign", "gadget": true, "lineNumber": 218, "qualifiedClassName": "QQmlQVariantHashForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Extended", "value": "Number"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "qint8"}], "className": "QQmlQint8Foreign", "gadget": true, "lineNumber": 226, "qualifiedClassName": "QQmlQint8Foreign"}, {"classInfos": [{"name": "QML.Foreign", "value": "int8_t"}, {"name": "QML.Using", "value": "qint8"}], "className": "QQmlInt8TForeign", "gadget": true, "lineNumber": 234, "qualifiedClassName": "QQmlInt8TForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Extended", "value": "Number"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "quint8"}], "className": "QQmlQuint8Foreign", "gadget": true, "lineNumber": 241, "qualifiedClassName": "QQmlQuint8Foreign"}, {"classInfos": [{"name": "QML.Foreign", "value": "uint8_t"}, {"name": "QML.Using", "value": "quint8"}], "className": "QQmlUint8TForeign", "gadget": true, "lineNumber": 249, "qualifiedClassName": "QQmlUint8TForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "uchar"}, {"name": "QML.Using", "value": "quint8"}], "className": "QQmlUcharForeign", "gadget": true, "lineNumber": 256, "qualifiedClassName": "QQmlUcharForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "char"}, {"name": "QML.Using", "value": "quint8"}], "className": "QQmlCharForeign", "gadget": true, "lineNumber": 263, "qualifiedClassName": "QQmlCharForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Extended", "value": "Number"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "short"}], "className": "QQmlShortForeign", "gadget": true, "lineNumber": 276, "qualifiedClassName": "QQmlShortForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "qint16"}, {"name": "QML.Using", "value": "short"}], "className": "QQmlQint16Foreign", "gadget": true, "lineNumber": 284, "qualifiedClassName": "QQmlQint16Foreign"}, {"classInfos": [{"name": "QML.Foreign", "value": "int16_t"}, {"name": "QML.Using", "value": "short"}], "className": "QQmlInt16TForeign", "gadget": true, "lineNumber": 291, "qualifiedClassName": "QQmlInt16TForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Extended", "value": "Number"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "ushort"}], "className": "QQmlUshortForeign", "gadget": true, "lineNumber": 298, "qualifiedClassName": "QQmlUshortForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "quint16"}, {"name": "QML.Using", "value": "ushort"}], "className": "QQmlQuint16Foreign", "gadget": true, "lineNumber": 306, "qualifiedClassName": "QQmlQuint16Foreign"}, {"classInfos": [{"name": "QML.Foreign", "value": "uint16_t"}, {"name": "QML.Using", "value": "ushort"}], "className": "QQmlUint16TForeign", "gadget": true, "lineNumber": 313, "qualifiedClassName": "QQmlUint16TForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Extended", "value": "Number"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "uint"}], "className": "QQmlUintForeign", "gadget": true, "lineNumber": 320, "qualifiedClassName": "QQmlUintForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "quint32"}, {"name": "QML.Using", "value": "uint"}], "className": "QQmlQuint32Foreign", "gadget": true, "lineNumber": 328, "qualifiedClassName": "QQmlQuint32Foreign"}, {"classInfos": [{"name": "QML.Foreign", "value": "uint32_t"}, {"name": "QML.Using", "value": "uint"}], "className": "QQmlUint32TForeign", "gadget": true, "lineNumber": 335, "qualifiedClassName": "QQmlUint32TForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Extended", "value": "Number"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "qlonglong"}, {"name": "QML.PrimitiveAlias", "value": "qsizetype"}], "className": "QQmlQlonglongForeign", "gadget": true, "lineNumber": 342, "qualifiedClassName": "QQmlQlonglongForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "qint64"}, {"name": "QML.Using", "value": "qlonglong"}], "className": "QQmlQint64Foreign", "gadget": true, "lineNumber": 354, "qualifiedClassName": "QQmlQint64Foreign"}, {"classInfos": [{"name": "QML.Foreign", "value": "int64_t"}, {"name": "QML.Using", "value": "qlonglong"}], "className": "QQmlInt64TForeign", "gadget": true, "lineNumber": 361, "qualifiedClassName": "QQmlInt64TForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "long"}, {"name": "QML.Using", "value": "int"}], "className": "QQmlLongForeign", "gadget": true, "lineNumber": 368, "qualifiedClassName": "QQmlLongForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Extended", "value": "Number"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "qulonglong"}], "className": "QQmlQulonglongForeign", "gadget": true, "lineNumber": 381, "qualifiedClassName": "QQmlQulonglongForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "quint64"}, {"name": "QML.Using", "value": "qulonglong"}], "className": "QQmlQuint64Foreign", "gadget": true, "lineNumber": 389, "qualifiedClassName": "QQmlQuint64Foreign"}, {"classInfos": [{"name": "QML.Foreign", "value": "uint64_t"}, {"name": "QML.Using", "value": "qulonglong"}], "className": "QQmlUint64TForeign", "gadget": true, "lineNumber": 396, "qualifiedClassName": "QQmlUint64TForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "<PERSON><PERSON>"}, {"name": "QML.Using", "value": "uint"}], "className": "QQmlUlongForeign", "gadget": true, "lineNumber": 403, "qualifiedClassName": "QQmlUlongForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Extended", "value": "Number"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "float"}], "className": "QQmlFloatForeign", "gadget": true, "lineNumber": 416, "qualifiedClassName": "QQmlFloatForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "qreal"}, {"name": "QML.Using", "value": "double"}], "className": "QQmlQRealForeign", "gadget": true, "lineNumber": 424, "qualifiedClassName": "QQmlQRealForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QChar"}, {"name": "QML.Extended", "value": "String"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}], "className": "QQmlQCharForeign", "gadget": true, "lineNumber": 437, "qualifiedClassName": "QQmlQCharForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QDate"}, {"name": "QML.Extended", "value": "Date"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}], "className": "QQmlQDateForeign", "gadget": true, "lineNumber": 445, "qualifiedClassName": "QQmlQDateForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QTime"}, {"name": "QML.Extended", "value": "Date"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}], "className": "QQmlQTimeForeign", "gadget": true, "lineNumber": 453, "qualifiedClassName": "QQmlQTimeForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Extended", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}, {"name": "QML.Foreign", "value": "QByteArray"}], "className": "QQmlQByteArrayForeign", "gadget": true, "lineNumber": 461, "qualifiedClassName": "QQmlQByteArrayForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QByteArrayList"}, {"name": "QML.Sequence", "value": "QByteArray"}], "className": "QQmlQByteArrayListForeign", "gadget": true, "lineNumber": 469, "qualifiedClassName": "QQmlQByteArrayListForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QStringList"}, {"name": "QML.Sequence", "value": "QString"}], "className": "QQmlQStringListForeign", "gadget": true, "lineNumber": 477, "qualifiedClassName": "QQmlQStringListForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QVariantList"}, {"name": "QML.Sequence", "value": "Q<PERSON><PERSON><PERSON>"}], "className": "QQmlQVariantListForeign", "gadget": true, "lineNumber": 485, "qualifiedClassName": "QQmlQVariantListForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QObjectList"}, {"name": "QML.Sequence", "value": "QObject*"}], "className": "QQmlQObjectListForeign", "gadget": true, "lineNumber": 493, "qualifiedClassName": "QQmlQObjectListForeign"}, {"classInfos": [{"name": "QML.Foreign", "value": "QList<QObject*>"}, {"name": "QML.Using", "value": "QObjectList"}], "className": "QQmlQListQObjectForeign", "gadget": true, "lineNumber": 501, "qualifiedClassName": "QQmlQListQObjectForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QJSValue"}, {"name": "QML.Extended", "value": "QQmlQJSValueForeign"}], "className": "QQmlQJSValueForeign", "gadget": true, "lineNumber": 508, "qualifiedClassName": "QQmlQJSValueForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Component"}, {"name": "QML.Foreign", "value": "QQmlComponent"}, {"name": "QML.Attached", "value": "QQmlComponentAttached"}], "className": "QQmlComponentForeign", "gadget": true, "lineNumber": 516, "qualifiedClassName": "QQmlComponentForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QQmlScriptString"}], "className": "QQmlScriptStringForeign", "gadget": true, "lineNumber": 524, "qualifiedClassName": "QQmlScriptStringForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QQmlV4FunctionPtr"}, {"name": "QML.Extended", "value": "QQmlV4FunctionPtrForeign"}], "className": "QQmlV4FunctionPtrForeign", "gadget": true, "lineNumber": 531, "qualifiedClassName": "QQmlV4FunctionPtrForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QJsonObject"}, {"name": "QML.Extended", "value": "Object"}, {"name": "QML.ExtensionIsJavaScript", "value": "true"}], "className": "QQmlQJsonObjectForeign", "gadget": true, "lineNumber": 539, "qualifiedClassName": "QQmlQJsonObjectForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QJsonValue"}, {"name": "QML.Extended", "value": "QQmlQJsonValueForeign"}], "className": "QQmlQJsonValueForeign", "gadget": true, "lineNumber": 547, "qualifiedClassName": "QQmlQJsonValueForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QJsonArray"}, {"name": "QML.Sequence", "value": "QJsonValue"}], "className": "QQmlQJsonArrayForeign", "gadget": true, "lineNumber": 555, "qualifiedClassName": "QQmlQJsonArrayForeign"}], "inputFile": "qqmlbuiltins_p.h", "outputRevision": 69}, {"classes": [{"className": "QQmlAnimationTimer", "lineNumber": 165, "object": true, "qualifiedClassName": "QQmlAnimationTimer", "slots": [{"access": "public", "index": 0, "name": "startAnimations", "returnType": "void"}, {"access": "public", "index": 1, "name": "stopTimer", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractAnimationTimer"}]}], "inputFile": "qabstractanimationjob_p.h", "outputRevision": 69}, {"classes": [{"className": "QJSEngine", "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "uiLanguage", "notify": "uiLanguageChanged", "read": "uiLanguage", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setUiLanguage"}], "qualifiedClassName": "QJSEngine", "signals": [{"access": "public", "index": 0, "name": "uiLanguageChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qjsengine.h", "outputRevision": 69}, {"classes": [{"className": "QQmlAbstractProfilerAdapter", "lineNumber": 29, "object": true, "qualifiedClassName": "QQmlAbstractProfilerAdapter", "signals": [{"access": "public", "arguments": [{"name": "features", "type": "quint64"}], "index": 0, "name": "profilingEnabled", "returnType": "void"}, {"access": "public", "arguments": [{"name": "features", "type": "quint64"}], "index": 1, "name": "profiling<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 2, "name": "profilingDisabled", "returnType": "void"}, {"access": "public", "index": 3, "name": "profilingDisabled<PERSON><PERSON>e<PERSON>aiting", "returnType": "void"}, {"access": "public", "index": 4, "name": "dataRequested", "returnType": "void"}, {"access": "public", "arguments": [{"name": "timer", "type": "QElapsedTimer"}], "index": 5, "name": "referenceTimeKnown", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlProfilerDefinitions"}]}, {"className": "QQmlAbstractProfilerAdapterFactory", "lineNumber": 74, "object": true, "qualifiedClassName": "QQmlAbstractProfilerAdapterFactory", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlabstractprofileradapter_p.h", "outputRevision": 69}, {"classes": [{"className": "QQmlApplicationEngine", "lineNumber": 16, "object": true, "qualifiedClassName": "QQmlApplicationEngine", "signals": [{"access": "public", "arguments": [{"name": "object", "type": "QObject*"}, {"name": "url", "type": "QUrl"}], "index": 0, "name": "objectCreated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 1, "name": "objectCreationFailed", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 2, "name": "load", "returnType": "void"}, {"access": "public", "arguments": [{"name": "filePath", "type": "QString"}], "index": 3, "name": "load", "returnType": "void"}, {"access": "public", "arguments": [{"name": "uri", "type": "QAnyStringView"}, {"name": "typeName", "type": "QAnyStringView"}], "index": 4, "name": "loadFromModule", "returnType": "void"}, {"access": "public", "arguments": [{"name": "initialProperties", "type": "QVariantMap"}], "index": 5, "name": "setInitialProperties", "returnType": "void"}, {"access": "public", "arguments": [{"name": "extraFileSelectors", "type": "QStringList"}], "index": 6, "name": "setExtraFileSelectors", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QByteArray"}, {"name": "url", "type": "QUrl"}], "index": 7, "name": "loadData", "returnType": "void"}, {"access": "public", "arguments": [{"name": "data", "type": "QByteArray"}], "index": 8, "isCloned": true, "name": "loadData", "returnType": "void"}, {"access": "private", "index": 9, "name": "_q_loadTranslations", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQmlEngine"}]}], "inputFile": "qqmlapplicationengine.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Qt"}, {"name": "QML.Singleton", "value": "true"}, {"name": "QML.Extended", "value": "Qt"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}, {"name": "QML.StrictArguments", "value": "true"}], "className": "QtObject", "enums": [{"isClass": false, "isFlag": false, "name": "LoadingMode", "values": ["Asynchronous", "Synchronous"]}], "lineNumber": 37, "methods": [{"access": "public", "arguments": [{"name": "url", "type": "QString"}, {"name": "callback", "type": "QJSValue"}], "index": 0, "isConst": true, "name": "include", "returnType": "QJSValue"}, {"access": "public", "arguments": [{"name": "url", "type": "QString"}], "index": 1, "isCloned": true, "isConst": true, "name": "include", "returnType": "QJSValue"}, {"access": "public", "arguments": [{"name": "value", "type": "QJSValue"}], "index": 2, "isConst": true, "name": "isQtObject", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 3, "isConst": true, "name": "color", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "r", "type": "double"}, {"name": "g", "type": "double"}, {"name": "b", "type": "double"}, {"name": "a", "type": "double"}], "index": 4, "isConst": true, "name": "rgba", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "r", "type": "double"}, {"name": "g", "type": "double"}, {"name": "b", "type": "double"}], "index": 5, "isCloned": true, "isConst": true, "name": "rgba", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "h", "type": "double"}, {"name": "s", "type": "double"}, {"name": "l", "type": "double"}, {"name": "a", "type": "double"}], "index": 6, "isConst": true, "name": "hsla", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "h", "type": "double"}, {"name": "s", "type": "double"}, {"name": "l", "type": "double"}], "index": 7, "isCloned": true, "isConst": true, "name": "hsla", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "h", "type": "double"}, {"name": "s", "type": "double"}, {"name": "v", "type": "double"}, {"name": "a", "type": "double"}], "index": 8, "isConst": true, "name": "hsva", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "h", "type": "double"}, {"name": "s", "type": "double"}, {"name": "v", "type": "double"}], "index": 9, "isCloned": true, "isConst": true, "name": "hsva", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "lhs", "type": "Q<PERSON><PERSON><PERSON>"}, {"name": "rhs", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 10, "isConst": true, "name": "colorEqual", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "x", "type": "double"}, {"name": "y", "type": "double"}, {"name": "width", "type": "double"}, {"name": "height", "type": "double"}], "index": 11, "isConst": true, "name": "rect", "returnType": "QRectF"}, {"access": "public", "arguments": [{"name": "x", "type": "double"}, {"name": "y", "type": "double"}], "index": 12, "isConst": true, "name": "point", "returnType": "QPointF"}, {"access": "public", "arguments": [{"name": "width", "type": "double"}, {"name": "height", "type": "double"}], "index": 13, "isConst": true, "name": "size", "returnType": "QSizeF"}, {"access": "public", "arguments": [{"name": "x", "type": "double"}, {"name": "y", "type": "double"}], "index": 14, "isConst": true, "name": "vector2d", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "x", "type": "double"}, {"name": "y", "type": "double"}, {"name": "z", "type": "double"}], "index": 15, "isConst": true, "name": "vector3d", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "x", "type": "double"}, {"name": "y", "type": "double"}, {"name": "z", "type": "double"}, {"name": "w", "type": "double"}], "index": 16, "isConst": true, "name": "vector4d", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "scalar", "type": "double"}, {"name": "x", "type": "double"}, {"name": "y", "type": "double"}, {"name": "z", "type": "double"}], "index": 17, "isConst": true, "name": "quaternion", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "index": 18, "isConst": true, "name": "matrix4x4", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "m11", "type": "double"}, {"name": "m12", "type": "double"}, {"name": "m13", "type": "double"}, {"name": "m14", "type": "double"}, {"name": "m21", "type": "double"}, {"name": "m22", "type": "double"}, {"name": "m23", "type": "double"}, {"name": "m24", "type": "double"}, {"name": "m31", "type": "double"}, {"name": "m32", "type": "double"}, {"name": "m33", "type": "double"}, {"name": "m34", "type": "double"}, {"name": "m41", "type": "double"}, {"name": "m42", "type": "double"}, {"name": "m43", "type": "double"}, {"name": "m44", "type": "double"}], "index": 19, "isConst": true, "name": "matrix4x4", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "value", "type": "QJSValue"}], "index": 20, "isConst": true, "name": "matrix4x4", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "color", "type": "QJSValue"}, {"name": "factor", "type": "double"}], "index": 21, "isConst": true, "name": "lighter", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "color", "type": "QJSValue"}], "index": 22, "isCloned": true, "isConst": true, "name": "lighter", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "color", "type": "QJSValue"}, {"name": "factor", "type": "double"}], "index": 23, "isConst": true, "name": "darker", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "color", "type": "QJSValue"}], "index": 24, "isCloned": true, "isConst": true, "name": "darker", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "baseColor", "type": "QJSValue"}, {"name": "value", "type": "double"}], "index": 25, "isConst": true, "name": "alpha", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "baseColor", "type": "QJSValue"}, {"name": "tintColor", "type": "QJSValue"}], "index": 26, "isConst": true, "name": "tint", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "date", "type": "QDate"}, {"name": "format", "type": "QString"}], "index": 27, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "dateTime", "type": "QDateTime"}, {"name": "format", "type": "QString"}], "index": 28, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "string", "type": "QString"}, {"name": "format", "type": "QString"}], "index": 29, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "date", "type": "QDate"}, {"name": "format", "type": "Qt::DateFormat"}], "index": 30, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "dateTime", "type": "QDateTime"}, {"name": "format", "type": "Qt::DateFormat"}], "index": 31, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "string", "type": "QString"}, {"name": "format", "type": "Qt::DateFormat"}], "index": 32, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "time", "type": "QTime"}, {"name": "format", "type": "QString"}], "index": 33, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "dateTime", "type": "QDateTime"}, {"name": "format", "type": "QString"}], "index": 34, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "time", "type": "QString"}, {"name": "format", "type": "QString"}], "index": 35, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "time", "type": "QTime"}, {"name": "format", "type": "Qt::DateFormat"}], "index": 36, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "dateTime", "type": "QDateTime"}, {"name": "format", "type": "Qt::DateFormat"}], "index": 37, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "time", "type": "QString"}, {"name": "format", "type": "Qt::DateFormat"}], "index": 38, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "date", "type": "QDateTime"}, {"name": "format", "type": "QString"}], "index": 39, "isConst": true, "name": "formatDateTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "string", "type": "QString"}, {"name": "format", "type": "QString"}], "index": 40, "isConst": true, "name": "formatDateTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "date", "type": "QDateTime"}, {"name": "format", "type": "Qt::DateFormat"}], "index": 41, "isConst": true, "name": "formatDateTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "string", "type": "QString"}, {"name": "format", "type": "Qt::DateFormat"}], "index": 42, "isConst": true, "name": "formatDateTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "date", "type": "QDate"}, {"name": "locale", "type": "QLocale"}, {"name": "formatType", "type": "QLocale::FormatType"}], "index": 43, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "date", "type": "QDate"}, {"name": "locale", "type": "QLocale"}], "index": 44, "isCloned": true, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "date", "type": "QDate"}], "index": 45, "isCloned": true, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "dateTime", "type": "QDateTime"}, {"name": "locale", "type": "QLocale"}, {"name": "formatType", "type": "QLocale::FormatType"}], "index": 46, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "dateTime", "type": "QDateTime"}, {"name": "locale", "type": "QLocale"}], "index": 47, "isCloned": true, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "dateTime", "type": "QDateTime"}], "index": 48, "isCloned": true, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "string", "type": "QString"}, {"name": "locale", "type": "QLocale"}, {"name": "formatType", "type": "QLocale::FormatType"}], "index": 49, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "string", "type": "QString"}, {"name": "locale", "type": "QLocale"}], "index": 50, "isCloned": true, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "string", "type": "QString"}], "index": 51, "isCloned": true, "isConst": true, "name": "formatDate", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "time", "type": "QTime"}, {"name": "locale", "type": "QLocale"}, {"name": "formatType", "type": "QLocale::FormatType"}], "index": 52, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "time", "type": "QTime"}, {"name": "locale", "type": "QLocale"}], "index": 53, "isCloned": true, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "time", "type": "QTime"}], "index": 54, "isCloned": true, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "dateTime", "type": "QDateTime"}, {"name": "locale", "type": "QLocale"}, {"name": "formatType", "type": "QLocale::FormatType"}], "index": 55, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "dateTime", "type": "QDateTime"}, {"name": "locale", "type": "QLocale"}], "index": 56, "isCloned": true, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "dateTime", "type": "QDateTime"}], "index": 57, "isCloned": true, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "time", "type": "QString"}, {"name": "locale", "type": "QLocale"}, {"name": "formatType", "type": "QLocale::FormatType"}], "index": 58, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "time", "type": "QString"}, {"name": "locale", "type": "QLocale"}], "index": 59, "isCloned": true, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "time", "type": "QString"}], "index": 60, "isCloned": true, "isConst": true, "name": "formatTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "date", "type": "QDateTime"}, {"name": "locale", "type": "QLocale"}, {"name": "formatType", "type": "QLocale::FormatType"}], "index": 61, "isConst": true, "name": "formatDateTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "date", "type": "QDateTime"}, {"name": "locale", "type": "QLocale"}], "index": 62, "isCloned": true, "isConst": true, "name": "formatDateTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "date", "type": "QDateTime"}], "index": 63, "isCloned": true, "isConst": true, "name": "formatDateTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "string", "type": "QString"}, {"name": "locale", "type": "QLocale"}, {"name": "formatType", "type": "QLocale::FormatType"}], "index": 64, "isConst": true, "name": "formatDateTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "string", "type": "QString"}, {"name": "locale", "type": "QLocale"}], "index": 65, "isCloned": true, "isConst": true, "name": "formatDateTime", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "string", "type": "QString"}], "index": 66, "isCloned": true, "isConst": true, "name": "formatDateTime", "returnType": "QString"}, {"access": "public", "index": 67, "isConst": true, "name": "locale", "returnType": "QLocale"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 68, "isConst": true, "name": "locale", "returnType": "QLocale"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 69, "isConst": true, "name": "url", "returnType": "QUrl"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 70, "isConst": true, "name": "resolvedUrl", "returnType": "QUrl"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "context", "type": "QObject*"}], "index": 71, "isConst": true, "name": "resolvedUrl", "returnType": "QUrl"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 72, "isConst": true, "name": "openUrlExternally", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "fontSpecifier", "type": "QJSValue"}], "index": 73, "isConst": true, "name": "font", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "index": 74, "isConst": true, "name": "fontFamilies", "returnType": "QStringList"}, {"access": "public", "arguments": [{"name": "data", "type": "QString"}], "index": 75, "isConst": true, "name": "md5", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "data", "type": "QString"}], "index": 76, "isConst": true, "name": "btoa", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "data", "type": "QString"}], "index": 77, "isConst": true, "name": "atob", "returnType": "QString"}, {"access": "public", "index": 78, "isConst": true, "name": "quit", "returnType": "void"}, {"access": "public", "arguments": [{"name": "retCode", "type": "int"}], "index": 79, "isConst": true, "name": "exit", "returnType": "void"}, {"access": "public", "arguments": [{"name": "qml", "type": "QString"}, {"name": "parent", "type": "QObject*"}, {"name": "url", "type": "QUrl"}], "index": 80, "isConst": true, "name": "createQmlObject", "returnType": "QObject*"}, {"access": "public", "arguments": [{"name": "qml", "type": "QString"}, {"name": "parent", "type": "QObject*"}], "index": 81, "isCloned": true, "isConst": true, "name": "createQmlObject", "returnType": "QObject*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "parent", "type": "QObject*"}], "index": 82, "isConst": true, "name": "createComponent", "returnType": "QQmlComponent*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "mode", "type": "QQmlComponent::CompilationMode"}, {"name": "parent", "type": "QObject*"}], "index": 83, "isConst": true, "name": "createComponent", "returnType": "QQmlComponent*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "mode", "type": "QQmlComponent::CompilationMode"}], "index": 84, "isCloned": true, "isConst": true, "name": "createComponent", "returnType": "QQmlComponent*"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 85, "isCloned": true, "isConst": true, "name": "createComponent", "returnType": "QQmlComponent*"}, {"access": "public", "arguments": [{"name": "moduleUri", "type": "QString"}, {"name": "typeName", "type": "QString"}, {"name": "parent", "type": "QObject*"}], "index": 86, "isConst": true, "name": "createComponent", "returnType": "QQmlComponent*"}, {"access": "public", "arguments": [{"name": "moduleUri", "type": "QString"}, {"name": "typeName", "type": "QString"}, {"name": "mode", "type": "QQmlComponent::CompilationMode"}, {"name": "parent", "type": "QObject*"}], "index": 87, "isConst": true, "name": "createComponent", "returnType": "QQmlComponent*"}, {"access": "public", "arguments": [{"name": "moduleUri", "type": "QString"}, {"name": "typeName", "type": "QString"}, {"name": "mode", "type": "QQmlComponent::CompilationMode"}], "index": 88, "isCloned": true, "isConst": true, "name": "createComponent", "returnType": "QQmlComponent*"}, {"access": "public", "arguments": [{"name": "moduleUri", "type": "QString"}, {"name": "typeName", "type": "QString"}], "index": 89, "isCloned": true, "isConst": true, "name": "createComponent", "returnType": "QQmlComponent*"}, {"access": "public", "arguments": [{"name": "function", "type": "QJSValue"}], "index": 90, "isConst": true, "name": "binding", "returnType": "QJSValue"}, {"access": "public", "arguments": [{"name": "args", "type": "QQmlV4FunctionPtr"}], "index": 91, "name": "callLater", "returnType": "void"}], "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "application", "read": "application", "required": false, "scriptable": true, "stored": true, "type": "QQmlApplication*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "platform", "read": "platform", "required": false, "scriptable": true, "stored": true, "type": "QQmlPlatform*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "inputMethod", "read": "inputMethod", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "styleHints", "read": "styleHints", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false}, {"bindable": "uiLanguageBindable", "constant": false, "designable": true, "final": false, "index": 4, "name": "uiLanguage", "read": "uiLanguage", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setUiLanguage"}], "qualifiedClassName": "QtObject", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlbuiltinfunctions_p.h", "outputRevision": 69}, {"classes": [{"className": "QQmlComponent", "enums": [{"isClass": false, "isFlag": false, "name": "CompilationMode", "values": ["PreferSynchronous", "Asynchronous"]}, {"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}], "lineNumber": 31, "methods": [{"access": "public", "index": 7, "isConst": true, "name": "errorString", "returnType": "QString"}, {"access": "protected", "arguments": [{"type": "QQmlV4FunctionPtr"}], "index": 8, "name": "createObject", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "parent", "type": "QObject*"}, {"name": "properties", "type": "QVariantMap"}], "index": 9, "name": "createObject", "returnType": "QObject*"}, {"access": "protected", "arguments": [{"name": "parent", "type": "QObject*"}], "index": 10, "isCloned": true, "name": "createObject", "returnType": "QObject*"}, {"access": "protected", "index": 11, "isCloned": true, "name": "createObject", "returnType": "QObject*"}, {"access": "protected", "arguments": [{"type": "QQmlV4FunctionPtr"}], "index": 12, "name": "incubateObject", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "progress", "notify": "progressChanged", "read": "progress", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "url", "read": "url", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false}], "qualifiedClassName": "QQmlComponent", "signals": [{"access": "public", "arguments": [{"type": "QQmlComponent::Status"}], "index": 0, "name": "statusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"type": "qreal"}], "index": 1, "name": "progressChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 2, "name": "loadUrl", "returnType": "void"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}, {"name": "mode", "type": "CompilationMode"}], "index": 3, "name": "loadUrl", "returnType": "void"}, {"access": "public", "arguments": [{"name": "uri", "type": "QAnyStringView"}, {"name": "typeName", "type": "QAnyStringView"}, {"name": "mode", "type": "QQmlComponent::CompilationMode"}], "index": 4, "name": "loadFromModule", "returnType": "void"}, {"access": "public", "arguments": [{"name": "uri", "type": "QAnyStringView"}, {"name": "typeName", "type": "QAnyStringView"}], "index": 5, "isCloned": true, "name": "loadFromModule", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QByteArray"}, {"name": "baseUrl", "type": "QUrl"}], "index": 6, "name": "setData", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlcomponent.h", "outputRevision": 69}, {"classes": [{"className": "QQmlComponentAttached", "lineNumber": 27, "object": true, "qualifiedClassName": "QQmlComponentAttached", "signals": [{"access": "public", "index": 0, "name": "completed", "returnType": "void"}, {"access": "public", "index": 1, "name": "destruction", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlcomponentattached_p.h", "outputRevision": 69}, {"classes": [{"className": "QQmlContext", "lineNumber": 24, "object": true, "qualifiedClassName": "QQmlContext", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlcontext.h", "outputRevision": 69}, {"classes": [{"className": "QQmlDebugConnector", "lineNumber": 53, "object": true, "qualifiedClassName": "QQmlDebugConnector", "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QQmlDebugConnectorFactory", "lineNumber": 90, "object": true, "qualifiedClassName": "QQmlDebugConnectorFactory", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmldebugconnector_p.h", "outputRevision": 69}, {"classes": [{"className": "QQmlDebugServer", "lineNumber": 25, "object": true, "qualifiedClassName": "QQmlDebugServer", "superClasses": [{"access": "public", "name": "QQmlDebugConnector"}]}], "inputFile": "qqmldebugserver_p.h", "outputRevision": 69}, {"classes": [{"className": "QQmlDebugServerConnection", "lineNumber": 24, "object": true, "qualifiedClassName": "QQmlDebugServerConnection", "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QQmlDebugServerConnectionFactory", "lineNumber": 40, "object": true, "qualifiedClassName": "QQmlDebugServerConnectionFactory", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmldebugserverconnection_p.h", "outputRevision": 69}, {"classes": [{"className": "QQmlDebugService", "lineNumber": 30, "object": true, "qualifiedClassName": "QQmlDebugService", "signals": [{"access": "public", "arguments": [{"type": "QJSEngine*"}], "index": 0, "name": "attachedToEngine", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QJSEngine*"}], "index": 1, "name": "detachedFromEngine", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "message", "type": "QByteArray"}], "index": 2, "name": "messageToClient", "returnType": "void"}, {"access": "public", "arguments": [{"name": "name", "type": "QString"}, {"name": "messages", "type": "QList<QByteArray>"}], "index": 3, "name": "messagesToClient", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmldebugservice_p.h", "outputRevision": 69}, {"classes": [{"className": "QQmlDebugServiceFactory", "lineNumber": 22, "object": true, "qualifiedClassName": "QQmlDebugServiceFactory", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmldebugservicefactory_p.h", "outputRevision": 69}, {"classes": [{"className": "QV4DebugService", "lineNumber": 81, "object": true, "qualifiedClassName": "QV4DebugService", "superClasses": [{"access": "public", "name": "QQmlDebugService"}]}, {"className": "QQmlProfilerService", "lineNumber": 99, "object": true, "qualifiedClassName": "QQmlProfilerService", "superClasses": [{"access": "public", "name": "QQmlDebugService"}]}, {"className": "QQmlEngineDebugService", "lineNumber": 123, "object": true, "qualifiedClassName": "QQmlEngineDebugService", "superClasses": [{"access": "public", "name": "QQmlDebugService"}]}, {"className": "QQmlDebugTranslationService", "lineNumber": 163, "object": true, "qualifiedClassName": "QQmlDebugTranslationService", "superClasses": [{"access": "public", "name": "QQmlDebugService"}]}, {"className": "QQmlInspectorService", "lineNumber": 181, "object": true, "qualifiedClassName": "QQmlInspectorService", "superClasses": [{"access": "public", "name": "QQmlDebugService"}]}, {"className": "QDebugMessageService", "lineNumber": 200, "object": true, "qualifiedClassName": "QDebugMessageService", "superClasses": [{"access": "public", "name": "QQmlDebugService"}]}, {"className": "QQmlEngineControlService", "lineNumber": 217, "object": true, "qualifiedClassName": "QQmlEngineControlService", "superClasses": [{"access": "public", "name": "QQmlDebugService"}]}, {"className": "QQmlNativeDebugService", "lineNumber": 233, "object": true, "qualifiedClassName": "QQmlNativeDebugService", "superClasses": [{"access": "public", "name": "QQmlDebugService"}]}], "inputFile": "qqmldebugserviceinterfaces_p.h", "outputRevision": 69}, {"classes": [{"className": "QQmlDelayedCallQueue", "lineNumber": 27, "object": true, "qualifiedClassName": "QQmlDelayedCallQueue", "slots": [{"access": "public", "index": 0, "name": "ticked", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmldelayedcallqueue_p.h", "outputRevision": 69}, {"classes": [{"className": "QQmlImageProviderBase", "lineNumber": 17, "object": true, "qualifiedClassName": "QQmlImageProviderBase", "superClasses": [{"access": "public", "name": "QObject"}]}, {"className": "QQmlEngine", "lineNumber": 56, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "offlineStoragePath", "notify": "offlineStoragePathChanged", "read": "offlineStoragePath", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setOfflineStoragePath"}], "qualifiedClassName": "QQmlEngine", "signals": [{"access": "public", "index": 0, "name": "offlineStoragePathChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "quit", "returnType": "void"}, {"access": "public", "arguments": [{"name": "retCode", "type": "int"}], "index": 2, "name": "exit", "returnType": "void"}, {"access": "public", "arguments": [{"name": "warnings", "type": "QList<QQmlError>"}], "index": 3, "name": "warnings", "returnType": "void"}], "slots": [{"access": "public", "index": 4, "name": "retranslate", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QJSEngine"}]}], "inputFile": "qqmlengine.h", "outputRevision": 69}, {"classes": [{"className": "QQmlExpression", "lineNumber": 21, "object": true, "qualifiedClassName": "QQmlExpression", "signals": [{"access": "public", "index": 0, "name": "valueChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlexpression.h", "outputRevision": 69}, {"classes": [{"className": "QQmlExtensionPlugin", "interfaces": [[{"className": "QQmlExtensionInterface", "id": "\"org.qt-project.Qt.QQmlExtensionInterface/1.0\""}], [{"className": "QQmlTypesExtensionInterface", "id": "\"org.qt-project.Qt.QQmlTypesExtensionInterface/1.0\""}]], "lineNumber": 25, "object": true, "qualifiedClassName": "QQmlExtensionPlugin", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlExtensionInterface"}]}, {"className": "QQmlEngineExtensionPlugin", "interfaces": [[{"className": "QQmlEngineExtensionInterface", "id": "\"org.qt-project.Qt.QQmlEngineExtensionInterface\""}]], "lineNumber": 50, "object": true, "qualifiedClassName": "QQmlEngineExtensionPlugin", "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlEngineExtensionInterface"}]}], "inputFile": "qqmlextensionplugin.h", "outputRevision": 69}, {"classes": [{"className": "QQmlFileSelector", "lineNumber": 16, "object": true, "qualifiedClassName": "QQmlFileSelector", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlfileselector.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}], "className": "QQmlApplication", "lineNumber": 265, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "arguments", "read": "args", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "version", "notify": "versionChanged", "read": "version", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setVersion"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "organization", "notify": "organizationChanged", "read": "organization", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setOrganization"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "domain", "notify": "domainChanged", "read": "domain", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setDomain"}], "qualifiedClassName": "QQmlApplication", "signals": [{"access": "public", "index": 0, "name": "aboutToQuit", "returnType": "void"}, {"access": "public", "index": 1, "name": "nameChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "versionChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "organizationChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "domainChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "arg", "type": "QString"}], "index": 5, "name": "setName", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QString"}], "index": 6, "name": "setVersion", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QString"}], "index": 7, "name": "setOrganization", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QString"}], "index": 8, "name": "setDomain", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlglobal_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}], "className": "QQmlLocale", "enums": [{"isClass": false, "isFlag": false, "name": "DayOfWeek", "values": ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]}], "gadget": true, "lineNumber": 60, "qualifiedClassName": "QQmlLocale", "superClasses": [{"access": "public", "name": "QLocale"}]}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QList<QQmlLocale::DayOfWeek>"}, {"name": "QML.Sequence", "value": "QQmlLocale::DayOfWeek"}], "className": "DayOfWeekList", "gadget": true, "lineNumber": 85, "qualifiedClassName": "DayOfWeekList"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QLocale"}, {"name": "QML.Extended", "value": "QQmlLocaleValueType"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "construct"}], "className": "QQmlLocaleValueType", "constructors": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 0, "name": "QQmlLocaleValueType", "returnType": ""}], "gadget": true, "lineNumber": 93, "methods": [{"access": "public", "arguments": [{"name": "format", "type": "QLocale::CurrencySymbolFormat"}], "index": 0, "isConst": true, "name": "currencySymbol", "returnType": "QString"}, {"access": "public", "index": 1, "isCloned": true, "isConst": true, "name": "currencySymbol", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "format", "type": "QLocale::FormatType"}], "index": 2, "isConst": true, "name": "dateTimeFormat", "returnType": "QString"}, {"access": "public", "index": 3, "isCloned": true, "isConst": true, "name": "dateTimeFormat", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "format", "type": "QLocale::FormatType"}], "index": 4, "isConst": true, "name": "timeFormat", "returnType": "QString"}, {"access": "public", "index": 5, "isCloned": true, "isConst": true, "name": "timeFormat", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "format", "type": "QLocale::FormatType"}], "index": 6, "isConst": true, "name": "dateFormat", "returnType": "QString"}, {"access": "public", "index": 7, "isCloned": true, "isConst": true, "name": "dateFormat", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "format", "type": "QLocale::FormatType"}], "index": 8, "isConst": true, "name": "monthName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 9, "isCloned": true, "isConst": true, "name": "monthName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "format", "type": "QLocale::FormatType"}], "index": 10, "isConst": true, "name": "standaloneMonthName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 11, "isCloned": true, "isConst": true, "name": "standaloneMonthName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "format", "type": "QLocale::FormatType"}], "index": 12, "isConst": true, "name": "day<PERSON><PERSON>", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 13, "isCloned": true, "isConst": true, "name": "day<PERSON><PERSON>", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "format", "type": "QLocale::FormatType"}], "index": 14, "isConst": true, "name": "standaloneDayName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 15, "isCloned": true, "isConst": true, "name": "standaloneDayName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "args", "type": "QQmlV4FunctionPtr"}], "index": 16, "isConst": true, "name": "formattedDataSize", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bytes", "type": "double"}, {"name": "precision", "type": "int"}, {"name": "format", "type": "QLocale::DataSizeFormats"}], "index": 17, "isConst": true, "name": "formattedDataSize", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "bytes", "type": "double"}, {"name": "precision", "type": "int"}], "index": 18, "isCloned": true, "isConst": true, "name": "formattedDataSize", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "bytes", "type": "double"}], "index": 19, "isCloned": true, "isConst": true, "name": "formattedDataSize", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "args", "type": "QQmlV4FunctionPtr"}], "index": 20, "isConst": true, "name": "toString", "returnType": "void"}, {"access": "public", "index": 21, "isConst": true, "name": "toString", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "i", "type": "int"}], "index": 22, "isConst": true, "name": "toString", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "f", "type": "double"}], "index": 23, "isConst": true, "name": "toString", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "f", "type": "double"}, {"name": "format", "type": "QString"}, {"name": "precision", "type": "int"}], "index": 24, "isConst": true, "name": "toString", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "f", "type": "double"}, {"name": "format", "type": "QString"}], "index": 25, "isCloned": true, "isConst": true, "name": "toString", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "dateTime", "type": "QDateTime"}, {"name": "format", "type": "QString"}], "index": 26, "isConst": true, "name": "toString", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "dateTime", "type": "QDateTime"}, {"name": "format", "type": "QLocale::FormatType"}], "index": 27, "isConst": true, "name": "toString", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "dateTime", "type": "QDateTime"}], "index": 28, "isCloned": true, "isConst": true, "name": "toString", "returnType": "QString"}], "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "firstDayOfWeek", "read": "firstDayOfWeek", "required": false, "scriptable": true, "stored": true, "type": "QQmlLocale::DayOfWeek", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "measurementSystem", "read": "measurementSystem", "required": false, "scriptable": true, "stored": true, "type": "QLocale::MeasurementSystem", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "textDirection", "read": "textDirection", "required": false, "scriptable": true, "stored": true, "type": "Qt::LayoutDirection", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "weekDays", "read": "weekDays", "required": false, "scriptable": true, "stored": true, "type": "QList<QQmlLocale::DayOfWeek>", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "uiLanguages", "read": "uiLanguages", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "nativeLanguageName", "read": "nativeLanguageName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "nativeCountryName", "read": "nativeCountryName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "nativeTerritoryName", "read": "nativeTerritoryName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 9, "name": "decimalPoint", "read": "decimalPoint", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 10, "name": "groupSeparator", "read": "groupSeparator", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 11, "name": "percent", "read": "percent", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 12, "name": "zeroDigit", "read": "zeroDigit", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 13, "name": "negativeSign", "read": "negativeSign", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 14, "name": "positiveSign", "read": "positiveSign", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 15, "name": "exponential", "read": "exponential", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 16, "name": "amText", "read": "amText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 17, "name": "pmText", "read": "pmText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "numberOptions", "read": "numberOptions", "required": false, "scriptable": true, "stored": true, "type": "QLocale::NumberOptions", "user": false, "write": "setNumberOptions"}], "qualifiedClassName": "QQmlLocaleValueType"}], "inputFile": "qqmllocale_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}], "className": "QQmlPlatform", "lineNumber": 24, "object": true, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "os", "read": "os", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "pluginName", "read": "pluginName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}], "qualifiedClassName": "QQmlPlatform", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlplatform_p.h", "outputRevision": 69}, {"classes": [{"className": "QQmlProfiler", "lineNumber": 115, "object": true, "qualifiedClassName": "QQmlProfiler", "signals": [{"access": "public", "arguments": [{"type": "QList<QQmlProfilerData>"}, {"type": "QQmlProfiler::LocationHash"}], "index": 0, "name": "dataReady", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlProfilerDefinitions"}]}], "inputFile": "qqmlprofiler_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}], "className": "QQmlProperty", "gadget": true, "lineNumber": 22, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "object", "read": "object", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false}, {"constant": true, "designable": true, "final": true, "index": 1, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}], "qualifiedClassName": "QQmlProperty"}], "inputFile": "qqmlproperty.h", "outputRevision": 69}, {"classes": [{"className": "QQmlPropertyMap", "lineNumber": 18, "methods": [{"access": "public", "index": 1, "isConst": true, "name": "keys", "returnType": "QStringList"}], "object": true, "qualifiedClassName": "QQmlPropertyMap", "signals": [{"access": "public", "arguments": [{"name": "key", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 0, "name": "valueChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlpropertymap.h", "outputRevision": 69}, {"classes": [{"className": "QQmlScriptString", "gadget": true, "lineNumber": 23, "qualifiedClassName": "QQmlScriptString"}], "inputFile": "qqmlscriptstring.h", "outputRevision": 69}, {"classes": [{"className": "QQmlTypeLoaderNetworkReplyProxy", "lineNumber": 34, "object": true, "qualifiedClassName": "QQmlTypeLoaderNetworkReplyProxy", "slots": [{"access": "public", "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"type": "qint64"}, {"type": "qint64"}], "index": 1, "name": "downloadProgress", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QNetworkReply*"}], "index": 2, "name": "manualFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmltypeloadernetworkreplyproxy_p.h", "outputRevision": 69}, {"classes": [{"className": "QQmlGadgetPtrWrapper", "lineNumber": 68, "object": true, "qualifiedClassName": "QQmlGadgetPtrWrapper", "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "point"}, {"name": "QML.Foreign", "value": "QPointF"}, {"name": "QML.Extended", "value": "QQmlPointFValueType"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QQmlPointFValueType", "constructors": [{"access": "public", "index": 0, "name": "QQmlPointFValueType", "returnType": ""}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "index": 1, "name": "QQmlPointFValueType", "returnType": ""}, {"access": "public", "arguments": [{"name": "point", "type": "QPoint"}], "index": 2, "name": "QQmlPointFValueType", "returnType": ""}], "gadget": true, "lineNumber": 111, "methods": [{"access": "public", "index": 0, "isConst": true, "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "x", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setX"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "y", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setY"}], "qualifiedClassName": "QQmlPointFValueType", "superClasses": [{"access": "public", "name": "QPointF"}]}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QPoint"}, {"name": "QML.Extended", "value": "QQmlPointValueType"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QQmlPointValueType", "constructors": [{"access": "public", "arguments": [{"name": "point", "type": "QPoint"}], "index": 0, "name": "QQmlPointValueType", "returnType": ""}, {"access": "public", "arguments": [{"name": "point", "type": "QPointF"}], "index": 1, "name": "QQmlPointValueType", "returnType": ""}], "gadget": true, "lineNumber": 132, "methods": [{"access": "public", "index": 0, "isConst": true, "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "x", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setX"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "y", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setY"}], "qualifiedClassName": "QQmlPointValueType", "superClasses": [{"access": "public", "name": "QPoint"}]}, {"classInfos": [{"name": "QML.Element", "value": "size"}, {"name": "QML.Foreign", "value": "QSizeF"}, {"name": "QML.Extended", "value": "QQmlSizeFValueType"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QQmlSizeFValueType", "constructors": [{"access": "public", "index": 0, "name": "QQmlSizeFValueType", "returnType": ""}, {"access": "public", "arguments": [{"name": "size", "type": "QSizeF"}], "index": 1, "name": "QQmlSizeFValueType", "returnType": ""}, {"access": "public", "arguments": [{"name": "size", "type": "QSize"}], "index": 2, "name": "QQmlSizeFValueType", "returnType": ""}], "gadget": true, "lineNumber": 153, "methods": [{"access": "public", "index": 0, "isConst": true, "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "width", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "height", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setHeight"}], "qualifiedClassName": "QQmlSizeFValueType", "superClasses": [{"access": "public", "name": "QSizeF"}]}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QSize"}, {"name": "QML.Extended", "value": "QQmlSizeValueType"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QQmlSizeValueType", "constructors": [{"access": "public", "arguments": [{"name": "size", "type": "QSize"}], "index": 0, "name": "QQmlSizeValueType", "returnType": ""}, {"access": "public", "arguments": [{"name": "size", "type": "QSizeF"}], "index": 1, "name": "QQmlSizeValueType", "returnType": ""}], "gadget": true, "lineNumber": 174, "methods": [{"access": "public", "index": 0, "isConst": true, "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "width", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "height", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHeight"}], "qualifiedClassName": "QQmlSizeValueType", "superClasses": [{"access": "public", "name": "QSize"}]}, {"classInfos": [{"name": "QML.Element", "value": "rect"}, {"name": "QML.Foreign", "value": "QRectF"}, {"name": "QML.Extended", "value": "QQmlRectFValueType"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QQmlRectFValueType", "constructors": [{"access": "public", "index": 0, "name": "QQmlRectFValueType", "returnType": ""}, {"access": "public", "arguments": [{"name": "rect", "type": "QRectF"}], "index": 1, "name": "QQmlRectFValueType", "returnType": ""}, {"access": "public", "arguments": [{"name": "rect", "type": "QRect"}], "index": 2, "name": "QQmlRectFValueType", "returnType": ""}], "gadget": true, "lineNumber": 195, "methods": [{"access": "public", "index": 0, "isConst": true, "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "x", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setX"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "y", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setY"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "width", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "height", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setHeight"}, {"constant": false, "designable": false, "final": true, "index": 4, "name": "left", "read": "left", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": false, "final": true, "index": 5, "name": "right", "read": "right", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": false, "final": true, "index": 6, "name": "top", "read": "top", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": false, "final": true, "index": 7, "name": "bottom", "read": "bottom", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QQmlRectFValueType", "superClasses": [{"access": "public", "name": "QRectF"}]}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QRect"}, {"name": "QML.Extended", "value": "QQmlRectValueType"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QQmlRectValueType", "constructors": [{"access": "public", "arguments": [{"name": "rect", "type": "QRect"}], "index": 0, "name": "QQmlRectValueType", "returnType": ""}, {"access": "public", "arguments": [{"name": "rect", "type": "QRectF"}], "index": 1, "name": "QQmlRectValueType", "returnType": ""}], "gadget": true, "lineNumber": 232, "methods": [{"access": "public", "index": 0, "isConst": true, "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "x", "read": "x", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setX"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "y", "read": "y", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setY"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "width", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "height", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHeight"}, {"constant": false, "designable": false, "final": true, "index": 4, "name": "left", "read": "left", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": false, "final": true, "index": 5, "name": "right", "read": "right", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": false, "final": true, "index": 6, "name": "top", "read": "top", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": false, "final": true, "index": 7, "name": "bottom", "read": "bottom", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "QQmlRectValueType", "superClasses": [{"access": "public", "name": "QRect"}]}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QMarginsF"}, {"name": "QML.Extended", "value": "QQmlMarginsFValueType"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QQmlMarginsFValueType", "constructors": [{"access": "public", "arguments": [{"name": "margins", "type": "QMarginsF"}], "index": 0, "name": "QQmlMarginsFValueType", "returnType": ""}, {"access": "public", "arguments": [{"name": "margins", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "index": 1, "name": "QQmlMarginsFValueType", "returnType": ""}], "gadget": true, "lineNumber": 269, "methods": [{"access": "public", "index": 0, "isConst": true, "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "left", "read": "left", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setLeft"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "right", "read": "right", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setRight"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "top", "read": "top", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setTop"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "bottom", "read": "bottom", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBottom"}], "qualifiedClassName": "QQmlMarginsFValueType", "superClasses": [{"access": "public", "name": "QMarginsF"}]}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.Extended", "value": "QQmlMarginsValueType"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QQmlMarginsValueType", "constructors": [{"access": "public", "arguments": [{"name": "margins", "type": "<PERSON><PERSON><PERSON><PERSON>"}], "index": 0, "name": "QQmlMarginsValueType", "returnType": ""}, {"access": "public", "arguments": [{"name": "margins", "type": "QMarginsF"}], "index": 1, "name": "QQmlMarginsValueType", "returnType": ""}], "gadget": true, "lineNumber": 296, "methods": [{"access": "public", "index": 0, "isConst": true, "name": "toString", "returnType": "QString"}], "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "left", "read": "left", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLeft"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "right", "read": "right", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRight"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "top", "read": "top", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setTop"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "bottom", "read": "bottom", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBottom"}], "qualifiedClassName": "QQmlMarginsValueType", "superClasses": [{"access": "public", "name": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QEasingCurve"}, {"name": "QML.Extended", "value": "QQmlEasingValueType"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QQmlEasingValueType", "constructors": [{"access": "public", "index": 0, "name": "QQmlEasingValueType", "returnType": ""}, {"access": "public", "arguments": [{"name": "easing", "type": "QEasingCurve"}], "index": 1, "name": "QQmlEasingValueType", "returnType": ""}], "gadget": true, "lineNumber": 360, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "type", "read": "type", "required": false, "scriptable": true, "stored": true, "type": "QQmlEasingEnums::Type", "user": false, "write": "setType"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "amplitude", "read": "amplitude", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setAmplitude"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "overshoot", "read": "overshoot", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setOvershoot"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "period", "read": "period", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "bezierCurve", "read": "bezierCurve", "required": false, "scriptable": true, "stored": true, "type": "QList<qreal>", "user": false, "write": "setBezierCurve"}], "qualifiedClassName": "QQmlEasingValueType", "superClasses": [{"access": "public", "name": "QEasingCurve"}]}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Foreign", "value": "QQmlV4ExecutionEnginePtr"}, {"name": "QML.Extended", "value": "QQmlV4ExecutionEnginePtrForeign"}], "className": "QQmlV4ExecutionEnginePtrForeign", "gadget": true, "lineNumber": 391, "qualifiedClassName": "QQmlV4ExecutionEnginePtrForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Easing"}], "className": "QQmlEasingEnums", "enums": [{"isClass": false, "isFlag": false, "name": "Type", "values": ["Linear", "InQuad", "OutQuad", "InOutQuad", "OutInQuad", "InCubic", "OutCubic", "InOutCubic", "OutInCubic", "InQuart", "OutQuart", "InOutQuart", "OutInQuart", "In<PERSON><PERSON>t", "OutQuint", "InOutQuint", "OutInQuint", "InSine", "OutSine", "InOutSine", "OutInSine", "InExpo", "OutExpo", "InOutExpo", "OutInExpo", "InCirc", "OutCirc", "InOutCirc", "OutInCirc", "InElastic", "OutElastic", "InOutElastic", "OutInElastic", "InBack", "OutBack", "InOutBack", "OutInBack", "InBounce", "OutBounce", "InOutBounce", "OutInBounce", "InCurve", "OutCurve", "SineCurve", "CosineCurve", "BezierSpline", "<PERSON><PERSON>"]}], "lineNumber": 324, "namespace": true, "qualifiedClassName": "QQmlEasingEnums"}], "inputFile": "qqmlvaluetype_p.h", "outputRevision": 69}, {"classes": [{"className": "Debugger", "lineNumber": 41, "object": true, "qualifiedClassName": "QV4::Debugging::Debugger", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qv4debugging_p.h", "outputRevision": 69}, {"classes": [{"className": "QV4Include", "lineNumber": 34, "object": true, "qualifiedClassName": "QV4Include", "slots": [{"access": "private", "index": 0, "name": "finished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qv4include_p.h", "outputRevision": 69}, {"classes": [{"className": "GCStateMachine", "enums": [{"isClass": false, "isFlag": false, "name": "GCState", "values": ["Mark<PERSON><PERSON><PERSON>", "MarkGlobalObject", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "InitMarkPersistentValues", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "InitMarkWeakValues", "MarkWeak<PERSON><PERSON><PERSON>", "Mark<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "InitCallDestroyObjects", "CallDestroyObjects", "FreeWeakMaps", "FreeWeakSets", "HandleQObjectWrappers", "DoSweep", "Invalid", "Count"]}], "gadget": true, "lineNumber": 37, "qualifiedClassName": "QV4::GCStateMachine"}], "inputFile": "qv4mm_p.h", "outputRevision": 69}, {"classes": [{"className": "Profiler", "lineNumber": 165, "object": true, "qualifiedClassName": "QV4::Profiling::Profiler", "signals": [{"access": "public", "arguments": [{"type": "QV4::Profiling::FunctionLocationHash"}, {"type": "QList<QV4::Profiling::FunctionCallProperties>"}, {"type": "QList<QV4::Profiling::MemoryAllocationProperties>"}], "index": 0, "name": "dataReady", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qv4profiling_p.h", "outputRevision": 69}, {"classes": [{"className": "ReactionHandler", "lineNumber": 31, "object": true, "qualifiedClassName": "QV4::Promise::<PERSON><PERSON>Handler", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qv4promiseobject_p.h", "outputRevision": 69}, {"classes": [{"className": "MultiplyWrappedQObjectMap", "lineNumber": 438, "object": true, "qualifiedClassName": "QV4::MultiplyWrappedQObjectMap", "slots": [{"access": "private", "arguments": [{"type": "QObject*"}], "index": 0, "name": "removeDestroyedObject", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "private", "name": "QHash<QObjectBiPointer,QV4::WeakValue>"}]}], "inputFile": "qv4qobjectwrapper_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Sequence", "value": "double"}, {"name": "QML.Foreign", "value": "std::vector<qreal>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QRealStdVectorForeign", "gadget": true, "lineNumber": 138, "qualifiedClassName": "QRealStdVectorForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Sequence", "value": "double"}, {"name": "QML.Foreign", "value": "QList<qreal>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QRealListForeign", "gadget": true, "lineNumber": 139, "qualifiedClassName": "QRealListForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Sequence", "value": "double"}, {"name": "QML.Foreign", "value": "std::vector<double>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QDoubleStdVectorForeign", "gadget": true, "lineNumber": 142, "qualifiedClassName": "QDoubleStdVectorForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Sequence", "value": "float"}, {"name": "QML.Foreign", "value": "std::vector<float>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QFloatStdVectorForeign", "gadget": true, "lineNumber": 143, "qualifiedClassName": "QFloatStdVectorForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Sequence", "value": "int"}, {"name": "QML.Foreign", "value": "std::vector<int>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QIntStdVectorForeign", "gadget": true, "lineNumber": 144, "qualifiedClassName": "QIntStdVectorForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Sequence", "value": "bool"}, {"name": "QML.Foreign", "value": "std::vector<bool>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QBoolStdVectorForeign", "gadget": true, "lineNumber": 145, "qualifiedClassName": "QBoolStdVectorForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Sequence", "value": "QString"}, {"name": "QML.Foreign", "value": "std::vector<QString>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QStringStdVectorForeign", "gadget": true, "lineNumber": 146, "qualifiedClassName": "QStringStdVectorForeign"}, {"classInfos": [{"name": "QML.Element", "value": "anonymous"}, {"name": "QML.Sequence", "value": "QUrl"}, {"name": "QML.Foreign", "value": "std::vector<QUrl>"}, {"name": "QML.AddedInVersion", "value": "512"}], "className": "QUrlStdVectorForeign", "gadget": true, "lineNumber": 147, "qualifiedClassName": "QUrlStdVectorForeign"}], "inputFile": "qv4sequenceobject_p.h", "outputRevision": 69}, {"classes": [{"className": "ObjectReferenceHash", "lineNumber": 88, "object": true, "qualifiedClassName": "ObjectReferenceHash", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmldebugservice.cpp", "outputRevision": 69}, {"classes": [{"className": "QQmlFileNetworkReply", "lineNumber": 49, "object": true, "qualifiedClassName": "QQmlFileNetworkReply", "signals": [{"access": "public", "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"type": "qint64"}, {"type": "qint64"}], "index": 1, "name": "downloadProgress", "returnType": "void"}], "slots": [{"access": "public", "index": 2, "name": "networkFinished", "returnType": "void"}, {"access": "public", "arguments": [{"type": "qint64"}, {"type": "qint64"}], "index": 3, "name": "networkDownloadProgress", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlfile.cpp", "outputRevision": 69}, {"classes": [{"className": "QQmlXMLHttpRequest", "lineNumber": 967, "object": true, "qualifiedClassName": "QQmlXMLHttpRequest", "slots": [{"access": "private", "index": 0, "name": "readyRead", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QNetworkReply::NetworkError"}], "index": 1, "name": "error", "returnType": "void"}, {"access": "private", "index": 2, "name": "finished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlxmlhttprequest.cpp", "outputRevision": 69}]