"""
知识搜索模块
提供向量搜索、知识图谱搜索和综合知识搜索功能
"""

import os
import json
import logging
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.settings_loader import get_setting, get_absolute_path
except ImportError as e:
    print(f"导入配置模块失败: {e}")
    def get_setting(key, default=None): return default
    def get_absolute_path(path): return path

# 配置日志
logger = logging.getLogger(__name__)

class KnowledgeSearcher:
    """知识搜索器类"""
    
    def __init__(self):
        """初始化知识搜索器"""
        # 获取配置
        self.core_vector_path = get_setting("vector_path_core", "data/vector/core")
        self.help_vector_path = get_setting("vector_path_help", "data/vector/help")
        self.core_graph_path = get_setting("graph_file_path_core", "data/graph/core")
        self.help_graph_path = get_setting("graph_file_path_help", "data/graph/help")
        
        # 搜索参数
        self.core_vector_top_k = get_setting("search.vector_search.core_top_k", 3)
        self.help_vector_top_k = get_setting("search.vector_search.help_top_k", 3)
        self.core_graph_top_k = get_setting("search.graph_search.core_top_k", 5)
        self.help_graph_top_k = get_setting("search.graph_search.help_top_k", 5)
        self.neighbor_limit = get_setting("search.graph_search.neighbor_limit", 3)
        
        logger.info("知识搜索器已初始化")
    
    def search_knowledge(self, query: str) -> str:
        """
        综合搜索知识库
        
        Args:
            query: 查询文本
            
        Returns:
            str: 格式化的搜索结果
        """
        logger.info(f"开始知识搜索，查询: '{query}'")
        
        try:
            # 增强查询
            enhanced_query = self._enhance_query(query)
            logger.info(f"增强后的查询: '{enhanced_query}'")
            
            # 搜索核心知识库
            core_vector_results = self._search_vector_db(
                enhanced_query, self.core_vector_path, self.core_vector_top_k
            )
            logger.info(f"核心向量搜索完成，找到 {len(core_vector_results)} 个结果")
            
            # 搜索辅助知识库
            help_vector_results = self._search_vector_db(
                enhanced_query, self.help_vector_path, self.help_vector_top_k
            )
            logger.info(f"辅助向量搜索完成，找到 {len(help_vector_results)} 个结果")
            
            # 搜索核心知识图谱
            core_graph_results = self._search_knowledge_graph(
                enhanced_query, self.core_graph_path, self.core_graph_top_k
            )
            logger.info(f"核心图谱搜索完成，找到 {len(core_graph_results)} 个结果")
            
            # 搜索辅助知识图谱
            help_graph_results = self._search_knowledge_graph(
                enhanced_query, self.help_graph_path, self.help_graph_top_k
            )
            logger.info(f"辅助图谱搜索完成，找到 {len(help_graph_results)} 个结果")
            
            # 格式化结果
            formatted_result = self._format_search_results(
                core_vector_results, help_vector_results,
                core_graph_results, help_graph_results
            )
            
            logger.info("知识搜索完成")
            return formatted_result
            
        except Exception as e:
            logger.error(f"知识搜索时出错: {str(e)}")
            return f"搜索出错: {str(e)}"
    
    def _enhance_query(self, query: str) -> str:
        """
        增强查询（简化版本）
        
        Args:
            query: 原始查询
            
        Returns:
            str: 增强后的查询
        """
        # 简化版本，直接返回原查询
        # 在实际应用中可以调用LLM API进行查询增强
        return query
    
    def _search_vector_db(self, query: str, vector_path: str, top_k: int) -> List[Dict]:
        """
        搜索向量数据库
        
        Args:
            query: 查询文本
            vector_path: 向量数据库路径
            top_k: 返回结果数量
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        try:
            vector_path = get_absolute_path(vector_path)
            
            # 检查向量库文件是否存在
            index_path = os.path.join(vector_path, "index.faiss")
            metadata_path = os.path.join(vector_path, "metadata.json")
            
            if not os.path.exists(index_path) or not os.path.exists(metadata_path):
                logger.warning(f"向量库文件不存在: {vector_path}")
                return []
            
            # 这里应该实现实际的向量搜索逻辑
            # 目前返回模拟结果
            return [
                {
                    "content": f"模拟搜索结果 - 查询: {query}",
                    "source": f"模拟文档_{i+1}.txt",
                    "score": 0.9 - i * 0.1,
                    "metadata": {"type": "document"}
                }
                for i in range(min(top_k, 3))
            ]
            
        except Exception as e:
            logger.error(f"向量搜索时出错: {str(e)}")
            return []
    
    def _search_knowledge_graph(self, query: str, graph_path: str, top_k: int) -> List[Dict]:
        """
        搜索知识图谱
        
        Args:
            query: 查询文本
            graph_path: 知识图谱路径
            top_k: 返回结果数量
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        try:
            graph_path = get_absolute_path(graph_path)
            
            # 检查图谱文件是否存在
            entities_path = os.path.join(graph_path, "entities.json")
            relations_path = os.path.join(graph_path, "relations.json")
            
            if not os.path.exists(entities_path) or not os.path.exists(relations_path):
                logger.warning(f"知识图谱文件不存在: {graph_path}")
                return []
            
            # 这里应该实现实际的图谱搜索逻辑
            # 目前返回模拟结果
            return [
                {
                    "entity": f"实体_{i+1}",
                    "relation": f"关系_{i+1}",
                    "target": f"目标实体_{i+1}",
                    "score": 0.8 - i * 0.1,
                    "context": f"上下文信息_{i+1}"
                }
                for i in range(min(top_k, 3))
            ]
            
        except Exception as e:
            logger.error(f"图谱搜索时出错: {str(e)}")
            return []
    
    def _format_search_results(self, core_vector_results: List[Dict], 
                             help_vector_results: List[Dict],
                             core_graph_results: List[Dict], 
                             help_graph_results: List[Dict]) -> str:
        """
        格式化搜索结果
        
        Args:
            core_vector_results: 核心向量搜索结果
            help_vector_results: 辅助向量搜索结果
            core_graph_results: 核心图谱搜索结果
            help_graph_results: 辅助图谱搜索结果
            
        Returns:
            str: 格式化的结果字符串
        """
        separator = "=" * 50
        
        result = f"<rag>\n{separator}\n"
        result += "## 核心知识库 (Core KB)\n"
        result += "* **说明:** 这是最权威的参考资料，优先级最高。\n"
        result += "* **内容:** 主要包含产品信息、服务规范以及过往客服与其他用户的聊天记录精华。\n"
        result += "* **数据:**\n"
        result += self._format_vector_results(core_vector_results)
        result += f"\n{separator}\n"
        
        result += "## 辅助知识库 (Auxiliary KB)\n"
        result += "* **说明:** 这是补充性的参考资料。在使用前需要进行冲突检查，其优先级低于核心知识库。\n"
        result += "* **内容:** 可能包含常见问题解答、一般性行业知识等。\n"
        result += "* **数据:**\n"
        result += self._format_vector_results(help_vector_results)
        result += f"\n{separator}\n"
        
        result += "## 核心知识图谱 (Core KG)\n"
        result += "* **说明:** 基于核心知识库构建的知识关联网络，作为核心推理的辅助参考资料。\n"
        result += "* **用途:** 帮助理解实体间的关系和进行复杂推理。\n"
        result += "* **数据:**\n"
        result += self._format_graph_results(core_graph_results)
        result += f"\n{separator}\n"
        
        result += "## 辅助知识图谱 (Auxiliary KG)\n"
        result += "* **说明:** 基于辅助知识库构建的知识关联网络，作为补充性的推理参考资料。\n"
        result += "* **用途:** 提供额外的实体关系信息。\n"
        result += "* **数据:**\n"
        result += self._format_graph_results(help_graph_results)
        result += f"\n{separator}\n"
        result += "</rag>\n"
        
        return result
    
    def _format_vector_results(self, results: List[Dict]) -> str:
        """格式化向量搜索结果"""
        if not results:
            return "暂无相关数据。\n"
        
        formatted = ""
        for i, result in enumerate(results, 1):
            content = result.get("content", "")
            source = result.get("source", "未知来源")
            score = result.get("score", 0.0)
            
            formatted += f"### 结果 {i} (相似度: {score:.3f})\n"
            formatted += f"**来源:** {source}\n"
            formatted += f"**内容:** {content}\n\n"
        
        return formatted
    
    def _format_graph_results(self, results: List[Dict]) -> str:
        """格式化图谱搜索结果"""
        if not results:
            return "暂无相关关系数据。\n"
        
        formatted = ""
        for i, result in enumerate(results, 1):
            entity = result.get("entity", "")
            relation = result.get("relation", "")
            target = result.get("target", "")
            context = result.get("context", "")
            score = result.get("score", 0.0)
            
            formatted += f"### 关系 {i} (相关度: {score:.3f})\n"
            formatted += f"**实体关系:** {entity} → {relation} → {target}\n"
            formatted += f"**上下文:** {context}\n\n"
        
        return formatted
    
    def search_vector_only(self, query: str, kb_type: str = "core") -> List[Dict]:
        """
        仅搜索向量数据库
        
        Args:
            query: 查询文本
            kb_type: 知识库类型，"core" 或 "help"
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        if kb_type == "core":
            return self._search_vector_db(query, self.core_vector_path, self.core_vector_top_k)
        else:
            return self._search_vector_db(query, self.help_vector_path, self.help_vector_top_k)
    
    def search_graph_only(self, query: str, kb_type: str = "core") -> List[Dict]:
        """
        仅搜索知识图谱
        
        Args:
            query: 查询文本
            kb_type: 知识库类型，"core" 或 "help"
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        if kb_type == "core":
            return self._search_knowledge_graph(query, self.core_graph_path, self.core_graph_top_k)
        else:
            return self._search_knowledge_graph(query, self.help_graph_path, self.help_graph_top_k)
