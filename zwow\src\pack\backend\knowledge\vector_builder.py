"""
向量构建器
将文档转换为向量并构建FAISS索引
"""

import os
import json
import logging
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.settings_loader import get_setting, get_absolute_path
    from .document_processor import DocumentProcessor
except ImportError as e:
    print(f"导入模块失败: {e}")
    def get_setting(key, default=None): return default
    def get_absolute_path(path): return path
    class DocumentProcessor:
        def __init__(self): pass
        def process_documents(self, path): return []

# 配置日志
logger = logging.getLogger(__name__)

class VectorBuilder:
    """向量构建器类"""
    
    def __init__(self):
        """初始化向量构建器"""
        self.document_processor = DocumentProcessor()
        
        # 向量配置
        self.embedding_model = get_setting("vector_db.embedding_model", "text-embedding-ada-002")
        self.dimension = get_setting("vector_db.dimension", 1536)
        self.index_type = get_setting("vector_db.index_type", "Flat")
        
        # API配置
        self.api_key = get_setting("llm.qwen.api_key", "")
        self.api_base_url = get_setting("llm.qwen.base_url", "https://dashscope.aliyuncs.com/api/v1")
        
        logger.info("向量构建器已初始化")
    
    def build_vector_database(self, source_path: str, output_path: str) -> bool:
        """
        构建向量数据库
        
        Args:
            source_path: 源文档目录路径
            output_path: 输出向量数据库路径
            
        Returns:
            bool: 构建是否成功
        """
        try:
            logger.info(f"开始构建向量数据库，源路径: {source_path}")
            
            # 处理文档
            documents = self.document_processor.process_documents(source_path)
            if not documents:
                logger.warning("没有找到可处理的文档")
                return False
            
            logger.info(f"找到 {len(documents)} 个文档块")
            
            # 获取文本嵌入
            texts = [doc['content'] for doc in documents]
            embeddings, doc_indices, text_chunks = self._get_embeddings(texts)
            
            if embeddings is None or len(embeddings) == 0:
                logger.error("获取文本嵌入失败")
                return False
            
            logger.info(f"成功获取 {len(embeddings)} 个向量")
            
            # 创建FAISS索引
            index = self._create_faiss_index(embeddings)
            if index is None:
                logger.error("创建FAISS索引失败")
                return False
            
            # 准备元数据
            metadata = self._prepare_metadata(documents, doc_indices, text_chunks)
            
            # 保存向量数据库
            success = self._save_vector_database(index, metadata, output_path)
            
            if success:
                logger.info(f"向量数据库构建成功，保存到: {output_path}")
            else:
                logger.error("保存向量数据库失败")
            
            return success
            
        except Exception as e:
            logger.error(f"构建向量数据库时出错: {str(e)}")
            return False
    
    def _get_embeddings(self, texts: List[str]) -> Tuple[Optional[np.ndarray], List[int], List[str]]:
        """
        获取文本嵌入向量
        
        Args:
            texts: 文本列表
            
        Returns:
            Tuple: (嵌入向量数组, 文档索引列表, 文本块列表)
        """
        try:
            # 这里应该调用实际的嵌入API
            # 目前使用模拟向量
            logger.info("生成模拟向量（实际应用中应调用嵌入API）")
            
            embeddings = []
            doc_indices = []
            text_chunks = []
            
            for i, text in enumerate(texts):
                # 生成模拟向量
                vector = np.random.rand(self.dimension).astype(np.float32)
                # 归一化向量
                vector = vector / np.linalg.norm(vector)
                
                embeddings.append(vector)
                doc_indices.append(i)
                text_chunks.append(text)
            
            embeddings_array = np.array(embeddings)
            logger.info(f"生成了 {len(embeddings)} 个向量，维度: {self.dimension}")
            
            return embeddings_array, doc_indices, text_chunks
            
        except Exception as e:
            logger.error(f"获取嵌入向量时出错: {str(e)}")
            return None, [], []
    
    def _get_embedding_from_api(self, text: str) -> Optional[np.ndarray]:
        """
        从API获取单个文本的嵌入向量
        
        Args:
            text: 输入文本
            
        Returns:
            Optional[np.ndarray]: 嵌入向量
        """
        try:
            # 这里应该实现实际的API调用
            # 例如调用Qwen的嵌入API
            
            # 模拟API调用
            import time
            time.sleep(0.1)  # 模拟API延迟
            
            # 生成模拟向量
            vector = np.random.rand(self.dimension).astype(np.float32)
            vector = vector / np.linalg.norm(vector)
            
            return vector
            
        except Exception as e:
            logger.error(f"从API获取嵌入向量时出错: {str(e)}")
            return None
    
    def _create_faiss_index(self, embeddings: np.ndarray) -> Optional[Any]:
        """
        创建FAISS索引
        
        Args:
            embeddings: 嵌入向量数组
            
        Returns:
            Optional[Any]: FAISS索引对象
        """
        try:
            import faiss
            
            # 确保向量已归一化
            faiss.normalize_L2(embeddings)
            
            # 创建索引
            if self.index_type == "Flat":
                index = faiss.IndexFlatIP(self.dimension)  # 内积索引，适合余弦相似度
            else:
                index = faiss.index_factory(self.dimension, self.index_type)
            
            # 添加向量到索引
            index.add(embeddings)
            
            logger.info(f"FAISS索引创建成功，类型: {self.index_type}, 向量数量: {index.ntotal}")
            return index
            
        except ImportError:
            logger.error("FAISS未安装，无法创建向量索引")
            return None
        except Exception as e:
            logger.error(f"创建FAISS索引时出错: {str(e)}")
            return None
    
    def _prepare_metadata(self, documents: List[Dict], doc_indices: List[int], 
                         text_chunks: List[str]) -> List[Dict]:
        """
        准备元数据
        
        Args:
            documents: 文档列表
            doc_indices: 文档索引列表
            text_chunks: 文本块列表
            
        Returns:
            List[Dict]: 元数据列表
        """
        metadata = []
        
        for i, (doc_idx, text_chunk) in enumerate(zip(doc_indices, text_chunks)):
            if doc_idx < len(documents):
                doc = documents[doc_idx]
                meta = {
                    'index': i,
                    'content': text_chunk,
                    'source': doc.get('source', ''),
                    'source_path': doc.get('source_path', ''),
                    'chunk_id': doc.get('chunk_id', 0),
                    'total_chunks': doc.get('total_chunks', 1),
                    'file_hash': doc.get('file_hash', ''),
                    'metadata': doc.get('metadata', {})
                }
                metadata.append(meta)
        
        return metadata
    
    def _save_vector_database(self, index: Any, metadata: List[Dict], output_path: str) -> bool:
        """
        保存向量数据库
        
        Args:
            index: FAISS索引
            metadata: 元数据
            output_path: 输出路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            output_path = get_absolute_path(output_path)
            os.makedirs(output_path, exist_ok=True)
            
            # 保存FAISS索引
            index_path = os.path.join(output_path, "index.faiss")
            import faiss
            faiss.write_index(index, index_path)
            
            # 保存元数据
            metadata_path = os.path.join(output_path, "metadata.json")
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            # 保存配置信息
            config_path = os.path.join(output_path, "config.json")
            config_info = {
                'dimension': self.dimension,
                'index_type': self.index_type,
                'embedding_model': self.embedding_model,
                'total_vectors': index.ntotal,
                'metadata_count': len(metadata)
            }
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_info, f, ensure_ascii=False, indent=2)
            
            logger.info(f"向量数据库已保存到: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存向量数据库时出错: {str(e)}")
            return False
    
    def load_vector_database(self, vector_path: str) -> Tuple[Optional[Any], Optional[List[Dict]]]:
        """
        加载向量数据库
        
        Args:
            vector_path: 向量数据库路径
            
        Returns:
            Tuple: (FAISS索引, 元数据列表)
        """
        try:
            vector_path = get_absolute_path(vector_path)
            
            # 检查文件是否存在
            index_path = os.path.join(vector_path, "index.faiss")
            metadata_path = os.path.join(vector_path, "metadata.json")
            
            if not os.path.exists(index_path) or not os.path.exists(metadata_path):
                logger.warning(f"向量数据库文件不存在: {vector_path}")
                return None, None
            
            # 加载FAISS索引
            import faiss
            index = faiss.read_index(index_path)
            
            # 加载元数据
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            logger.info(f"向量数据库加载成功: {vector_path}, 向量数量: {index.ntotal}")
            return index, metadata
            
        except Exception as e:
            logger.error(f"加载向量数据库时出错: {str(e)}")
            return None, None
    
    def search_vectors(self, query_vector: np.ndarray, vector_path: str, top_k: int = 5) -> List[Dict]:
        """
        搜索向量
        
        Args:
            query_vector: 查询向量
            vector_path: 向量数据库路径
            top_k: 返回结果数量
            
        Returns:
            List[Dict]: 搜索结果
        """
        try:
            index, metadata = self.load_vector_database(vector_path)
            if index is None or metadata is None:
                return []
            
            # 归一化查询向量
            import faiss
            query_vector = query_vector.reshape(1, -1).astype(np.float32)
            faiss.normalize_L2(query_vector)
            
            # 搜索
            scores, indices = index.search(query_vector, top_k)
            
            # 组织结果
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx < len(metadata):
                    result = metadata[idx].copy()
                    result['score'] = float(score)
                    result['rank'] = i + 1
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"搜索向量时出错: {str(e)}")
            return []
