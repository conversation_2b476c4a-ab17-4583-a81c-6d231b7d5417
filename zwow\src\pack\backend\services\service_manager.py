"""
服务管理器
管理各种后台服务的启动、停止和状态监控
"""

import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Type
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.settings_loader import get_setting
    from src.pack.common_utils.database.app_db_manager import AppDBManager
    from ..chat.whatsapp_service import WhatsAppService
    from .poller import StorePoller
except ImportError as e:
    print(f"导入模块失败: {e}")
    def get_setting(key, default=None): return default
    class AppDBManager:
        def __init__(self): pass
        def initialize(self): return True
    class WhatsAppService:
        def __init__(self, store_name, username=None): 
            self.is_running = False
            self.store_name = store_name
        def start(self): return True
        def stop(self): return True
        def get_client_status(self): return {"is_running": self.is_running}
    class StorePoller:
        def __init__(self): 
            self.running = False
        def start(self): return True
        def stop(self): return True
        def get_status(self): return {"running": self.running}

# 配置日志
logger = logging.getLogger(__name__)

class ServiceManager:
    """服务管理器类"""
    
    def __init__(self):
        """初始化服务管理器"""
        # 数据库管理器
        self.db_manager = AppDBManager()
        self.db_manager.initialize()
        
        # 注册的平台服务类
        self.platform_classes = {
            'whatsapp': WhatsAppService
        }
        
        # 运行中的服务实例
        self.services = {}  # platform -> {store_name -> service_instance}
        
        # 全局服务
        self.global_services = {}  # service_name -> service_instance
        
        # 初始化轮询器
        self.poller = StorePoller()
        
        logger.info("服务管理器已初始化")
    
    def register_platform(self, platform_name: str, service_class: Type):
        """
        注册平台服务类
        
        Args:
            platform_name: 平台名称
            service_class: 服务类
        """
        self.platform_classes[platform_name] = service_class
        logger.info(f"已注册平台服务: {platform_name}")
    
    def start_service(self, platform: str, store_name: str, username: str = None) -> bool:
        """
        启动指定平台的店铺服务
        
        Args:
            platform: 平台名称
            store_name: 店铺名称
            username: 用户名
            
        Returns:
            bool: 启动是否成功
        """
        try:
            # 检查平台是否已注册
            if platform not in self.platform_classes:
                logger.error(f"平台 {platform} 未注册")
                return False
            
            # 检查该店铺服务是否已经在运行
            if (platform in self.services and 
                store_name in self.services[platform]):
                service = self.services[platform][store_name]
                if service.is_running:
                    logger.warning(f"{platform} 平台的店铺 {store_name} 服务已在运行")
                    return True
            
            # 创建服务实例
            service_class = self.platform_classes[platform]
            service = service_class(store_name, username)
            
            # 启动服务
            result = service.start()
            
            if result:
                # 存储服务实例
                if platform not in self.services:
                    self.services[platform] = {}
                self.services[platform][store_name] = service
                
                logger.info(f"已启动 {platform} 平台的店铺 {store_name} 服务")
                
                # 更新数据库中的服务状态
                self._update_service_status_in_db(store_name, platform, 1)
                
                return True
            else:
                logger.error(f"启动 {platform} 平台的店铺 {store_name} 服务失败")
                return False
                
        except Exception as e:
            logger.error(f"启动服务时出错: {str(e)}")
            return False
    
    def stop_service(self, platform: str, store_name: str) -> bool:
        """
        停止指定平台的店铺服务
        
        Args:
            platform: 平台名称
            store_name: 店铺名称
            
        Returns:
            bool: 停止是否成功
        """
        try:
            # 检查服务是否存在并运行
            if (platform not in self.services or 
                store_name not in self.services[platform]):
                logger.warning(f"{platform} 平台的店铺 {store_name} 服务未运行")
                return True
            
            # 获取服务实例
            service = self.services[platform][store_name]
            
            # 停止服务
            result = service.stop()
            
            if result:
                # 从管理器中移除服务实例
                del self.services[platform][store_name]
                
                # 如果该平台没有其他服务，清理平台记录
                if not self.services[platform]:
                    del self.services[platform]
                
                logger.info(f"已停止 {platform} 平台的店铺 {store_name} 服务")
                
                # 更新数据库中的服务状态
                self._update_service_status_in_db(store_name, platform, 0)
                
                return True
            else:
                logger.error(f"停止 {platform} 平台的店铺 {store_name} 服务失败")
                return False
                
        except Exception as e:
            logger.error(f"停止服务时出错: {str(e)}")
            return False
    
    def get_service(self, platform: str, store_name: str):
        """
        获取指定平台的店铺服务实例
        
        Args:
            platform: 平台名称
            store_name: 店铺名称
            
        Returns:
            服务实例或None
        """
        if (platform in self.services and 
            store_name in self.services[platform]):
            return self.services[platform][store_name]
        return None
    
    def get_running_services(self, platform: str = None) -> List[Dict[str, Any]]:
        """
        获取正在运行的服务列表
        
        Args:
            platform: 可选，平台名称过滤
            
        Returns:
            List[Dict]: 服务信息列表
        """
        try:
            # 从数据库获取服务状态
            db_services = self._get_running_services_from_db(platform)
            
            # 从内存获取服务状态
            memory_services = []
            for plat, stores in self.services.items():
                if platform and plat != platform:
                    continue
                
                for store_name, service in stores.items():
                    if hasattr(service, 'is_running') and service.is_running:
                        memory_services.append({
                            'platform': plat,
                            'store_name': store_name,
                            'status': 'running'
                        })
            
            # 同步数据库和内存状态
            self._sync_service_status(db_services, memory_services)
            
            return memory_services
            
        except Exception as e:
            logger.error(f"获取运行中服务时出错: {str(e)}")
            return []
    
    def start_poller(self) -> bool:
        """
        启动轮询器
        
        Returns:
            bool: 启动是否成功
        """
        try:
            if self.poller.running:
                logger.warning("轮询器已在运行")
                return True
            
            result = self.poller.start()
            if result:
                self.global_services['poller'] = self.poller
                logger.info("轮询器已启动")
            else:
                logger.error("轮询器启动失败")
            
            return result
            
        except Exception as e:
            logger.error(f"启动轮询器时出错: {str(e)}")
            return False
    
    def stop_poller(self) -> bool:
        """
        停止轮询器
        
        Returns:
            bool: 停止是否成功
        """
        try:
            if not self.poller.running:
                logger.warning("轮询器未在运行")
                return True
            
            result = self.poller.stop()
            if result:
                if 'poller' in self.global_services:
                    del self.global_services['poller']
                logger.info("轮询器已停止")
            else:
                logger.error("轮询器停止失败")
            
            return result
            
        except Exception as e:
            logger.error(f"停止轮询器时出错: {str(e)}")
            return False
    
    def get_poller_status(self) -> Dict[str, Any]:
        """
        获取轮询器状态
        
        Returns:
            Dict[str, Any]: 轮询器状态
        """
        return self.poller.get_status()
    
    def stop_all_services(self) -> bool:
        """
        停止所有服务
        
        Returns:
            bool: 停止是否成功
        """
        try:
            success = True
            
            # 停止所有平台服务
            for platform in list(self.services.keys()):
                for store_name in list(self.services[platform].keys()):
                    if not self.stop_service(platform, store_name):
                        success = False
            
            # 停止轮询器
            if not self.stop_poller():
                success = False
            
            logger.info("所有服务停止完成")
            return success
            
        except Exception as e:
            logger.error(f"停止所有服务时出错: {str(e)}")
            return False
    
    def get_service_statistics(self) -> Dict[str, Any]:
        """
        获取服务统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            total_services = 0
            platform_counts = {}
            
            for platform, stores in self.services.items():
                count = len(stores)
                platform_counts[platform] = count
                total_services += count
            
            poller_status = self.get_poller_status()
            
            return {
                'total_services': total_services,
                'platform_counts': platform_counts,
                'poller_status': poller_status,
                'registered_platforms': list(self.platform_classes.keys())
            }
            
        except Exception as e:
            logger.error(f"获取服务统计时出错: {str(e)}")
            return {}
    
    def _update_service_status_in_db(self, store_name: str, platform: str, status: int):
        """
        更新数据库中的服务状态
        
        Args:
            store_name: 店铺名称
            platform: 平台名称
            status: 状态（1=运行，0=停止）
        """
        try:
            # 这里应该调用数据库操作来更新服务状态
            # 目前只记录日志
            logger.debug(f"更新数据库服务状态: {store_name} ({platform}) -> {status}")
        except Exception as e:
            logger.error(f"更新数据库服务状态时出错: {str(e)}")
    
    def _get_running_services_from_db(self, platform: str = None) -> List[Dict[str, Any]]:
        """
        从数据库获取运行中的服务
        
        Args:
            platform: 平台过滤器
            
        Returns:
            List[Dict[str, Any]]: 服务列表
        """
        try:
            # 这里应该从数据库查询运行中的服务
            # 目前返回空列表
            return []
        except Exception as e:
            logger.error(f"从数据库获取服务状态时出错: {str(e)}")
            return []
    
    def _sync_service_status(self, db_services: List[Dict], memory_services: List[Dict]):
        """
        同步数据库和内存中的服务状态
        
        Args:
            db_services: 数据库中的服务列表
            memory_services: 内存中的服务列表
        """
        try:
            # 将内存中的服务转换为集合
            memory_set = set()
            for service in memory_services:
                key = (service.get("platform", ""), service.get("store_name", ""))
                memory_set.add(key)
            
            # 将数据库中的服务转换为集合
            db_set = set()
            for service in db_services:
                key = (service.get("platform", ""), service.get("store_name", ""))
                db_set.add(key)
            
            # 数据库中有记录但内存中没有的服务（可能已崩溃）
            for platform, store_name in db_set - memory_set:
                logger.warning(f"数据库显示服务 {store_name} ({platform}) 在运行，但实际未运行")
                # 更新数据库状态
                self._update_service_status_in_db(store_name, platform, 0)
            
            # 内存中有但数据库中没有的服务（可能数据库未同步）
            for platform, store_name in memory_set - db_set:
                logger.info(f"同步服务状态到数据库: {store_name} ({platform})")
                self._update_service_status_in_db(store_name, platform, 1)
                
        except Exception as e:
            logger.error(f"同步服务状态时出错: {str(e)}")
    
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            Dict[str, Any]: 健康状态
        """
        try:
            stats = self.get_service_statistics()
            poller_status = self.get_poller_status()
            
            return {
                'status': 'healthy',
                'service_manager': 'ok',
                'total_services': stats.get('total_services', 0),
                'poller_running': poller_status.get('running', False),
                'registered_platforms': stats.get('registered_platforms', [])
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
