{"app_name": "WOWCKER AGENT", "version": "v1.0.0", "copyright": "", "year": 2024, "source_file_path_core": "data/source/core", "source_file_path_help": "data/source/help", "vector_path_core": "data/vector/core", "vector_path_help": "data/vector/help", "graph_file_path_core": "data/graph/core", "graph_file_path_help": "data/graph/help", "database": {"path": "data/hermes.db", "backup_enabled": true, "backup_interval": 24}, "api": {"base_url": "http://************:9090/api", "timeout": 30, "retry_count": 3}, "poller": {"interval": 30, "enabled": true}, "knowledge_graph": {"use_multiprocessing": false, "entity_extraction": {"min_entity_freq": 2, "max_entity_length": 10, "custom_entities": []}, "relation_extraction": {"min_relation_freq": 2, "max_distance": 50, "custom_relations": ["包含", "属于", "应用于", "等同于"]}}, "vector_db": {"chunk_size": 1000, "chunk_overlap": 200, "embedding_model": "text-embedding-ada-002", "dimension": 1536, "index_type": "Flat", "similarity_threshold": 0.7}, "search": {"bm25_k1": 1.2, "bm25_b": 0.75, "max_results": 10, "min_score_threshold": 0.1, "vector_search": {"core_top_k": 5, "help_top_k": 5, "min_score_threshold": 0.3}, "graph_search": {"core_top_k": 5, "help_top_k": 5, "neighbor_limit": 3}, "hybrid": {"vector_weight": 0.6, "bm25_weight": 0.4, "fusion_method": "weighted_sum"}, "default_type": "hybrid", "default_kb_type": "core", "default_top_k": 5}, "chat": {"whatsapp": {"session_path": "data/whatsapp_session", "timeout": 30}, "message": {"max_context_length": 4000, "response_timeout": 30, "auto_translate": true}}, "llm": {"qwen": {"api_key": "", "base_url": "https://dashscope.aliyuncs.com/api/v1", "model": "qwen-plus-latest", "temperature": 0.1, "max_tokens": 100}}, "bulk_acquisition": {"send_interval": 1.0, "max_retries": 3, "timeout": 30, "batch_size": 10, "auto_add_customers": true, "default_source": "bulk_acquisition", "data_dir": "data/customers"}, "scheduler": {"check_interval": 1, "max_concurrent_tasks": 10}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_enabled": true, "file_path": "logs/app.log"}}