"""
登录页面组件
提供用户登录界面和认证功能
"""

import os
import sys
import json
import time
import logging
import requests
from pathlib import Path
from PySide6.QtCore import Qt, QTimer, Signal, QThread, pyqtSignal
from PySide6.QtGui import QIcon, QPixmap, QColor, QPainter, QFont, QPen
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QFrame, QMessageBox, QComboBox, QSpacerItem, QSizePolicy
)

# 导入数据库模块
try:
    from ...common_utils.database.app_db_manager import AppDBManager
    from ...config.settings_loader import get_setting
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    sys.path.append(str(Path(__file__).parent.parent.parent.parent.parent))
    from src.pack.common_utils.database.app_db_manager import AppDBManager
    from src.pack.config.settings_loader import get_setting

# 配置日志
logger = logging.getLogger(__name__)

# 设置样式常量
DARK_BLUE = "#1B1E23"
DARK_BLUE_TWO = "#21252B"
MEDIUM_BLUE = "#292D32"
LIGHT_BLUE = "#3C4D5F"
VERY_LIGHT_BLUE = "#6C99F4"
TEXT_COLOR = "#FFFFFF"

class LoginWorker(QThread):
    """登录认证工作线程"""
    login_success = pyqtSignal(dict, list)  # 用户信息, 店铺列表
    login_failed = pyqtSignal(str)  # 错误信息
    
    def __init__(self, username, password, api_base_url):
        super().__init__()
        self.username = username
        self.password = password
        self.api_base_url = api_base_url
        self.db_manager = AppDBManager()
        self.db_manager.initialize()
    
    def run(self):
        """执行登录认证"""
        try:
            # 准备请求数据
            request_data = {
                "plg_usn": self.username,
                "plg_pwd": self.password
            }
            
            # 发送登录请求
            url = f"{self.api_base_url}/plugin/login"
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'WowckerPlugin/1.0'
            }
            
            response = requests.post(url, json=request_data, headers=headers, timeout=30)
            
            if response.status_code == 200:
                response_data = response.json()
                
                if response_data.get("code") == 200 and response_data.get("data", {}).get("verified", False):
                    # 验证成功，保存用户信息
                    user_id = self.save_user_to_db(self.username, self.password)
                    if user_id != -1:
                        # 获取店铺信息
                        stores_info = response_data.get("data", {}).get("stores", [])
                        self.save_stores_to_db(stores_info)
                        
                        user_info = {"id": user_id, "username": self.username}
                        self.login_success.emit(user_info, stores_info)
                    else:
                        self.login_failed.emit("无法保存用户信息")
                else:
                    self.login_failed.emit("用户名或密码错误")
            else:
                self.login_failed.emit("无法连接到验证服务")
                
        except requests.exceptions.RequestException:
            self.login_failed.emit("网络连接失败")
        except Exception as e:
            self.login_failed.emit(f"登录失败: {str(e)}")
    
    def save_user_to_db(self, username, password):
        """保存用户到数据库"""
        try:
            # 检查用户是否已存在
            existing_user = self.db_manager.get_user_by_username(username)
            if existing_user:
                # 更新密码
                user_id = existing_user["id"]
                self.db_manager.update_user(user_id, {"password": password})
                return user_id
            else:
                # 创建新用户
                return self.db_manager.add_user(username, password)
        except Exception as e:
            logger.error(f"保存用户信息时出错: {str(e)}")
            return -1
    
    def save_stores_to_db(self, stores_info):
        """保存店铺信息到数据库"""
        try:
            for store_data in stores_info:
                store_name = store_data.get("plg_shopname", "")
                status = store_data.get("plg_status", 1)
                prompt = store_data.get("plg_prompt", "")
                api_key = store_data.get("plg_apikey", "")
                points = store_data.get("plg_points", 0)
                
                if store_name:
                    # 检查店铺是否已存在
                    existing_store = self.db_manager.get_store_by_name(store_name)
                    if existing_store:
                        # 更新店铺信息
                        store_id = existing_store["id"]
                        self.db_manager.update_store(store_id, {
                            "is_active": bool(status),
                            "prompt": prompt,
                            "api_key": api_key,
                            "points": points
                        })
                    else:
                        # 创建新店铺
                        self.db_manager.add_store(
                            store_name=store_name,
                            is_active=bool(status),
                            prompt=prompt,
                            api_key=api_key,
                            points=points
                        )
        except Exception as e:
            logger.error(f"保存店铺信息时出错: {str(e)}")

class LoginPage(QWidget):
    """登录页面组件"""
    
    login_success = pyqtSignal(dict, list)  # 登录成功信号
    
    def __init__(self):
        super().__init__()
        
        # 初始化数据库管理器
        self.db_manager = AppDBManager()
        self.db_manager.initialize()
        
        # API配置
        self.api_base_url = get_setting("api.base_url", "http://47.120.74.30:9090/api")
        
        # 用户和店铺数据
        self.current_user = None
        self.stores = []
        
        # 登录工作线程
        self.login_worker = None
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 添加垂直间距
        main_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        # 创建登录框架
        self.create_login_frame()
        main_layout.addWidget(self.login_frame, 0, Qt.AlignCenter)
        
        # 添加垂直间距
        main_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
    
    def create_login_frame(self):
        """创建登录框架"""
        self.login_frame = QFrame()
        self.login_frame.setFixedSize(400, 550)
        self.login_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {DARK_BLUE};
                border-radius: 10px;
            }}
        """)
        
        # 设置登录框布局
        login_layout = QVBoxLayout(self.login_frame)
        login_layout.setContentsMargins(20, 20, 20, 20)
        login_layout.setSpacing(15)
        
        # 添加LOGO和标题
        self.create_header(login_layout)
        
        # 添加输入字段
        self.create_input_fields(login_layout)
        
        # 添加登录按钮
        self.create_login_button(login_layout)
        
        # 添加店铺选择
        self.create_store_selection(login_layout)
    
    def create_header(self, layout):
        """创建头部区域"""
        # LOGO和标题布局
        header_layout = QHBoxLayout()
        header_layout.setAlignment(Qt.AlignLeft)
        
        # 创建机器人LOGO
        logo_label = QLabel()
        logo_pixmap = self.create_robot_logo()
        logo_label.setPixmap(logo_pixmap)
        
        # 标题文本
        title_label = QLabel("WOWCKER")
        title_label.setStyleSheet(f"""
            color: {TEXT_COLOR};
            font-size: 24px;
            font-weight: bold;
            margin-left: 10px;
        """)
        
        header_layout.addWidget(logo_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # 副标题
        subtitle_label = QLabel("智能客服代理系统")
        subtitle_label.setStyleSheet(f"""
            color: {LIGHT_BLUE};
            font-size: 14px;
            margin-bottom: 20px;
        """)
        layout.addWidget(subtitle_label)
    
    def create_robot_logo(self):
        """创建机器人LOGO"""
        logo_pixmap = QPixmap(32, 32)
        logo_pixmap.fill(Qt.transparent)
        painter = QPainter(logo_pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 设置颜色
        robot_color = QColor(VERY_LIGHT_BLUE)
        
        # 画机器人头部
        painter.setPen(Qt.NoPen)
        painter.setBrush(robot_color)
        painter.drawRoundedRect(4, 6, 24, 18, 6, 6)
        
        # 画机器人天线
        painter.setPen(QPen(robot_color, 2))
        painter.drawLine(16, 6, 16, 2)
        painter.setBrush(robot_color)
        painter.drawEllipse(14, 1, 4, 4)
        
        # 画眼睛
        painter.setBrush(QColor(DARK_BLUE))
        painter.drawEllipse(10, 12, 3, 3)
        painter.drawEllipse(19, 12, 3, 3)
        
        # 画嘴巴
        painter.setPen(QPen(QColor(DARK_BLUE), 2))
        painter.drawLine(12, 18, 20, 18)
        
        painter.end()
        return logo_pixmap
    
    def create_input_fields(self, layout):
        """创建输入字段"""
        # 用户名输入
        username_label = QLabel("— 用户名")
        username_label.setStyleSheet(f"color: {TEXT_COLOR}; font-size: 14px;")
        layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入用户名")
        self.username_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {DARK_BLUE_TWO};
                border: 1px solid {LIGHT_BLUE};
                border-radius: 5px;
                padding: 10px;
                color: {TEXT_COLOR};
                font-size: 14px;
            }}
            QLineEdit:focus {{
                border: 1px solid {VERY_LIGHT_BLUE};
            }}
        """)
        self.username_input.setMinimumHeight(45)
        layout.addWidget(self.username_input)
        
        # 密码输入
        password_label = QLabel("— 密码")
        password_label.setStyleSheet(f"color: {TEXT_COLOR}; font-size: 14px;")
        layout.addWidget(password_label)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("请输入密码")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {DARK_BLUE_TWO};
                border: 1px solid {LIGHT_BLUE};
                border-radius: 5px;
                padding: 10px;
                color: {TEXT_COLOR};
                font-size: 14px;
            }}
            QLineEdit:focus {{
                border: 1px solid {VERY_LIGHT_BLUE};
            }}
        """)
        self.password_input.setMinimumHeight(45)
        layout.addWidget(self.password_input)
    
    def create_login_button(self, layout):
        """创建登录按钮"""
        self.login_button = QPushButton("登录 →")
        self.login_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {MEDIUM_BLUE};
                color: {TEXT_COLOR};
                border-radius: 5px;
                padding: 10px;
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {LIGHT_BLUE};
            }}
            QPushButton:pressed {{
                background-color: {VERY_LIGHT_BLUE};
            }}
            QPushButton:disabled {{
                background-color: {DARK_BLUE_TWO};
                color: {LIGHT_BLUE};
            }}
        """)
        self.login_button.setMinimumHeight(45)
        self.login_button.setCursor(Qt.PointingHandCursor)
        layout.addWidget(self.login_button)
    
    def create_store_selection(self, layout):
        """创建店铺选择"""
        store_label = QLabel("— 选择店铺")
        store_label.setStyleSheet(f"color: {TEXT_COLOR}; font-size: 14px;")
        layout.addWidget(store_label)
        
        self.store_combo = QComboBox()
        self.store_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {DARK_BLUE_TWO};
                border: 1px solid {LIGHT_BLUE};
                border-radius: 5px;
                padding: 10px;
                color: {TEXT_COLOR};
                font-size: 14px;
            }}
            QComboBox:focus {{
                border: 1px solid {VERY_LIGHT_BLUE};
            }}
            QComboBox::drop-down {{
                border: none;
            }}
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {TEXT_COLOR};
            }}
        """)
        self.store_combo.setMinimumHeight(45)
        self.store_combo.setEnabled(False)
        layout.addWidget(self.store_combo)
    
    def setup_connections(self):
        """设置信号连接"""
        # 回车键登录
        self.username_input.returnPressed.connect(self.password_input.setFocus)
        self.password_input.returnPressed.connect(self.login)
        
        # 登录按钮
        self.login_button.clicked.connect(self.login)
    
    def login(self):
        """执行登录"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            self.show_message("错误", "用户名和密码不能为空")
            return
        
        # 禁用登录按钮并显示登录中状态
        self.login_button.setEnabled(False)
        self.login_button.setText("登录中...")
        
        # 创建并启动登录工作线程
        self.login_worker = LoginWorker(username, password, self.api_base_url)
        self.login_worker.login_success.connect(self.on_login_success)
        self.login_worker.login_failed.connect(self.on_login_failed)
        self.login_worker.start()
    
    def on_login_success(self, user_info, stores_info):
        """登录成功处理"""
        self.current_user = user_info
        self.stores = stores_info
        
        # 更新店铺选择框
        self.store_combo.clear()
        for store in stores_info:
            store_name = store.get("plg_shopname", "")
            if store_name:
                self.store_combo.addItem(store_name)
        
        if stores_info:
            self.store_combo.setEnabled(True)
        
        # 重新启用登录按钮
        self.login_button.setEnabled(True)
        self.login_button.setText("登录 →")
        
        # 发送登录成功信号
        self.login_success.emit(user_info, stores_info)
        
        self.show_message("成功", "登录成功！")
    
    def on_login_failed(self, error_message):
        """登录失败处理"""
        # 重新启用登录按钮
        self.login_button.setEnabled(True)
        self.login_button.setText("登录 →")
        
        self.show_message("登录失败", error_message)
    
    def show_message(self, title, message):
        """显示消息框"""
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {DARK_BLUE};
                color: {TEXT_COLOR};
            }}
            QMessageBox QPushButton {{
                background-color: {MEDIUM_BLUE};
                color: {TEXT_COLOR};
                border-radius: 5px;
                padding: 5px 15px;
                min-width: 60px;
            }}
            QMessageBox QPushButton:hover {{
                background-color: {LIGHT_BLUE};
            }}
        """)
        msg_box.exec()

def create_login_page():
    """创建登录页面"""
    return LoginPage()
