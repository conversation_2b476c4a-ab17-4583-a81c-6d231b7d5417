"""
文档处理器
处理各种格式的文档，包括PDF、Word、Excel、TXT等
"""

import os
import logging
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.settings_loader import get_setting, get_absolute_path
except ImportError as e:
    print(f"导入配置模块失败: {e}")
    def get_setting(key, default=None): return default
    def get_absolute_path(path): return path

# 配置日志
logger = logging.getLogger(__name__)

class DocumentProcessor:
    """文档处理器类"""
    
    def __init__(self):
        """初始化文档处理器"""
        # 支持的文件格式
        self.supported_formats = {
            '.txt': self._process_txt,
            '.md': self._process_txt,
            '.pdf': self._process_pdf,
            '.docx': self._process_docx,
            '.doc': self._process_doc,
            '.xlsx': self._process_xlsx,
            '.xls': self._process_xls,
            '.csv': self._process_csv
        }
        
        # 文本分块配置
        self.chunk_size = get_setting("vector_db.chunk_size", 1000)
        self.chunk_overlap = get_setting("vector_db.chunk_overlap", 200)
        
        logger.info("文档处理器已初始化")
    
    def process_documents(self, source_path: str) -> List[Dict[str, Any]]:
        """
        处理指定目录下的所有文档
        
        Args:
            source_path: 源文档目录路径
            
        Returns:
            List[Dict]: 处理后的文档列表
        """
        source_path = get_absolute_path(source_path)
        
        if not os.path.exists(source_path):
            logger.warning(f"源文档目录不存在: {source_path}")
            return []
        
        documents = []
        
        # 遍历目录中的所有文件
        for root, dirs, files in os.walk(source_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()
                
                if file_ext in self.supported_formats:
                    try:
                        logger.info(f"处理文档: {file_path}")
                        doc_data = self._process_single_document(file_path)
                        if doc_data:
                            documents.extend(doc_data)
                    except Exception as e:
                        logger.error(f"处理文档 {file_path} 时出错: {str(e)}")
                        continue
                else:
                    logger.debug(f"跳过不支持的文件格式: {file_path}")
        
        logger.info(f"文档处理完成，共处理 {len(documents)} 个文档块")
        return documents
    
    def _process_single_document(self, file_path: str) -> List[Dict[str, Any]]:
        """
        处理单个文档
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            List[Dict]: 文档块列表
        """
        file_ext = os.path.splitext(file_path)[1].lower()
        processor = self.supported_formats.get(file_ext)
        
        if not processor:
            logger.warning(f"不支持的文件格式: {file_ext}")
            return []
        
        try:
            # 提取文档内容
            content = processor(file_path)
            if not content:
                logger.warning(f"文档内容为空: {file_path}")
                return []
            
            # 分块处理
            chunks = self._split_text(content)
            
            # 创建文档块数据
            documents = []
            for i, chunk in enumerate(chunks):
                doc_data = {
                    'content': chunk,
                    'source': os.path.basename(file_path),
                    'source_path': file_path,
                    'chunk_id': i,
                    'total_chunks': len(chunks),
                    'file_hash': self._get_file_hash(file_path),
                    'metadata': {
                        'file_type': file_ext,
                        'file_size': os.path.getsize(file_path),
                        'chunk_index': i
                    }
                }
                documents.append(doc_data)
            
            return documents
            
        except Exception as e:
            logger.error(f"处理文档 {file_path} 时出错: {str(e)}")
            return []
    
    def _process_txt(self, file_path: str) -> str:
        """处理TXT和MD文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            for encoding in ['gbk', 'gb2312', 'latin-1']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        return f.read()
                except UnicodeDecodeError:
                    continue
            raise Exception(f"无法解码文件: {file_path}")
    
    def _process_pdf(self, file_path: str) -> str:
        """处理PDF文件"""
        try:
            import PyPDF2
            content = ""
            with open(file_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                for page in reader.pages:
                    content += page.extract_text() + "\n"
            return content
        except ImportError:
            logger.error("PyPDF2未安装，无法处理PDF文件")
            return ""
        except Exception as e:
            logger.error(f"处理PDF文件时出错: {str(e)}")
            return ""
    
    def _process_docx(self, file_path: str) -> str:
        """处理DOCX文件"""
        try:
            import docx
            doc = docx.Document(file_path)
            content = ""
            for paragraph in doc.paragraphs:
                content += paragraph.text + "\n"
            return content
        except ImportError:
            logger.error("python-docx未安装，无法处理DOCX文件")
            return ""
        except Exception as e:
            logger.error(f"处理DOCX文件时出错: {str(e)}")
            return ""
    
    def _process_doc(self, file_path: str) -> str:
        """处理DOC文件"""
        try:
            import win32com.client
            word = win32com.client.Dispatch("Word.Application")
            word.Visible = False
            doc = word.Documents.Open(file_path)
            content = doc.Content.Text
            doc.Close()
            word.Quit()
            return content
        except ImportError:
            logger.error("pywin32未安装，无法处理DOC文件")
            return ""
        except Exception as e:
            logger.error(f"处理DOC文件时出错: {str(e)}")
            return ""
    
    def _process_xlsx(self, file_path: str) -> str:
        """处理XLSX文件"""
        try:
            import pandas as pd
            df = pd.read_excel(file_path, sheet_name=None)
            content = ""
            for sheet_name, sheet_df in df.items():
                content += f"工作表: {sheet_name}\n"
                content += sheet_df.to_string(index=False) + "\n\n"
            return content
        except ImportError:
            logger.error("pandas未安装，无法处理XLSX文件")
            return ""
        except Exception as e:
            logger.error(f"处理XLSX文件时出错: {str(e)}")
            return ""
    
    def _process_xls(self, file_path: str) -> str:
        """处理XLS文件"""
        return self._process_xlsx(file_path)
    
    def _process_csv(self, file_path: str) -> str:
        """处理CSV文件"""
        try:
            import pandas as pd
            df = pd.read_csv(file_path)
            return df.to_string(index=False)
        except ImportError:
            logger.error("pandas未安装，无法处理CSV文件")
            return ""
        except Exception as e:
            logger.error(f"处理CSV文件时出错: {str(e)}")
            return ""
    
    def _split_text(self, text: str) -> List[str]:
        """
        将文本分割成块
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 文本块列表
        """
        if len(text) <= self.chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + self.chunk_size
            
            # 如果不是最后一块，尝试在句号、换行符等处分割
            if end < len(text):
                # 寻找合适的分割点
                for sep in ['\n\n', '\n', '。', '！', '？', '.', '!', '?']:
                    split_pos = text.rfind(sep, start, end)
                    if split_pos > start:
                        end = split_pos + len(sep)
                        break
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # 计算下一块的起始位置，考虑重叠
            start = max(start + 1, end - self.chunk_overlap)
        
        return chunks
    
    def _get_file_hash(self, file_path: str) -> str:
        """
        计算文件的MD5哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: MD5哈希值
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希时出错: {str(e)}")
            return ""
    
    def get_supported_formats(self) -> List[str]:
        """
        获取支持的文件格式列表
        
        Returns:
            List[str]: 支持的文件格式列表
        """
        return list(self.supported_formats.keys())
    
    def is_supported_format(self, file_path: str) -> bool:
        """
        检查文件格式是否支持
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持
        """
        file_ext = os.path.splitext(file_path)[1].lower()
        return file_ext in self.supported_formats
