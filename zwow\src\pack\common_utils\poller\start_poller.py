#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
启动轮询服务的独立模块
"""

import os
import sys
import logging
import time
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 确保能导入主程序模块
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.common_utils.poller.poller import StorePoller
except ImportError as e:
    logger.error(f"导入轮询模块失败: {str(e)}")
    # 尝试直接导入
    try:
        from .poller import StorePoller
    except ImportError:
        logger.error("无法导入StorePoller类")
        sys.exit(1)

def start_poller_service():
    """启动轮询服务"""
    try:
        logger.info("启动店铺状态轮询服务...")
        poller = StorePoller()
        poller.start()
        logger.info("轮询服务已启动，每30秒轮询一次")
        return True
    except Exception as e:
        logger.error(f"启动轮询服务时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("=== 轮询服务启动器 ===")
    
    # 启动轮询服务
    if start_poller_service():
        logger.info("轮询服务启动成功")
        
        try:
            logger.info("轮询服务正在运行，按Ctrl+C停止...")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("接收到停止信号，正在关闭轮询服务...")
    else:
        logger.error("轮询服务启动失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
