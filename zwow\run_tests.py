"""
测试运行脚本
用于快速测试应用程序的各个功能模块
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")
    
    required_packages = [
        'PySide6',
        'requests',
        'jieba'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✓ 所有依赖项都已安装")
    return True

def test_basic_imports():
    """测试基本导入"""
    print("\n测试基本导入...")
    
    try:
        # 测试Qt导入
        from PySide6.QtWidgets import QApplication
        print("✓ PySide6导入成功")
        
        # 测试项目核心模块导入
        from src.pack.config.settings_loader import get_setting
        print("✓ 配置模块导入成功")
        
        from src.pack.common_utils.database.app_db_manager import AppDBManager
        print("✓ 数据库模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本导入失败: {str(e)}")
        return False

def test_gui_startup():
    """测试GUI启动"""
    print("\n测试GUI启动...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.pack.frontend.main import MainWindow
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        window = MainWindow()
        print("✓ 主窗口创建成功")
        
        # 测试窗口显示（不实际显示）
        window.setVisible(False)
        print("✓ 窗口初始化成功")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI启动测试失败: {str(e)}")
        return False

def run_module_tests():
    """运行模块测试"""
    print("\n运行模块功能测试...")
    
    try:
        from tests.test_modules import run_all_tests
        return run_all_tests()
    except Exception as e:
        print(f"✗ 模块测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("ZWOW 应用程序测试")
    print("=" * 50)
    
    # 检查依赖项
    if not check_dependencies():
        return False
    
    # 测试基本导入
    if not test_basic_imports():
        return False
    
    # 测试GUI启动
    if not test_gui_startup():
        return False
    
    # 运行模块测试
    if not run_module_tests():
        return False
    
    print("\n" + "=" * 50)
    print("🎉 所有测试都通过了！应用程序可以正常运行。")
    print("=" * 50)
    
    # 询问是否启动应用程序
    try:
        response = input("\n是否启动应用程序？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            print("启动应用程序...")
            from src.pack.frontend.main import main as run_app
            run_app()
    except KeyboardInterrupt:
        print("\n测试完成。")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断。")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中出现未处理的错误: {str(e)}")
        sys.exit(1)
