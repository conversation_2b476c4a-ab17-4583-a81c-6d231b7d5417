"""
获客服务
整合批量发送和客户管理功能，提供统一的获客服务接口
"""

import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.settings_loader import get_setting
    from .bulk_sender import BulkSender, SendResult
    from .customer_manager import CustomerManager, Customer, MessageTemplate
except ImportError as e:
    print(f"导入模块失败: {e}")
    def get_setting(key, default=None): return default
    class BulkSender:
        def __init__(self): pass
        def send_bulk_messages(self, phones, message, progress_cb=None, completion_cb=None): return False
        def get_sending_status(self): return {}
        def cancel_sending(self): pass
    class CustomerManager:
        def __init__(self): pass
        def filter_valid_numbers(self, phones): return phones
        def get_statistics(self): return {}
    class SendResult: pass
    class Customer: pass
    class MessageTemplate: pass

# 配置日志
logger = logging.getLogger(__name__)

class AcquisitionService:
    """获客服务类"""
    
    def __init__(self):
        """初始化获客服务"""
        # 初始化组件
        self.bulk_sender = BulkSender()
        self.customer_manager = CustomerManager()
        
        # 服务配置
        self.auto_add_customers = get_setting("bulk_acquisition.auto_add_customers", True)
        self.default_customer_source = get_setting("bulk_acquisition.default_source", "bulk_acquisition")
        
        logger.info("获客服务已初始化")
    
    def send_bulk_messages(self, phone_numbers: List[str], message: str,
                          template_id: str = None,
                          progress_callback: Callable = None,
                          completion_callback: Callable = None) -> Dict[str, Any]:
        """
        发送批量消息
        
        Args:
            phone_numbers: 电话号码列表
            message: 消息内容
            template_id: 消息模板ID（可选）
            progress_callback: 进度回调函数
            completion_callback: 完成回调函数
            
        Returns:
            Dict[str, Any]: 发送结果
        """
        try:
            # 验证输入
            if not phone_numbers:
                return {
                    'success': False,
                    'error': '电话号码列表为空'
                }
            
            if not message and not template_id:
                return {
                    'success': False,
                    'error': '消息内容和模板ID不能同时为空'
                }
            
            # 如果使用模板，获取模板内容
            if template_id:
                template = self.customer_manager.get_message_template(template_id)
                if template:
                    message = template.content
                    # 增加模板使用次数
                    template.usage_count += 1
                else:
                    return {
                        'success': False,
                        'error': f'模板不存在: {template_id}'
                    }
            
            # 过滤黑名单号码
            valid_numbers = self.customer_manager.filter_valid_numbers(phone_numbers)
            filtered_count = len(phone_numbers) - len(valid_numbers)
            
            if not valid_numbers:
                return {
                    'success': False,
                    'error': '所有号码都在黑名单中'
                }
            
            # 自动添加客户
            if self.auto_add_customers:
                self._auto_add_customers(valid_numbers)
            
            # 包装回调函数
            wrapped_progress_callback = self._wrap_progress_callback(progress_callback)
            wrapped_completion_callback = self._wrap_completion_callback(completion_callback, valid_numbers)
            
            # 执行批量发送
            success = self.bulk_sender.send_bulk_messages(
                valid_numbers,
                message,
                wrapped_progress_callback,
                wrapped_completion_callback
            )
            
            if success:
                return {
                    'success': True,
                    'total_numbers': len(phone_numbers),
                    'valid_numbers': len(valid_numbers),
                    'filtered_count': filtered_count,
                    'message': '批量发送已启动'
                }
            else:
                return {
                    'success': False,
                    'error': '启动批量发送失败'
                }
                
        except Exception as e:
            logger.error(f"发送批量消息时出错: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _auto_add_customers(self, phone_numbers: List[str]):
        """
        自动添加客户
        
        Args:
            phone_numbers: 电话号码列表
        """
        try:
            added_count = 0
            for phone in phone_numbers:
                if not self.customer_manager.get_customer(phone):
                    success = self.customer_manager.add_customer(
                        phone_number=phone,
                        source=self.default_customer_source,
                        tags=["bulk_acquisition"]
                    )
                    if success:
                        added_count += 1
            
            if added_count > 0:
                logger.info(f"自动添加了 {added_count} 个新客户")
                
        except Exception as e:
            logger.error(f"自动添加客户时出错: {str(e)}")
    
    def _wrap_progress_callback(self, original_callback: Callable) -> Callable:
        """
        包装进度回调函数
        
        Args:
            original_callback: 原始回调函数
            
        Returns:
            Callable: 包装后的回调函数
        """
        def wrapped_callback(progress: int, status: str):
            try:
                if original_callback:
                    original_callback(progress, status)
            except Exception as e:
                logger.error(f"进度回调函数出错: {str(e)}")
        
        return wrapped_callback
    
    def _wrap_completion_callback(self, original_callback: Callable, phone_numbers: List[str]) -> Callable:
        """
        包装完成回调函数
        
        Args:
            original_callback: 原始回调函数
            phone_numbers: 电话号码列表
            
        Returns:
            Callable: 包装后的回调函数
        """
        def wrapped_callback(result: SendResult):
            try:
                # 更新客户联系记录
                if result:
                    self._update_customer_contact_records(phone_numbers, result)
                
                if original_callback:
                    original_callback(result)
            except Exception as e:
                logger.error(f"完成回调函数出错: {str(e)}")
        
        return wrapped_callback
    
    def _update_customer_contact_records(self, phone_numbers: List[str], result: SendResult):
        """
        更新客户联系记录
        
        Args:
            phone_numbers: 电话号码列表
            result: 发送结果
        """
        try:
            from datetime import datetime
            current_time = datetime.now().isoformat()
            
            for phone in phone_numbers:
                customer = self.customer_manager.get_customer(phone)
                if customer:
                    # 更新最后联系时间和联系次数
                    self.customer_manager.update_customer(
                        phone,
                        last_contact_time=current_time,
                        contact_count=customer.contact_count + 1
                    )
                    
                    # 如果发送成功，更新状态
                    task = next((t for t in result.tasks if t.phone_number == phone), None)
                    if task and task.status.value == "success":
                        if customer.status == "potential":
                            self.customer_manager.update_customer(phone, status="contacted")
            
            logger.info(f"更新了 {len(phone_numbers)} 个客户的联系记录")
            
        except Exception as e:
            logger.error(f"更新客户联系记录时出错: {str(e)}")
    
    def get_sending_status(self) -> Dict[str, Any]:
        """
        获取发送状态
        
        Returns:
            Dict[str, Any]: 发送状态
        """
        return self.bulk_sender.get_sending_status()
    
    def cancel_sending(self):
        """取消发送"""
        self.bulk_sender.cancel_sending()
    
    def add_customer(self, phone_number: str, name: str = "", source: str = "", 
                    tags: List[str] = None) -> bool:
        """
        添加客户
        
        Args:
            phone_number: 电话号码
            name: 客户姓名
            source: 来源
            tags: 标签列表
            
        Returns:
            bool: 是否添加成功
        """
        return self.customer_manager.add_customer(phone_number, name, source, tags)
    
    def get_customer(self, phone_number: str) -> Optional[Customer]:
        """
        获取客户信息
        
        Args:
            phone_number: 电话号码
            
        Returns:
            Optional[Customer]: 客户信息
        """
        return self.customer_manager.get_customer(phone_number)
    
    def add_to_blacklist(self, phone_number: str) -> bool:
        """
        添加到黑名单
        
        Args:
            phone_number: 电话号码
            
        Returns:
            bool: 是否添加成功
        """
        return self.customer_manager.add_to_blacklist(phone_number)
    
    def remove_from_blacklist(self, phone_number: str) -> bool:
        """
        从黑名单移除
        
        Args:
            phone_number: 电话号码
            
        Returns:
            bool: 是否移除成功
        """
        return self.customer_manager.remove_from_blacklist(phone_number)
    
    def add_message_template(self, name: str, content: str, category: str = "general", 
                           tags: List[str] = None) -> str:
        """
        添加消息模板
        
        Args:
            name: 模板名称
            content: 模板内容
            category: 模板分类
            tags: 标签列表
            
        Returns:
            str: 模板ID
        """
        return self.customer_manager.add_message_template(name, content, category, tags)
    
    def get_message_templates(self, category: str = None) -> List[MessageTemplate]:
        """
        获取消息模板列表
        
        Args:
            category: 模板分类（可选）
            
        Returns:
            List[MessageTemplate]: 模板列表
        """
        if category:
            return self.customer_manager.get_templates_by_category(category)
        else:
            return list(self.customer_manager.templates.values())
    
    def import_customers_from_csv(self, csv_file_path: str) -> int:
        """
        从CSV文件导入客户
        
        Args:
            csv_file_path: CSV文件路径
            
        Returns:
            int: 导入的客户数量
        """
        return self.customer_manager.import_customers_from_csv(csv_file_path)
    
    def export_customers_to_csv(self, csv_file_path: str, status_filter: str = None) -> bool:
        """
        导出客户到CSV文件
        
        Args:
            csv_file_path: CSV文件路径
            status_filter: 状态过滤器
            
        Returns:
            bool: 是否导出成功
        """
        return self.customer_manager.export_customers_to_csv(csv_file_path, status_filter)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        customer_stats = self.customer_manager.get_statistics()
        sending_status = self.bulk_sender.get_sending_status()
        
        return {
            'customer_stats': customer_stats,
            'sending_status': sending_status,
            'service_config': {
                'auto_add_customers': self.auto_add_customers,
                'default_source': self.default_customer_source
            }
        }
    
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            Dict[str, Any]: 健康状态
        """
        try:
            stats = self.get_statistics()
            
            return {
                'status': 'healthy',
                'components': {
                    'bulk_sender': 'ok',
                    'customer_manager': 'ok'
                },
                'statistics': stats
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
