/**
 * WhatsApp客户端
 * 基于whatsapp-web.js的WhatsApp集成客户端
 */

const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const fs = require('fs');
const path = require('path');

// 从环境变量获取配置
const STORE_NAME = process.env.STORE_NAME || 'default_store';
const STORE_DATA_DIR = process.env.STORE_DATA_DIR || './data/whatsapp_session';
const IPC_FILE = process.env.IPC_FILE || path.join(STORE_DATA_DIR, 'whatsapp_messages.json');
const REPLY_FILE = path.join(STORE_DATA_DIR, 'whatsapp_replies.json');

console.log(`启动WhatsApp客服服务 - 店铺: ${STORE_NAME}`);
console.log(`店铺数据目录: ${STORE_DATA_DIR}`);
console.log(`IPC文件路径: ${IPC_FILE}`);
console.log(`回复文件路径: ${REPLY_FILE}`);

// 确保目录存在
if (!fs.existsSync(STORE_DATA_DIR)) {
    fs.mkdirSync(STORE_DATA_DIR, { recursive: true });
}

// 清空或创建消息和回复文件
fs.writeFileSync(IPC_FILE, JSON.stringify([]));
fs.writeFileSync(REPLY_FILE, JSON.stringify([]));

// 创建WhatsApp客户端实例
const client = new Client({
    authStrategy: new LocalAuth({
        dataPath: path.join(STORE_DATA_DIR, 'session')
    }),
    puppeteer: {
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu'
        ]
    }
});

// 生成二维码
client.on('qr', (qr) => {
    console.log('请扫描二维码登录WhatsApp:');
    qrcode.generate(qr, { small: true });
    
    // 保存二维码到文件
    const qrFile = path.join(STORE_DATA_DIR, 'qr_code.txt');
    fs.writeFileSync(qrFile, qr);
    console.log(`二维码已保存到: ${qrFile}`);
});

// 存储接收到的消息
let receivedMessages = [];

// 将消息保存到IPC文件
function saveMessageToFile(message) {
    try {
        // 读取当前文件内容
        let messages = [];
        if (fs.existsSync(IPC_FILE)) {
            const fileContent = fs.readFileSync(IPC_FILE, 'utf8');
            if (fileContent.trim()) {
                messages = JSON.parse(fileContent);
            }
        }
        
        // 添加新消息
        messages.push({
            id: message.id._serialized,
            from: message.from,
            body: message.body,
            timestamp: Date.now(),
            type: message.type || 'chat'
        });
        
        // 写回文件
        fs.writeFileSync(IPC_FILE, JSON.stringify(messages, null, 2));
        console.log(`消息已保存到文件: ${message.from} - ${message.body}`);
    } catch (error) {
        console.error('保存消息到文件时出错:', error);
    }
}

// 检查并发送回复
function checkAndSendReplies() {
    try {
        if (!fs.existsSync(REPLY_FILE)) {
            return;
        }
        
        const fileContent = fs.readFileSync(REPLY_FILE, 'utf8');
        if (!fileContent.trim()) {
            return;
        }
        
        const replies = JSON.parse(fileContent);
        if (replies.length === 0) {
            return;
        }
        
        // 处理每个回复
        replies.forEach(async (reply) => {
            try {
                await client.sendMessage(reply.to, reply.message);
                console.log(`已发送回复到 ${reply.to}: ${reply.message}`);
            } catch (error) {
                console.error(`发送回复失败 ${reply.to}:`, error);
            }
        });
        
        // 清空回复文件
        fs.writeFileSync(REPLY_FILE, JSON.stringify([]));
        
    } catch (error) {
        console.error('检查回复时出错:', error);
    }
}

// 客户端准备加载中
client.on('loading_screen', (percent, message) => {
    console.log('加载中... ', percent, '%', message);
});

// 客户端已认证（不需要扫码）
client.on('authenticated', () => {
    console.log('认证成功!');
});

// 认证失败
client.on('auth_failure', (msg) => {
    console.error('认证失败:', msg);
});

// 客户端已准备好
client.on('ready', () => {
    console.log('客户端已准备就绪，开始监听新消息!');
    
    // 检查所有未读消息
    checkUnreadMessages();
    
    // 设置定时检查回复
    setInterval(checkAndSendReplies, 1000);
});

// 监听新消息
client.on('message', async (message) => {
    console.log(`收到来自 ${message.from} 的新消息: ${message.body}`);
    
    // 只处理文本消息，忽略系统消息和群组消息
    if (message.type === 'chat' && !message.from.includes('@g.us')) {
        // 保存消息到文件以便Python处理
        saveMessageToFile(message);
    }
});

// 监听断开连接事件
client.on('disconnected', (reason) => {
    console.log('客户端已断开连接:', reason);
    // 重连逻辑
    console.log('正在尝试重新连接...');
    setTimeout(() => {
        client.initialize();
    }, 5000);
});

// 检查并处理所有未读消息
async function checkUnreadMessages() {
    console.log('正在检查未读消息...');
    
    try {
        // 获取所有聊天
        const chats = await client.getChats();
        
        // 筛选出有未读消息的聊天
        const unreadChats = chats.filter(chat => chat.unreadCount > 0);
        
        console.log(`发现 ${unreadChats.length} 个聊天有未读消息`);
        
        // 处理每个有未读消息的聊天
        for (const chat of unreadChats) {
            console.log(`处理来自 ${chat.name} 的 ${chat.unreadCount} 条未读消息`);
            
            // 获取聊天历史消息
            const messages = await chat.fetchMessages({
                limit: chat.unreadCount
            });
            
            // 保存每条未读消息以便Python处理
            for (const message of messages) {
                if (message.type === 'chat' && !message.from.includes('@g.us')) {
                    saveMessageToFile(message);
                }
            }
        }
        
        console.log('所有未读消息已处理完毕');
        
    } catch (error) {
        console.error('处理未读消息时出错:', error);
    }
}

// 优雅关闭处理
process.on('SIGINT', () => {
    console.log('接收到关闭信号，正在关闭客户端...');
    client.destroy().then(() => {
        console.log('客户端已关闭');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('接收到终止信号，正在关闭客户端...');
    client.destroy().then(() => {
        console.log('客户端已关闭');
        process.exit(0);
    });
});

// 初始化客户端
client.initialize();

console.log(`WhatsApp客服服务已启动 - ${STORE_NAME}`);
