# ///////////////////////////////////////////////////////////////
#
# 作者: WANDERSON M.PIMENTA
# 项目基于: Qt Designer 和 PySide6
# 版本: 1.0.0
#
# 本项目可以免费用于任何用途，但必须在Python脚本中保留相应的版权信息。
# 可以自由修改可视化界面(GUI)中的任何信息，不会有任何影响。
#
# 如果您想将产品商业化，Qt许可证有一些限制，
# 建议您阅读官方网站的相关内容：
# https://doc.qt.io/qtforpython/licenses.html
#
# ///////////////////////////////////////////////////////////////

# 导入包和模块
# ///////////////////////////////////////////////////////////////
from qt_core import *
from gui.widgets import *
from gui.core.json_themes import Themes
from gui.core.functions import Functions
import sys
from pathlib import Path

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.config_manager import ConfigManager
    from src.pack.config.settings_loader import get_setting
except ImportError as e:
    print(f"导入配置管理器失败: {e}")
    class ConfigManager:
        def __init__(self): pass
        def get_config(self, key, default=None): return default
        def set_config(self, key, value): return True
        def save_config(self): return True
    def get_setting(key, default=None): return default

# 设置页面主类
# ///////////////////////////////////////////////////////////////
class SettingsPage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config_manager = ConfigManager()
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        # 加载主题
        themes = Themes()
        self.themes = themes.items
        
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        self.main_layout.setSpacing(20)
        
        # 页面标题
        self.setup_title()
        
        # 设置内容区域
        self.setup_content_area()
        
        # 底部按钮区域
        self.setup_bottom_buttons()
        
        # 设置整体样式
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {self.themes['app_color']['bg_one']};
                color: {self.themes['app_color']['text_foreground']};
            }}
        """)
        
    def setup_title(self):
        """设置页面标题"""
        self.title_label = QLabel("系统设置")
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet(f"""
            QLabel {{
                color: {self.themes['app_color']['text_foreground']};
                font-size: 24px;
                font-weight: bold;
                padding: 10px;
            }}
        """)
        self.main_layout.addWidget(self.title_label)
        
    def setup_content_area(self):
        """设置内容区域"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: {self.themes['app_color']['bg_one']};
            }}
            QScrollBar:vertical {{
                background-color: {self.themes['app_color']['bg_three']};
                width: 12px;
                border-radius: 6px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {self.themes['app_color']['context_color']};
                border-radius: 6px;
                min-height: 20px;
            }}
        """)
        
        # 内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)
        
        # 创建设置分组
        self.setup_basic_settings(content_layout)
        self.setup_api_settings(content_layout)
        self.setup_search_settings(content_layout)
        self.setup_chat_settings(content_layout)
        self.setup_bulk_settings(content_layout)
        
        # 添加弹性空间
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        self.main_layout.addWidget(scroll_area)
        
    def setup_basic_settings(self, parent_layout):
        """设置基础设置"""
        group = self.create_settings_group("基础设置")
        layout = group.layout()
        
        # 应用名称
        self.app_name_input = self.create_text_input("应用名称", "app_name")
        layout.addWidget(self.app_name_input)
        
        # 版本信息（只读）
        self.version_label = self.create_readonly_field("版本", "version")
        layout.addWidget(self.version_label)
        
        parent_layout.addWidget(group)
        
    def setup_api_settings(self, parent_layout):
        """设置API设置"""
        group = self.create_settings_group("API设置")
        layout = group.layout()
        
        # API基础URL
        self.api_url_input = self.create_text_input("API基础URL", "api.base_url")
        layout.addWidget(self.api_url_input)
        
        # API超时时间
        self.api_timeout_input = self.create_number_input("超时时间(秒)", "api.timeout", 1, 300)
        layout.addWidget(self.api_timeout_input)
        
        # API重试次数
        self.api_retry_input = self.create_number_input("重试次数", "api.retry_count", 0, 10)
        layout.addWidget(self.api_retry_input)
        
        # LLM API密钥
        self.llm_api_key_input = self.create_password_input("LLM API密钥", "llm.qwen.api_key")
        layout.addWidget(self.llm_api_key_input)
        
        parent_layout.addWidget(group)
        
    def setup_search_settings(self, parent_layout):
        """设置搜索设置"""
        group = self.create_settings_group("搜索设置")
        layout = group.layout()
        
        # BM25参数
        self.bm25_k1_input = self.create_decimal_input("BM25 K1参数", "search.bm25_k1", 0.1, 5.0)
        layout.addWidget(self.bm25_k1_input)
        
        self.bm25_b_input = self.create_decimal_input("BM25 B参数", "search.bm25_b", 0.0, 1.0)
        layout.addWidget(self.bm25_b_input)
        
        # 最大结果数
        self.max_results_input = self.create_number_input("最大结果数", "search.max_results", 1, 100)
        layout.addWidget(self.max_results_input)
        
        parent_layout.addWidget(group)
        
    def setup_chat_settings(self, parent_layout):
        """设置聊天设置"""
        group = self.create_settings_group("聊天设置")
        layout = group.layout()
        
        # WhatsApp会话路径
        self.whatsapp_path_input = self.create_text_input("WhatsApp会话路径", "chat.whatsapp.session_path")
        layout.addWidget(self.whatsapp_path_input)
        
        # 最大上下文长度
        self.context_length_input = self.create_number_input("最大上下文长度", "chat.message.max_context_length", 1000, 10000)
        layout.addWidget(self.context_length_input)
        
        # 自动翻译
        self.auto_translate_checkbox = self.create_checkbox("启用自动翻译", "chat.message.auto_translate")
        layout.addWidget(self.auto_translate_checkbox)
        
        parent_layout.addWidget(group)
        
    def setup_bulk_settings(self, parent_layout):
        """设置群发设置"""
        group = self.create_settings_group("群发获客设置")
        layout = group.layout()
        
        # 发送间隔
        self.send_interval_input = self.create_decimal_input("发送间隔(秒)", "bulk_acquisition.send_interval", 0.1, 60.0)
        layout.addWidget(self.send_interval_input)
        
        # 最大重试次数
        self.max_retries_input = self.create_number_input("最大重试次数", "bulk_acquisition.max_retries", 0, 10)
        layout.addWidget(self.max_retries_input)
        
        # 自动添加客户
        self.auto_add_customers_checkbox = self.create_checkbox("自动添加客户", "bulk_acquisition.auto_add_customers")
        layout.addWidget(self.auto_add_customers_checkbox)
        
        parent_layout.addWidget(group)
        
    def setup_bottom_buttons(self):
        """设置底部按钮"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 保存按钮
        self.save_btn = PyPushButton(
            text="保存设置",
            radius=8,
            color=self.themes['app_color']['white'],
            bg_color=self.themes['app_color']['green'],
            bg_color_hover=self.themes['app_color']['context_hover'],
            bg_color_pressed=self.themes['app_color']['context_pressed']
        )
        self.save_btn.setFixedHeight(40)
        self.save_btn.clicked.connect(self.save_settings)
        
        # 重置按钮
        self.reset_btn = PyPushButton(
            text="重置默认",
            radius=8,
            color=self.themes['app_color']['white'],
            bg_color=self.themes['app_color']['red'],
            bg_color_hover="#e74c3c",
            bg_color_pressed="#c0392b"
        )
        self.reset_btn.setFixedHeight(40)
        self.reset_btn.clicked.connect(self.reset_settings)
        
        # 导入按钮
        self.import_btn = PyPushButton(
            text="导入配置",
            radius=8,
            color=self.themes['app_color']['white'],
            bg_color=self.themes['app_color']['context_color'],
            bg_color_hover=self.themes['app_color']['context_hover'],
            bg_color_pressed=self.themes['app_color']['context_pressed']
        )
        self.import_btn.setFixedHeight(40)
        self.import_btn.clicked.connect(self.import_config)
        
        # 导出按钮
        self.export_btn = PyPushButton(
            text="导出配置",
            radius=8,
            color=self.themes['app_color']['white'],
            bg_color=self.themes['app_color']['context_color'],
            bg_color_hover=self.themes['app_color']['context_hover'],
            bg_color_pressed=self.themes['app_color']['context_pressed']
        )
        self.export_btn.setFixedHeight(40)
        self.export_btn.clicked.connect(self.export_config)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.reset_btn)
        button_layout.addWidget(self.import_btn)
        button_layout.addWidget(self.export_btn)
        button_layout.addStretch()
        
        self.main_layout.addLayout(button_layout)
        
    def create_settings_group(self, title: str) -> QGroupBox:
        """创建设置分组"""
        group = QGroupBox(title)
        group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 16px;
                font-weight: bold;
                color: {self.themes['app_color']['text_foreground']};
                border: 2px solid {self.themes['app_color']['dark_three']};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)
        
        layout = QVBoxLayout(group)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        return group
        
    def create_text_input(self, label: str, config_key: str) -> QWidget:
        """创建文本输入控件"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        
        label_widget = QLabel(label + ":")
        label_widget.setFixedWidth(150)
        label_widget.setStyleSheet(f"color: {self.themes['app_color']['text_foreground']};")
        
        input_widget = PyLineEdit(
            place_holder_text=f"请输入{label}",
            radius=6,
            border_size=1,
            color=self.themes['app_color']['text_foreground'],
            bg_color=self.themes['app_color']['bg_three'],
            bg_color_active=self.themes['app_color']['bg_two']
        )
        input_widget.setFixedHeight(35)
        
        # 存储配置键
        input_widget.config_key = config_key
        
        layout.addWidget(label_widget)
        layout.addWidget(input_widget)
        
        return container
        
    def create_number_input(self, label: str, config_key: str, min_val: int, max_val: int) -> QWidget:
        """创建数字输入控件"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        
        label_widget = QLabel(label + ":")
        label_widget.setFixedWidth(150)
        label_widget.setStyleSheet(f"color: {self.themes['app_color']['text_foreground']};")
        
        input_widget = QSpinBox()
        input_widget.setRange(min_val, max_val)
        input_widget.setFixedHeight(35)
        input_widget.setStyleSheet(f"""
            QSpinBox {{
                background-color: {self.themes['app_color']['bg_three']};
                border: 1px solid {self.themes['app_color']['dark_three']};
                border-radius: 6px;
                color: {self.themes['app_color']['text_foreground']};
                padding: 5px;
            }}
        """)
        
        # 存储配置键
        input_widget.config_key = config_key
        
        layout.addWidget(label_widget)
        layout.addWidget(input_widget)
        layout.addStretch()
        
        return container
        
    def create_decimal_input(self, label: str, config_key: str, min_val: float, max_val: float) -> QWidget:
        """创建小数输入控件"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        
        label_widget = QLabel(label + ":")
        label_widget.setFixedWidth(150)
        label_widget.setStyleSheet(f"color: {self.themes['app_color']['text_foreground']};")
        
        input_widget = QDoubleSpinBox()
        input_widget.setRange(min_val, max_val)
        input_widget.setDecimals(2)
        input_widget.setSingleStep(0.1)
        input_widget.setFixedHeight(35)
        input_widget.setStyleSheet(f"""
            QDoubleSpinBox {{
                background-color: {self.themes['app_color']['bg_three']};
                border: 1px solid {self.themes['app_color']['dark_three']};
                border-radius: 6px;
                color: {self.themes['app_color']['text_foreground']};
                padding: 5px;
            }}
        """)
        
        # 存储配置键
        input_widget.config_key = config_key
        
        layout.addWidget(label_widget)
        layout.addWidget(input_widget)
        layout.addStretch()
        
        return container
        
    def create_password_input(self, label: str, config_key: str) -> QWidget:
        """创建密码输入控件"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        
        label_widget = QLabel(label + ":")
        label_widget.setFixedWidth(150)
        label_widget.setStyleSheet(f"color: {self.themes['app_color']['text_foreground']};")
        
        input_widget = PyLineEdit(
            place_holder_text=f"请输入{label}",
            radius=6,
            border_size=1,
            color=self.themes['app_color']['text_foreground'],
            bg_color=self.themes['app_color']['bg_three'],
            bg_color_active=self.themes['app_color']['bg_two']
        )
        input_widget.setEchoMode(QLineEdit.Password)
        input_widget.setFixedHeight(35)
        
        # 存储配置键
        input_widget.config_key = config_key
        
        layout.addWidget(label_widget)
        layout.addWidget(input_widget)
        
        return container
        
    def create_checkbox(self, label: str, config_key: str) -> QWidget:
        """创建复选框控件"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        
        checkbox = QCheckBox(label)
        checkbox.setStyleSheet(f"""
            QCheckBox {{
                color: {self.themes['app_color']['text_foreground']};
                font-size: 14px;
            }}
            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
            }}
            QCheckBox::indicator:unchecked {{
                border: 2px solid {self.themes['app_color']['dark_three']};
                background-color: {self.themes['app_color']['bg_three']};
                border-radius: 3px;
            }}
            QCheckBox::indicator:checked {{
                border: 2px solid {self.themes['app_color']['context_color']};
                background-color: {self.themes['app_color']['context_color']};
                border-radius: 3px;
            }}
        """)
        
        # 存储配置键
        checkbox.config_key = config_key
        
        layout.addWidget(checkbox)
        layout.addStretch()
        
        return container
        
    def create_readonly_field(self, label: str, config_key: str) -> QWidget:
        """创建只读字段"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        
        label_widget = QLabel(label + ":")
        label_widget.setFixedWidth(150)
        label_widget.setStyleSheet(f"color: {self.themes['app_color']['text_foreground']};")
        
        value_widget = QLabel()
        value_widget.setStyleSheet(f"""
            QLabel {{
                background-color: {self.themes['app_color']['bg_three']};
                border: 1px solid {self.themes['app_color']['dark_three']};
                border-radius: 6px;
                color: {self.themes['app_color']['text_description']};
                padding: 8px;
            }}
        """)
        
        # 存储配置键
        value_widget.config_key = config_key
        
        layout.addWidget(label_widget)
        layout.addWidget(value_widget)
        
        return container
        
    def load_settings(self):
        """加载设置"""
        try:
            # 遍历所有输入控件并加载值
            for widget in self.findChildren(QWidget):
                if hasattr(widget, 'config_key'):
                    config_key = widget.config_key
                    value = self.config_manager.get_config(config_key)
                    
                    if isinstance(widget, PyLineEdit):
                        widget.setText(str(value) if value is not None else "")
                    elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                        if value is not None:
                            widget.setValue(value)
                    elif isinstance(widget, QCheckBox):
                        widget.setChecked(bool(value) if value is not None else False)
                    elif isinstance(widget, QLabel):  # 只读字段
                        widget.setText(str(value) if value is not None else "")
                        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载设置时出错: {str(e)}")
            
    def save_settings(self):
        """保存设置"""
        try:
            # 遍历所有输入控件并保存值
            for widget in self.findChildren(QWidget):
                if hasattr(widget, 'config_key'):
                    config_key = widget.config_key
                    
                    if isinstance(widget, PyLineEdit):
                        value = widget.text()
                    elif isinstance(widget, QSpinBox):
                        value = widget.value()
                    elif isinstance(widget, QDoubleSpinBox):
                        value = widget.value()
                    elif isinstance(widget, QCheckBox):
                        value = widget.isChecked()
                    else:
                        continue  # 跳过只读字段
                    
                    # 保存配置
                    self.config_manager.set_config(config_key, value)
            
            # 保存到文件
            if self.config_manager.save_config():
                QMessageBox.information(self, "成功", "设置已保存")
            else:
                QMessageBox.warning(self, "警告", "保存设置时出现问题")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存设置时出错: {str(e)}")
            
    def reset_settings(self):
        """重置设置"""
        reply = QMessageBox.question(
            self, "确认重置", 
            "确定要重置所有设置到默认值吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                if self.config_manager.reset_config():
                    self.load_settings()
                    QMessageBox.information(self, "成功", "设置已重置为默认值")
                else:
                    QMessageBox.warning(self, "警告", "重置设置时出现问题")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"重置设置时出错: {str(e)}")
                
    def import_config(self):
        """导入配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入配置文件", "", 
            "JSON文件 (*.json);;YAML文件 (*.yaml *.yml);;所有文件 (*)"
        )
        
        if file_path:
            try:
                if self.config_manager.import_config(file_path):
                    self.load_settings()
                    QMessageBox.information(self, "成功", "配置已导入")
                else:
                    QMessageBox.warning(self, "警告", "导入配置时出现问题")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导入配置时出错: {str(e)}")
                
    def export_config(self):
        """导出配置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出配置文件", "config.json",
            "JSON文件 (*.json);;YAML文件 (*.yaml);;所有文件 (*)"
        )
        
        if file_path:
            try:
                format_type = "yaml" if file_path.endswith(('.yaml', '.yml')) else "json"
                if self.config_manager.export_config(file_path, format_type):
                    QMessageBox.information(self, "成功", f"配置已导出到: {file_path}")
                else:
                    QMessageBox.warning(self, "警告", "导出配置时出现问题")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出配置时出错: {str(e)}")

# 创建设置页面实例的函数
# ///////////////////////////////////////////////////////////////
def create_settings_page():
    """创建设置页面实例"""
    return SettingsPage()
