# ///////////////////////////////////////////////////////////////
#
# 作者: WANDERSON M.PIMENTA
# 项目基于: Qt Designer 和 PySide6
# 版本: 1.0.0
#
# 本项目可以免费用于任何用途，但必须在Python脚本中保留相应的版权信息。
# 可以自由修改可视化界面(GUI)中的任何信息，不会有任何影响。
#
# 如果您想将产品商业化，Qt许可证有一些限制，
# 建议您阅读官方网站的相关内容：
# https://doc.qt.io/qtforpython/licenses.html
#
# ///////////////////////////////////////////////////////////////

# 导入包和模块
# ///////////////////////////////////////////////////////////////
from qt_core import *
from gui.widgets import *
from gui.core.json_themes import Themes
from gui.core.functions import Functions
import sys
from pathlib import Path

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.backend.search.search_service import SearchService
    from src.pack.backend.knowledge.knowledge_search import KnowledgeSearcher
except ImportError as e:
    print(f"导入搜索服务失败: {e}")
    class SearchService:
        def __init__(self): pass
        def search(self, query, **kwargs): return {'results': [], 'status': 'error'}
    class KnowledgeSearcher:
        def __init__(self): pass
        def search_knowledge(self, query): return "搜索服务未可用"

# 知识管理页面主类
# ///////////////////////////////////////////////////////////////
class KnowledgePage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.search_service = SearchService()
        self.knowledge_searcher = KnowledgeSearcher()
        self.setup_ui()
        
    def setup_ui(self):
        # 加载主题
        themes = Themes()
        self.themes = themes.items
        
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        self.main_layout.setSpacing(20)
        
        # 页面标题
        self.setup_title()
        
        # 搜索区域
        self.setup_search_area()
        
        # 结果显示区域
        self.setup_results_area()
        
        # 设置整体样式
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {self.themes['app_color']['bg_one']};
                color: {self.themes['app_color']['text_foreground']};
            }}
        """)
        
    def setup_title(self):
        """设置页面标题"""
        self.title_label = QLabel("知识管理与搜索")
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet(f"""
            QLabel {{
                color: {self.themes['app_color']['text_foreground']};
                font-size: 24px;
                font-weight: bold;
                padding: 10px;
            }}
        """)
        self.main_layout.addWidget(self.title_label)
        
    def setup_search_area(self):
        """设置搜索区域"""
        # 搜索容器
        search_container = QFrame()
        search_container.setStyleSheet(f"""
            QFrame {{
                background-color: {self.themes['app_color']['bg_two']};
                border-radius: 10px;
                border: 1px solid {self.themes['app_color']['dark_three']};
                padding: 20px;
            }}
        """)
        
        search_layout = QVBoxLayout(search_container)
        search_layout.setContentsMargins(20, 20, 20, 20)
        search_layout.setSpacing(15)
        
        # 搜索标题
        search_title = QLabel("知识搜索")
        search_title.setStyleSheet(f"""
            QLabel {{
                color: {self.themes['app_color']['text_foreground']};
                font-size: 18px;
                font-weight: bold;
            }}
        """)
        search_layout.addWidget(search_title)
        
        # 搜索输入区域
        input_layout = QHBoxLayout()
        
        # 搜索输入框
        self.search_input = PyLineEdit(
            place_holder_text="请输入要搜索的内容...",
            radius=8,
            border_size=2,
            color=self.themes['app_color']['text_foreground'],
            selection_color=self.themes['app_color']['text_foreground'],
            bg_color=self.themes['app_color']['bg_one'],
            bg_color_active=self.themes['app_color']['bg_three'],
            context_color=self.themes['app_color']['context_color']
        )
        self.search_input.setFixedHeight(45)
        self.search_input.returnPressed.connect(self.perform_search)
        
        # 搜索按钮
        self.search_btn = PyPushButton(
            text="搜索",
            radius=8,
            color=self.themes['app_color']['white'],
            bg_color=self.themes['app_color']['context_color'],
            bg_color_hover=self.themes['app_color']['context_hover'],
            bg_color_pressed=self.themes['app_color']['context_pressed']
        )
        self.search_btn.setFixedSize(80, 45)
        self.search_btn.clicked.connect(self.perform_search)
        
        input_layout.addWidget(self.search_input)
        input_layout.addWidget(self.search_btn)
        search_layout.addLayout(input_layout)
        
        # 搜索选项
        options_layout = QHBoxLayout()
        
        # 搜索类型选择
        self.search_type_combo = QComboBox()
        self.search_type_combo.addItems(["混合搜索", "向量搜索", "关键词搜索", "知识图谱"])
        self.search_type_combo.setFixedHeight(35)
        self.search_type_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {self.themes['app_color']['bg_three']};
                border: 1px solid {self.themes['app_color']['dark_three']};
                border-radius: 6px;
                color: {self.themes['app_color']['text_foreground']};
                padding: 5px;
                font-size: 14px;
            }}
            QComboBox::drop-down {{
                border: none;
            }}
            QComboBox::down-arrow {{
                image: url(icon_arrow_down.png);
                width: 12px;
                height: 12px;
            }}
        """)
        
        # 知识库类型选择
        self.kb_type_combo = QComboBox()
        self.kb_type_combo.addItems(["核心知识库", "辅助知识库"])
        self.kb_type_combo.setFixedHeight(35)
        self.kb_type_combo.setStyleSheet(self.search_type_combo.styleSheet())
        
        options_layout.addWidget(QLabel("搜索类型:"))
        options_layout.addWidget(self.search_type_combo)
        options_layout.addWidget(QLabel("知识库:"))
        options_layout.addWidget(self.kb_type_combo)
        options_layout.addStretch()
        
        search_layout.addLayout(options_layout)
        
        self.main_layout.addWidget(search_container)
        
    def setup_results_area(self):
        """设置结果显示区域"""
        # 结果容器
        results_container = QFrame()
        results_container.setStyleSheet(f"""
            QFrame {{
                background-color: {self.themes['app_color']['bg_two']};
                border-radius: 10px;
                border: 1px solid {self.themes['app_color']['dark_three']};
            }}
        """)
        
        results_layout = QVBoxLayout(results_container)
        results_layout.setContentsMargins(20, 20, 20, 20)
        results_layout.setSpacing(15)
        
        # 结果标题
        results_title = QLabel("搜索结果")
        results_title.setStyleSheet(f"""
            QLabel {{
                color: {self.themes['app_color']['text_foreground']};
                font-size: 18px;
                font-weight: bold;
            }}
        """)
        results_layout.addWidget(results_title)
        
        # 结果显示区域
        self.results_text = QTextEdit()
        self.results_text.setPlaceholderText("搜索结果将在这里显示...")
        self.results_text.setReadOnly(True)
        self.results_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {self.themes['app_color']['bg_one']};
                border: 1px solid {self.themes['app_color']['dark_three']};
                border-radius: 8px;
                color: {self.themes['app_color']['text_foreground']};
                padding: 15px;
                font-size: 14px;
                line-height: 1.5;
            }}
        """)
        
        results_layout.addWidget(self.results_text)
        
        self.main_layout.addWidget(results_container)
        
    def perform_search(self):
        """执行搜索"""
        query = self.search_input.text().strip()
        if not query:
            QMessageBox.warning(self, "输入错误", "请输入搜索内容")
            return
        
        # 获取搜索选项
        search_type_map = {
            "混合搜索": "hybrid",
            "向量搜索": "vector", 
            "关键词搜索": "bm25",
            "知识图谱": "knowledge"
        }
        
        kb_type_map = {
            "核心知识库": "core",
            "辅助知识库": "help"
        }
        
        search_type = search_type_map[self.search_type_combo.currentText()]
        kb_type = kb_type_map[self.kb_type_combo.currentText()]
        
        # 显示搜索中状态
        self.results_text.setText("正在搜索...")
        self.search_btn.setText("搜索中...")
        self.search_btn.setEnabled(False)
        
        # 执行搜索
        try:
            if search_type == "knowledge":
                # 使用知识搜索器
                result = self.knowledge_searcher.search_knowledge(query)
                self.results_text.setHtml(result.replace('\n', '<br>'))
            else:
                # 使用搜索服务
                result = self.search_service.search(
                    query=query,
                    search_type=search_type,
                    kb_type=kb_type,
                    top_k=5
                )
                
                if result.get('status') == 'success':
                    self.display_search_results(result)
                else:
                    self.results_text.setText(f"搜索失败: {result.get('error', '未知错误')}")
                    
        except Exception as e:
            self.results_text.setText(f"搜索出错: {str(e)}")
        
        finally:
            # 恢复按钮状态
            self.search_btn.setText("搜索")
            self.search_btn.setEnabled(True)
    
    def display_search_results(self, result):
        """显示搜索结果"""
        try:
            results = result.get('results', [])
            query = result.get('query', '')
            search_type = result.get('search_type', '')
            search_time = result.get('search_time_ms', 0)
            
            if not results:
                self.results_text.setText("未找到相关结果")
                return
            
            # 构建结果HTML
            html = f"""
            <div style="margin-bottom: 20px;">
                <h3 style="color: {self.themes['app_color']['context_color']};">
                    搜索查询: "{query}"
                </h3>
                <p style="color: {self.themes['app_color']['text_description']};">
                    搜索类型: {search_type} | 找到 {len(results)} 个结果 | 耗时: {search_time:.1f}ms
                </p>
            </div>
            """
            
            for i, item in enumerate(results, 1):
                content = item.get('content', item.get('content_snippet', ''))
                score = item.get('score', 0)
                source = item.get('source', '未知来源')
                
                html += f"""
                <div style="margin-bottom: 15px; padding: 15px; 
                           background-color: {self.themes['app_color']['bg_three']}; 
                           border-radius: 8px; border-left: 4px solid {self.themes['app_color']['context_color']};">
                    <h4 style="color: {self.themes['app_color']['text_foreground']}; margin: 0 0 10px 0;">
                        结果 {i} (相关度: {score:.3f})
                    </h4>
                    <p style="color: {self.themes['app_color']['text_description']}; margin: 0 0 10px 0;">
                        来源: {source}
                    </p>
                    <p style="color: {self.themes['app_color']['text_foreground']}; margin: 0; line-height: 1.6;">
                        {content[:500]}{'...' if len(content) > 500 else ''}
                    </p>
                </div>
                """
            
            self.results_text.setHtml(html)
            
        except Exception as e:
            self.results_text.setText(f"显示结果时出错: {str(e)}")

# 创建知识管理页面实例的函数
# ///////////////////////////////////////////////////////////////
def create_knowledge_page():
    """创建知识管理页面实例"""
    return KnowledgePage()
