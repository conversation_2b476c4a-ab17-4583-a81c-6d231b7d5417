"""
意图分析数据操作模块
提供intent_analysis表的CRUD操作
"""
import logging
import sqlite3
import time
from typing import Dict, List, Tuple, Optional, Any
from .db_manager import DatabaseManager

logger = logging.getLogger(__name__)

class IntentOperations:
    """意图分析数据操作类"""
    
    def __init__(self):
        """初始化意图分析数据操作类"""
        self.db_manager = DatabaseManager()
        self._conn = None
    
    def _ensure_connection(self, max_retries=3):
        """
        确保数据库连接可用，支持重试
        
        Args:
            max_retries: 最大重试次数
            
        Returns:
            bool: 连接是否成功
        """
        retry_count = 0
        while retry_count < max_retries:
            if self.db_manager.connect():
                logger.info("数据库连接成功")
                return True
            else:
                retry_count += 1
                logger.warning(f"数据库连接失败，尝试重连 ({retry_count}/{max_retries})")
                time.sleep(0.5)  # 短暂延迟后重试
        
        logger.error(f"数据库连接失败，已重试 {max_retries} 次")
        return False
    
    def save_intent_analysis(self, store_name: str, sender_id: str, intent_result: Dict[str, Any], 
                           message: str = '', context: str = '', manual: int = 0, username: str = '') -> bool:
        """
        保存意图分析结果
        如果存在相同store_name和sender_id的记录，则更新该记录，否则插入新记录
        
        Args:
            store_name: 店铺名称
            sender_id: 发送者ID
            intent_result: 意图分析结果字典，包含stage、specific_intent、reasoning和source字段
            message: 用户消息内容
            context: 历史消息上下文
            manual: 是否手动设置的意图，0表示自动分析，1表示手动设置
            username: 产生这条数据的wowcker账户名称，如果为空则尝试从users表获取
            
        Returns:
            bool: 操作是否成功
        """
        if not self._ensure_connection():
            return False
        
        try:
            # 从intent_result中提取字段
            stage = intent_result.get('stage', '')
            specific_intent = intent_result.get('specific_intent', '')
            reasoning = intent_result.get('reasoning', '')
            source = intent_result.get('source', '')
            
            # 如果username为空，尝试从users表中获取当前登录用户
            if not username:
                try:
                    # 尝试获取users表中的第一个用户名
                    self.db_manager.cursor.execute("SELECT plg_usn FROM users LIMIT 1")
                    user_record = self.db_manager.cursor.fetchone()
                    if user_record:
                        username = user_record[0]
                        logger.info(f"从users表获取用户名: {username}")
                    else:
                        username = 'none'  # 如果users表为空，使用默认值
                        logger.warning("未能从users表获取用户名，使用默认值'none'")
                except Exception as e:
                    logger.error(f"获取用户名时出错: {str(e)}")
                    username = 'test_user'  # 出错时使用默认值
            
            # 检查是否存在相同的记录
            check_query = '''
            SELECT id FROM intent_analysis 
            WHERE store_name = ? AND sender_id = ?
            '''
            
            self.db_manager.cursor.execute(check_query, (store_name, sender_id))
            existing_record = self.db_manager.cursor.fetchone()
            
            if existing_record:
                # 存在记录，执行更新
                record_id = existing_record[0]
                update_query = '''
                UPDATE intent_analysis 
                SET stage = ?, specific_intent = ?, reasoning = ?, 
                    source = ?, message = ?, context = ?, manual = ?, username = ?,
                    created_at = CURRENT_TIMESTAMP 
                WHERE id = ?
                '''
                
                update_params = (stage, specific_intent, reasoning, source, message, context, manual, username, record_id)
                self.db_manager.cursor.execute(update_query, update_params)
                self.db_manager.conn.commit()
                logger.info(f"成功更新意图分析结果: {store_name}, {sender_id}, ID: {record_id}")
            else:
                # 不存在记录，执行插入
                insert_query = '''
                INSERT INTO intent_analysis (store_name, sender_id, stage, specific_intent, reasoning, source, message, context, manual, username)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                '''
                
                insert_params = (store_name, sender_id, stage, specific_intent, reasoning, source, message, context, manual, username)
                self.db_manager.cursor.execute(insert_query, insert_params)
                self.db_manager.conn.commit()
                logger.info(f"成功插入意图分析结果: {store_name}, {sender_id}")
            
            return True
        except sqlite3.Error as e:
            logger.error(f"保存意图分析结果时出错: {str(e)}")
            self.db_manager.conn.rollback()
            return False
        finally:
            self.db_manager.close()
    
    def get_intent_analysis_by_sender(self, store_name: str, sender_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取指定发送者的意图分析历史
        
        Args:
            store_name: 店铺名称
            sender_id: 发送者ID
            limit: 返回结果数量限制
            
        Returns:
            List[Dict]: 意图分析记录列表
        """
        if not self._ensure_connection():
            return []
        
        try:
            query = '''
            SELECT id, store_name, sender_id, stage, specific_intent, reasoning, 
                   source, message, context, manual, username, created_at
            FROM intent_analysis
            WHERE store_name = ? AND sender_id = ?
            ORDER BY created_at DESC
            LIMIT ?
            '''
            
            self.db_manager.cursor.execute(query, (store_name, sender_id, limit))
            results = self.db_manager.cursor.fetchall()
            
            intent_records = []
            for row in results:
                intent_records.append({
                    'id': row[0],
                    'store_name': row[1],
                    'sender_id': row[2],
                    'stage': row[3],
                    'specific_intent': row[4],
                    'reasoning': row[5],
                    'source': row[6],
                    'message': row[7],
                    'context': row[8],
                    'manual': row[9],
                    'username': row[10],
                    'created_at': row[11]
                })
            
            return intent_records
        except sqlite3.Error as e:
            logger.error(f"获取意图分析记录时出错: {str(e)}")
            return []
        finally:
            self.db_manager.close()
    
    def get_latest_intent_analysis(self, store_name: str, sender_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定发送者的最新意图分析结果
        
        Args:
            store_name: 店铺名称
            sender_id: 发送者ID
            
        Returns:
            Optional[Dict]: 最新的意图分析记录
        """
        records = self.get_intent_analysis_by_sender(store_name, sender_id, limit=1)
        return records[0] if records else None
    
    def get_intent_analysis_by_store(self, store_name: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取指定店铺的意图分析历史
        
        Args:
            store_name: 店铺名称
            limit: 返回结果数量限制
            
        Returns:
            List[Dict]: 意图分析记录列表
        """
        if not self._ensure_connection():
            return []
        
        try:
            query = '''
            SELECT id, store_name, sender_id, stage, specific_intent, reasoning, 
                   source, message, context, manual, username, created_at
            FROM intent_analysis
            WHERE store_name = ?
            ORDER BY created_at DESC
            LIMIT ?
            '''
            
            self.db_manager.cursor.execute(query, (store_name, limit))
            results = self.db_manager.cursor.fetchall()
            
            intent_records = []
            for row in results:
                intent_records.append({
                    'id': row[0],
                    'store_name': row[1],
                    'sender_id': row[2],
                    'stage': row[3],
                    'specific_intent': row[4],
                    'reasoning': row[5],
                    'source': row[6],
                    'message': row[7],
                    'context': row[8],
                    'manual': row[9],
                    'username': row[10],
                    'created_at': row[11]
                })
            
            return intent_records
        except sqlite3.Error as e:
            logger.error(f"获取店铺意图分析记录时出错: {str(e)}")
            return []
        finally:
            self.db_manager.close()
    
    def delete_intent_analysis(self, store_name: str, sender_id: str) -> bool:
        """
        删除指定发送者的意图分析记录
        
        Args:
            store_name: 店铺名称
            sender_id: 发送者ID
            
        Returns:
            bool: 操作是否成功
        """
        if not self._ensure_connection():
            return False
        
        try:
            query = '''
            DELETE FROM intent_analysis
            WHERE store_name = ? AND sender_id = ?
            '''
            
            self.db_manager.cursor.execute(query, (store_name, sender_id))
            self.db_manager.conn.commit()
            
            deleted_count = self.db_manager.cursor.rowcount
            logger.info(f"已删除 {deleted_count} 条意图分析记录")
            return True
        except sqlite3.Error as e:
            logger.error(f"删除意图分析记录时出错: {str(e)}")
            self.db_manager.conn.rollback()
            return False
        finally:
            self.db_manager.close()
    
    def delete_old_intent_analysis(self, store_name: str, days: int = 30) -> bool:
        """
        删除指定天数之前的意图分析记录
        
        Args:
            store_name: 店铺名称
            days: 保留天数
            
        Returns:
            bool: 操作是否成功
        """
        if not self._ensure_connection():
            return False
        
        try:
            query = '''
            DELETE FROM intent_analysis
            WHERE store_name = ? AND created_at < datetime('now', ?)
            '''
            
            days_param = f"-{days} days"
            self.db_manager.cursor.execute(query, (store_name, days_param))
            self.db_manager.conn.commit()
            
            deleted_count = self.db_manager.cursor.rowcount
            logger.info(f"已删除 {deleted_count} 条过期的意图分析记录")
            return True
        except sqlite3.Error as e:
            logger.error(f"删除过期意图分析记录时出错: {str(e)}")
            self.db_manager.conn.rollback()
            return False
        finally:
            self.db_manager.close()
    
    def get_intent_statistics(self, store_name: str) -> Dict[str, int]:
        """
        获取意图分析统计信息
        
        Args:
            store_name: 店铺名称
            
        Returns:
            Dict[str, int]: 统计信息字典
        """
        if not self._ensure_connection():
            return {}
        
        try:
            # 获取总记录数
            total_query = "SELECT COUNT(*) FROM intent_analysis WHERE store_name = ?"
            self.db_manager.cursor.execute(total_query, (store_name,))
            total_count = self.db_manager.cursor.fetchone()[0]
            
            # 获取各阶段统计
            stage_query = '''
            SELECT stage, COUNT(*) 
            FROM intent_analysis 
            WHERE store_name = ? 
            GROUP BY stage
            '''
            self.db_manager.cursor.execute(stage_query, (store_name,))
            stage_results = self.db_manager.cursor.fetchall()
            
            statistics = {
                'total_count': total_count,
                'stage_counts': {row[0]: row[1] for row in stage_results}
            }
            
            return statistics
        except sqlite3.Error as e:
            logger.error(f"获取意图分析统计信息时出错: {str(e)}")
            return {}
        finally:
            self.db_manager.close()
