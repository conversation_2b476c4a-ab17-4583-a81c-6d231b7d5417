"""
WhatsApp聊天服务
基于chat_interface实现的WhatsApp集成服务
"""

import os
import json
import time
import logging
import subprocess
import threading
from pathlib import Path
from typing import Dict, List, Any, Optional
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from .chat_interface import ChatInterface
    from .message_handler import MessageHandler
    from src.pack.config.settings_loader import get_setting
except ImportError as e:
    print(f"导入错误: {e}")
    # 创建模拟类以避免错误
    class ChatInterface:
        def __init__(self, store_name, aggregation_timeout=10):
            self.store_name = store_name
            self.is_running = False
        def _platform_initialize(self): pass
    class MessageHandler:
        def __init__(self, store_name, username=None): pass
        def process_message(self, message): return "Hello!"
    def get_setting(key, default=None): return default

logger = logging.getLogger(__name__)

class WhatsAppService(ChatInterface):
    """WhatsApp聊天客服服务类"""
    
    def __init__(self, store_name: str, username: str = None, aggregation_timeout: int = 10):
        """
        初始化WhatsApp客服服务

        Args:
            store_name: 店铺名称
            username: 用户名
            aggregation_timeout: 消息聚合超时时间（秒），默认10秒
        """
        self.store_name = store_name
        self.username = username or "default_user"
        self.whatsapp_process = None
        self.message_handler = MessageHandler(store_name, self.username)
        
        # 文件路径配置
        self.data_dir = self._get_data_dir()
        self.ipc_file = os.path.join(self.data_dir, 'whatsapp_messages.json')
        self.reply_file = os.path.join(self.data_dir, 'whatsapp_replies.json')
        
        # 监听线程
        self.monitor_thread = None
        self.stop_monitoring = False
        
        logger.info(f"初始化WhatsApp服务，店铺: {store_name}, 用户名: {self.username}")
        
        # 调用父类初始化方法
        super().__init__(store_name, aggregation_timeout)
    
    def _platform_initialize(self) -> None:
        """平台特定的初始化操作"""
        # 确保数据目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 初始化IPC文件
        self._init_ipc_files()
        
        logger.info(f"WhatsApp平台初始化完成，数据目录: {self.data_dir}")
    
    def _get_data_dir(self) -> str:
        """获取数据存储目录"""
        base_dir = get_setting("chat.whatsapp.session_path", "data/whatsapp_session")
        # 为每个用户和店铺创建独立目录
        user_store_dir = f"{self.username}_{self.store_name}"
        return os.path.join(base_dir, user_store_dir)
    
    def _init_ipc_files(self) -> None:
        """初始化IPC文件"""
        try:
            # 创建空的消息和回复文件
            with open(self.ipc_file, 'w', encoding='utf-8') as f:
                json.dump([], f)
            
            with open(self.reply_file, 'w', encoding='utf-8') as f:
                json.dump([], f)
                
            logger.info("IPC文件初始化完成")
        except Exception as e:
            logger.error(f"初始化IPC文件时出错: {str(e)}")
    
    def start(self) -> bool:
        """启动WhatsApp服务"""
        if self.is_running:
            logger.warning("WhatsApp服务已在运行")
            return True
        
        try:
            # 启动WhatsApp客户端进程
            if not self._start_whatsapp_client():
                return False
            
            # 启动消息监听线程
            self._start_message_monitor()
            
            self.is_running = True
            logger.info("WhatsApp服务启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动WhatsApp服务时出错: {str(e)}")
            return False
    
    def stop(self) -> bool:
        """停止WhatsApp服务"""
        if not self.is_running:
            logger.warning("WhatsApp服务未在运行")
            return True
        
        try:
            # 停止消息监听
            self._stop_message_monitor()
            
            # 停止WhatsApp客户端进程
            self._stop_whatsapp_client()
            
            self.is_running = False
            logger.info("WhatsApp服务已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止WhatsApp服务时出错: {str(e)}")
            return False
    
    def _start_whatsapp_client(self) -> bool:
        """启动WhatsApp客户端进程"""
        try:
            # WhatsApp客户端脚本路径
            client_script = os.path.join(
                os.path.dirname(__file__), 
                "whatsapp", 
                "whatsapp_client.js"
            )
            
            if not os.path.exists(client_script):
                logger.error(f"WhatsApp客户端脚本不存在: {client_script}")
                return False
            
            # 设置环境变量
            env = os.environ.copy()
            env['STORE_NAME'] = self.store_name
            env['STORE_DATA_DIR'] = self.data_dir
            env['IPC_FILE'] = self.ipc_file
            
            # 启动Node.js进程
            self.whatsapp_process = subprocess.Popen(
                ['node', client_script],
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            logger.info(f"WhatsApp客户端进程已启动，PID: {self.whatsapp_process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"启动WhatsApp客户端时出错: {str(e)}")
            return False
    
    def _stop_whatsapp_client(self) -> None:
        """停止WhatsApp客户端进程"""
        if self.whatsapp_process:
            try:
                self.whatsapp_process.terminate()
                self.whatsapp_process.wait(timeout=10)
                logger.info("WhatsApp客户端进程已停止")
            except subprocess.TimeoutExpired:
                self.whatsapp_process.kill()
                logger.warning("强制终止WhatsApp客户端进程")
            except Exception as e:
                logger.error(f"停止WhatsApp客户端时出错: {str(e)}")
            finally:
                self.whatsapp_process = None
    
    def _start_message_monitor(self) -> None:
        """启动消息监听线程"""
        self.stop_monitoring = False
        self.monitor_thread = threading.Thread(target=self._monitor_messages)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info("消息监听线程已启动")
    
    def _stop_message_monitor(self) -> None:
        """停止消息监听线程"""
        self.stop_monitoring = True
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        logger.info("消息监听线程已停止")
    
    def _monitor_messages(self) -> None:
        """监听消息文件变化"""
        last_message_count = 0
        
        while not self.stop_monitoring:
            try:
                if os.path.exists(self.ipc_file):
                    with open(self.ipc_file, 'r', encoding='utf-8') as f:
                        messages = json.load(f)
                    
                    # 检查是否有新消息
                    if len(messages) > last_message_count:
                        new_messages = messages[last_message_count:]
                        for message in new_messages:
                            self.buffer_message(message)
                        last_message_count = len(messages)
                
                time.sleep(1)  # 每秒检查一次
                
            except Exception as e:
                logger.error(f"监听消息时出错: {str(e)}")
                time.sleep(5)  # 出错时等待更长时间
    
    def _get_sender_id(self, message: Dict[str, Any]) -> str:
        """从消息中提取发送者ID"""
        return message.get("from", "")
    
    def _get_message_content(self, message: Dict[str, Any]) -> str:
        """从消息中提取内容"""
        return message.get("body", "")
    
    def _combine_messages(self, messages: List[Dict[str, Any]]) -> str:
        """合并多条消息内容"""
        combined = []
        for msg in messages:
            content = self._get_message_content(msg)
            if content:
                combined.append(content)
        return "\n".join(combined)
    
    def _create_aggregated_message(self, sender_id: str, content: str) -> Dict[str, Any]:
        """创建聚合后的消息对象"""
        return {
            "from": sender_id,
            "body": content,
            "timestamp": int(time.time()),
            "aggregated": True
        }
    
    def process_message(self, message: Dict[str, Any]) -> None:
        """处理消息并生成回复"""
        try:
            # 使用消息处理器处理消息
            reply = self.message_handler.process_message(message)
            
            if reply:
                sender_id = self._get_sender_id(message)
                self.send_reply(sender_id, reply)
            
        except Exception as e:
            logger.error(f"处理消息时出错: {str(e)}")
    
    def send_reply(self, recipient_id: str, message: str) -> None:
        """发送回复消息"""
        try:
            # 读取现有回复
            replies = []
            if os.path.exists(self.reply_file):
                with open(self.reply_file, 'r', encoding='utf-8') as f:
                    replies = json.load(f)
            
            # 添加新回复
            reply_data = {
                "to": recipient_id,
                "message": message,
                "timestamp": int(time.time())
            }
            replies.append(reply_data)
            
            # 写回文件
            with open(self.reply_file, 'w', encoding='utf-8') as f:
                json.dump(replies, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已发送回复给 {recipient_id}: {message}")
            
        except Exception as e:
            logger.error(f"发送回复时出错: {str(e)}")
    
    def get_client_status(self) -> Dict[str, Any]:
        """获取客户端状态"""
        status = self.get_status()
        status.update({
            "whatsapp_process_running": self.whatsapp_process is not None,
            "monitor_thread_active": self.monitor_thread and self.monitor_thread.is_alive(),
            "data_dir": self.data_dir,
            "ipc_file_exists": os.path.exists(self.ipc_file),
            "reply_file_exists": os.path.exists(self.reply_file)
        })
        return status
