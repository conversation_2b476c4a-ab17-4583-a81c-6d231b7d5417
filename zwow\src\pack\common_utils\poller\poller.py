#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
轮询模块，用于定期向平台发送请求获取店铺状态
"""

import os
import sys
import json
import time
import logging
import requests
import threading
import sqlite3
from datetime import datetime
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入数据库模块
from ..database.app_db_manager import AppDBManager

class StorePoller:
    """轮询店铺状态更新类"""
    
    def __init__(self, polling_interval=30, api_base_url="http://47.120.74.30:9090/api"):
        """
        初始化轮询器
        
        Args:
            polling_interval (int): 轮询间隔，单位秒
            api_base_url (str): API基础URL
        """
        self.polling_interval = polling_interval
        self.api_base_url = api_base_url
        self.db_manager = AppDBManager()
        self.db_manager.initialize()
        self.running = False
        self.poll_thread = None
        
    def start(self):
        """
        启动轮询线程
        
        Returns:
            bool: 启动是否成功
        """
        if self.running:
            logger.warning("轮询器已经在运行")
            return False
            
        self.running = True
        self.poll_thread = threading.Thread(target=self._polling_task)
        self.poll_thread.daemon = True
        self.poll_thread.start()
        logger.info("轮询器已启动，间隔：%d秒", self.polling_interval)
        return True
    
    def stop(self):
        """停止轮询线程"""
        if not self.running:
            logger.warning("轮询器未在运行")
            return
            
        self.running = False
        if self.poll_thread and self.poll_thread.is_alive():
            self.poll_thread.join(timeout=5)
        logger.info("轮询器已停止")
    
    def _polling_task(self):
        """轮询任务主循环"""
        logger.info("轮询任务已启动")
        last_execution_time = 0
        
        while self.running:
            try:
                current_time = time.time()
                # 记录执行时间，用于监控轮询间隔
                if last_execution_time > 0:
                    elapsed = current_time - last_execution_time
                    logger.info(f"距离上次轮询已经过去 {elapsed:.1f} 秒")
                
                last_execution_time = current_time
                
                # 执行轮询并记录结果
                success = self._poll_once()
                if success:
                    logger.info("轮询执行成功")
                else:
                    logger.warning("轮询执行失败，将在下一周期重试")
                    
                # 验证积分重置状态
                self._verify_points_reset()
                    
            except Exception as e:
                logger.error(f"轮询过程中发生错误: {str(e)}")
                
            # 等待下一次轮询
            logger.info(f"等待 {self.polling_interval} 秒后进行下一次轮询")
            for i in range(self.polling_interval):
                if not self.running:
                    break
                if i > 0 and i % 10 == 0:  # 每10秒打印一次等待状态
                    logger.info(f"轮询器等待中... 还剩 {self.polling_interval - i} 秒")
                time.sleep(1)
    
    def _poll_once(self):
        """
        执行一次轮询操作，获取店铺状态并更新
        
        Returns:
            bool: 轮询是否成功
        """
        logger.info("执行轮询操作...")
        
        # 获取当前用户信息（在users表中只有一条记录）
        user = self._get_current_user()
        if not user:
            logger.error("找不到当前用户，轮询操作取消")
            return False
        
        # 获取所有店铺信息
        stores = self._get_all_stores()
        if not stores:
            logger.warning("没有找到任何店铺信息")
            return False
            
        # 准备请求数据
        request_data = {
            "plg_usn": user["plg_usn"],
            "stores": [
                {
                    "plg_shopname": store["plg_shopname"],
                    "plg_points": store["plg_points"]
                } for store in stores
            ]
        }
        
        # 发送请求到平台
        logger.info("正在向平台发送积分数据...")
        response = self._send_request_to_platform(request_data)
        
        # 确保只在请求成功后才重置积分
        if response and isinstance(response, dict):
            logger.info("平台请求成功，正在重置店铺积分...")
            
            # 确保重置积分成功
            if not self._reset_all_store_points():
                logger.error("重置店铺积分失败，但平台请求已成功")
                # 继续处理响应数据，因为平台请求已成功
            else:
                logger.info("成功重置店铺积分为0")
            
            # 处理响应数据，更新店铺状态
            return self._update_store_status(response.get("stores", []))
        else:
            logger.error("平台请求失败，保留当前积分，不进行重置")
            return False
    
    def _get_current_user(self):
        """
        获取当前用户信息（users表中唯一的记录）
        
        Returns:
            dict: 用户信息，如果没有找到则返回None
        """
        try:
            conn = self.db_manager.db_manager.conn
            if not conn:
                self.db_manager.db_manager.connect()
                conn = self.db_manager.db_manager.conn
                
            cursor = conn.cursor()
            cursor.execute("SELECT id, plg_usn, plg_pwd FROM users LIMIT 1")
            user = cursor.fetchone()
            
            if user:
                return {
                    "id": user[0],
                    "plg_usn": user[1],
                    "plg_pwd": user[2]
                }
            return None
            
        except sqlite3.Error as e:
            logger.error(f"查询用户数据时出错: {str(e)}")
            return None
        finally:
            if hasattr(self.db_manager.db_manager, 'close'):
                self.db_manager.db_manager.close()
    
    def _get_all_stores(self):
        """
        获取所有店铺信息
        
        Returns:
            list: 店铺信息列表
        """
        try:
            conn = self.db_manager.db_manager.conn
            if not conn:
                self.db_manager.db_manager.connect()
                conn = self.db_manager.db_manager.conn
                
            cursor = conn.cursor()
            cursor.execute("SELECT id, plg_shopname, plg_status, plg_points FROM stores")
            stores = cursor.fetchall()
            
            return [
                {
                    "id": store[0],
                    "plg_shopname": store[1],
                    "plg_status": store[2],
                    "plg_points": store[3] or 0
                } for store in stores
            ]
            
        except sqlite3.Error as e:
            logger.error(f"查询店铺数据时出错: {str(e)}")
            return []
        finally:
            if hasattr(self.db_manager.db_manager, 'close'):
                self.db_manager.db_manager.close()
    
    def _send_request_to_platform(self, data):
        """
        向平台发送请求
        
        Args:
            data (dict): 要发送的数据
            
        Returns:
            dict: 平台响应数据，失败返回None
        """
        try:
            url = f"{self.api_base_url}/plugin/update-points"
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'WowckerPlugin/1.0'
            }
            
            logger.info(f"发送请求到: {url}")
            logger.info(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            response = requests.post(
                url, 
                json=data, 
                headers=headers, 
                timeout=30
            )
            
            logger.info(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                logger.info(f"响应数据: {json.dumps(response_data, ensure_ascii=False, indent=2)}")
                return response_data
            else:
                logger.error(f"平台请求失败，状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"发送请求时出错: {str(e)}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"解析响应JSON时出错: {str(e)}")
            return None
    
    def _reset_all_store_points(self):
        """
        重置所有店铺的积分为0
        
        Returns:
            bool: 重置是否成功
        """
        try:
            conn = self.db_manager.db_manager.conn
            if not conn:
                self.db_manager.db_manager.connect()
                conn = self.db_manager.db_manager.conn
                
            cursor = conn.cursor()
            cursor.execute("UPDATE stores SET plg_points = 0")
            conn.commit()
            
            affected_rows = cursor.rowcount
            logger.info(f"成功重置 {affected_rows} 个店铺的积分为0")
            return True
            
        except sqlite3.Error as e:
            logger.error(f"重置店铺积分时出错: {str(e)}")
            return False
        finally:
            if hasattr(self.db_manager.db_manager, 'close'):
                self.db_manager.db_manager.close()
    
    def _update_store_status(self, stores_data):
        """
        根据平台响应更新店铺状态
        
        Args:
            stores_data (list): 平台返回的店铺状态数据
            
        Returns:
            bool: 更新是否成功
        """
        if not stores_data:
            logger.warning("没有收到店铺状态数据")
            return False
            
        try:
            conn = self.db_manager.db_manager.conn
            if not conn:
                self.db_manager.db_manager.connect()
                conn = self.db_manager.db_manager.conn
                
            cursor = conn.cursor()
            updated_count = 0
            
            for store_data in stores_data:
                shop_name = store_data.get("plg_shopname")
                status = store_data.get("plg_status", 1)
                
                if shop_name:
                    cursor.execute(
                        "UPDATE stores SET plg_status = ? WHERE plg_shopname = ?",
                        (status, shop_name)
                    )
                    if cursor.rowcount > 0:
                        updated_count += 1
                        logger.info(f"更新店铺 '{shop_name}' 状态为: {status}")
            
            conn.commit()
            logger.info(f"成功更新 {updated_count} 个店铺的状态")
            return True
            
        except sqlite3.Error as e:
            logger.error(f"更新店铺状态时出错: {str(e)}")
            return False
        finally:
            if hasattr(self.db_manager.db_manager, 'close'):
                self.db_manager.db_manager.close()
    
    def _verify_points_reset(self):
        """验证积分重置状态"""
        try:
            stores = self._get_all_stores()
            total_points = sum(store["plg_points"] for store in stores)
            
            if total_points > 0:
                logger.warning(f"发现未重置的积分，总计: {total_points}")
            else:
                logger.info("积分重置验证通过，所有店铺积分为0")
                
        except Exception as e:
            logger.error(f"验证积分重置状态时出错: {str(e)}")


# 创建启动函数
def run_poller():
    """启动轮询程序"""
    poller = StorePoller()
    success = poller.start()
    
    if success:
        logger.info("轮询器已成功启动")
    else:
        logger.error("轮询器启动失败")
        sys.exit(1)
    
    try:
        logger.info("按Ctrl+C终止轮询...")  
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("接收到终止信号，停止轮询...")
        poller.stop()


if __name__ == "__main__":
    run_poller()
