import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qgfxshaderbuilder_p.h"
        name: "QGfxShaderBuilder"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "Qt5Compat.GraphicalEffects.private/ShaderBuilder 5.0",
            "Qt5Compat.GraphicalEffects.private/ShaderBuilder 6.0"
        ]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [1280, 1536]
        Method {
            name: "gaussianBlur"
            type: "QVariantMap"
            Parameter { name: "parameters"; type: "QJSValue" }
        }
        Method {
            name: "buildVertexShader"
            type: "QUrl"
            Parameter { name: "code"; type: "QByteArray" }
        }
        Method {
            name: "buildFragmentShader"
            type: "QUrl"
            Parameter { name: "code"; type: "QByteArray" }
        }
    }
    Component {
        file: "qgfxsourceproxy_p.h"
        name: "QGfxSourceProxy"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "Qt5Compat.GraphicalEffects.private/SourceProxy 5.0",
            "Qt5Compat.GraphicalEffects.private/SourceProxy 6.0",
            "Qt5Compat.GraphicalEffects.private/SourceProxy 6.3",
            "Qt5Compat.GraphicalEffects.private/SourceProxy 6.7"
        ]
        exportMetaObjectRevisions: [1280, 1536, 1539, 1543]
        Enum {
            name: "Interpolation"
            values: [
                "AnyInterpolation",
                "NearestInterpolation",
                "LinearInterpolation"
            ]
        }
        Property {
            name: "input"
            type: "QQuickItem"
            isPointer: true
            read: "input"
            write: "setInput"
            reset: "resetInput"
            notify: "inputChanged"
            index: 0
        }
        Property {
            name: "output"
            type: "QQuickItem"
            isPointer: true
            read: "output"
            notify: "outputChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "sourceRect"
            type: "QRectF"
            read: "sourceRect"
            write: "setSourceRect"
            notify: "sourceRectChanged"
            index: 2
        }
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            notify: "activeChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "interpolation"
            type: "Interpolation"
            read: "interpolation"
            write: "setInterpolation"
            notify: "interpolationChanged"
            index: 4
        }
        Signal { name: "inputChanged" }
        Signal { name: "outputChanged" }
        Signal { name: "sourceRectChanged" }
        Signal { name: "activeChanged" }
        Signal { name: "interpolationChanged" }
        Method { name: "repolish" }
    }
}
