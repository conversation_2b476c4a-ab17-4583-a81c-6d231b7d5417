"""
店铺数据操作模块
提供stores表的CRUD操作
"""
import logging
from typing import Dict, List, Tuple, Optional, Any
from .db_manager import DatabaseManager

logger = logging.getLogger(__name__)

class StoreOperations:
    """店铺数据操作类"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """
        初始化店铺数据操作
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager or DatabaseManager()
    
    def add_store(self, store_name: str, 
                  is_active: bool = True, prompt: str = "", 
                  api_key: str = "", points: int = 0) -> int:
        """
        添加新店铺
        
        Args:
            store_name: 店铺名称
            is_active: 是否激活
            prompt: 提示词模板
            api_key: API密钥
            points: 店铺积分
            
        Returns:
            int: 新店铺ID，失败返回-1
        """
        # 检查店铺是否已存在
        existing_store = self.get_store_by_name(store_name)
        if existing_store:
            logger.warning(f"店铺已存在: {store_name}")
            return -1
        
        # 新结构中使用新的字段名
        query = """
        INSERT INTO stores (
            plg_shopname, plg_status, plg_prompt, 
            plg_apikey, plg_points
        )
        VALUES (?, ?, ?, ?, ?)
        """
        
        status_int = 1 if is_active else 0
        
        if not self.db_manager.connect():
            return -1
        
        try:
            self.db_manager.cursor.execute(query, (store_name, status_int, prompt, api_key, points))
            self.db_manager.conn.commit()
            return self.db_manager.get_last_insert_id()
        except Exception as e:
            logger.error(f"添加店铺时出错: {str(e)}")
            self.db_manager.conn.rollback()
            return -1
        finally:
            self.db_manager.close()
    
    def update_store(self, store_id: int, data: Dict[str, Any]) -> bool:
        """
        更新店铺信息
        
        Args:
            store_id: 店铺ID
            data: 更新数据字典
            
        Returns:
            bool: 操作是否成功
        """
        if not data:
            return False
            
        # 映射旧字段名到新字段名
        field_mapping = {
            'store_name': 'plg_shopname',
            'is_active': 'plg_status',
            'prompt': 'plg_prompt',
            'api_key': 'plg_apikey',
            'points': 'plg_points'
        }
        
        # 使用新的字段名
        new_data = {}
        for key, value in data.items():
            if key in field_mapping:
                new_data[field_mapping[key]] = value
            else:
                new_data[key] = value
                
        # 如果包含plg_status字段，确保它是整数
        if 'plg_status' in new_data:
            new_data['plg_status'] = 1 if new_data['plg_status'] else 0
        
        # 构建更新语句
        set_clause = ", ".join([f"{key} = ?" for key in new_data.keys()])
        query = f"UPDATE stores SET {set_clause} WHERE id = ?"
        
        # 准备参数
        params = list(new_data.values())
        params.append(store_id)
        
        return self.db_manager.execute_update(query, tuple(params))
    
    def get_store_by_id(self, store_id: int) -> Optional[Dict]:
        """
        通过ID获取店铺
        
        Args:
            store_id: 店铺ID
            
        Returns:
            Optional[Dict]: 店铺信息字典
        """
        # 新结构中使用新的字段名
        query = """
        SELECT id, plg_shopname, plg_status, plg_prompt, 
               plg_apikey, plg_points, created_at
        FROM stores
        WHERE id = ?
        """
        
        results = self.db_manager.execute_query(query, (store_id,))
        if not results:
            return None
        
        return self._row_to_dict(results[0])
    
    def get_store_by_name(self, store_name: str) -> Optional[Dict]:
        """
        通过名称获取店铺
        
        Args:
            store_name: 店铺名称
            
        Returns:
            Optional[Dict]: 店铺信息字典
        """
        # 新结构中使用plg_shopname字段
        query = """
        SELECT id, plg_shopname, plg_status, plg_prompt, 
               plg_apikey, plg_points, created_at
        FROM stores
        WHERE plg_shopname = ?
        """
        
        results = self.db_manager.execute_query(query, (store_name,))
        if not results:
            return None
        
        return self._row_to_dict(results[0])
    
    def get_all_stores(self) -> List[Dict]:
        """
        获取所有店铺
        
        Returns:
            List[Dict]: 店铺信息列表
        """
        # 新结构中不再按用户ID获取店铺
        query = """
        SELECT id, plg_shopname, plg_status, plg_prompt, 
               plg_apikey, plg_points, created_at
        FROM stores
        """
        
        results = self.db_manager.execute_query(query)
        stores = []
        
        for row in results:
            stores.append(self._row_to_dict(row))
        
        return stores
    
    def delete_store(self, store_id: int) -> bool:
        """
        删除店铺
        
        Args:
            store_id: 店铺ID
            
        Returns:
            bool: 操作是否成功
        """
        query = "DELETE FROM stores WHERE id = ?"
        return self.db_manager.execute_update(query, (store_id,))
    
    def toggle_store_status(self, store_id: int, is_active: bool) -> bool:
        """
        切换店铺状态
        
        Args:
            store_id: 店铺ID
            is_active: 是否激活
            
        Returns:
            bool: 操作是否成功
        """
        status_int = 1 if is_active else 0
        query = "UPDATE stores SET plg_status = ? WHERE id = ?"
        return self.db_manager.execute_update(query, (status_int, store_id))
    
    def _row_to_dict(self, row: Tuple) -> Dict:
        """将数据库行转换为字典"""
        # 使用兼容性的键名和新结构字段
        return {
            'id': row[0],
            'store_name': row[1],  # 兼容性键名，plg_shopname
            'is_active': bool(row[2]),  # 兼容性键名，plg_status
            'prompt': row[3],  # 兼容性键名，plg_prompt
            'api_key': row[4],  # 兼容性键名，plg_apikey
            'points': row[5],  # plg_points
            'created_at': row[6],
            # 添加新结构字段名作为备用
            'plg_shopname': row[1],
            'plg_status': bool(row[2]),
            'plg_prompt': row[3],
            'plg_apikey': row[4],
            'plg_points': row[5]
        }
        
    def update_points_atomically(self, store_id: int, points_increment: int = 1) -> Any:
        """
        使用事务原子性地更新店铺积分
        
        Args:
            store_id: 店铺ID
            points_increment: 要增加的积分值，默认为1
            
        Returns:
            Any: 操作成功则返回更新后的积分值，失败返回False
        """
        # 连接数据库
        if not self.db_manager.connect():
            logger.error("连接数据库失败")
            return False
        
        try:
            # 开始事务
            self.db_manager.cursor.execute("BEGIN TRANSACTION")
            
            # 获取当前积分值
            select_query = "SELECT plg_points FROM stores WHERE id = ?"
            self.db_manager.cursor.execute(select_query, (store_id,))
            result = self.db_manager.cursor.fetchone()
            
            if not result:
                logger.error(f"店铺ID {store_id} 不存在")
                self.db_manager.conn.rollback()
                return False
            
            current_points = result[0] or 0
            new_points = current_points + points_increment
            
            # 更新积分
            update_query = "UPDATE stores SET plg_points = ? WHERE id = ?"
            self.db_manager.cursor.execute(update_query, (new_points, store_id))
            
            # 提交事务
            self.db_manager.conn.commit()
            logger.info(f"店铺 {store_id} 积分更新成功: {current_points} -> {new_points}")
            return new_points
            
        except Exception as e:
            logger.error(f"更新店铺积分时出错: {str(e)}")
            self.db_manager.conn.rollback()
            return False
        finally:
            self.db_manager.close()
    
    def get_active_stores(self) -> List[Dict]:
        """
        获取所有激活的店铺
        
        Returns:
            List[Dict]: 激活的店铺信息列表
        """
        query = """
        SELECT id, plg_shopname, plg_status, plg_prompt, 
               plg_apikey, plg_points, created_at
        FROM stores
        WHERE plg_status = 1
        """
        
        results = self.db_manager.execute_query(query)
        stores = []
        
        for row in results:
            stores.append(self._row_to_dict(row))
        
        return stores
    
    def store_exists(self, store_name: str) -> bool:
        """
        检查店铺是否存在
        
        Args:
            store_name: 店铺名称
            
        Returns:
            bool: 店铺是否存在
        """
        store = self.get_store_by_name(store_name)
        return store is not None
    
    def get_store_count(self) -> int:
        """
        获取店铺总数
        
        Returns:
            int: 店铺总数
        """
        query = "SELECT COUNT(*) FROM stores"
        results = self.db_manager.execute_query(query)
        return results[0][0] if results else 0
