[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}], "className": "QQmlXmlListModelQueryResult", "gadget": true, "lineNumber": 51, "qualifiedClassName": "QQmlXmlListModelQueryResult"}, {"classInfos": [{"name": "QML.Element", "value": "XmlListModelRole"}], "className": "QQmlXmlListModelRole", "lineNumber": 61, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "elementName", "notify": "elementNameChanged", "read": "elementName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setElementName"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "attributeName", "notify": "attributeNameChanged", "read": "attributeName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setAttributeName"}], "qualifiedClassName": "QQmlXmlListModelRole", "signals": [{"access": "public", "index": 0, "name": "nameChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "elementNameChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "attributeNameChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "XmlListModel"}, {"name": "DefaultProperty", "value": "roles"}], "className": "QQmlXmlListModel", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 95, "methods": [{"access": "public", "index": 11, "isConst": true, "name": "errorString", "returnType": "QString"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "progress", "notify": "progressChanged", "read": "progress", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "query", "notify": "queryChanged", "read": "query", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "roles", "read": "roleObjects", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QQmlXmlListModelRole>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "QQmlXmlListModel", "signals": [{"access": "public", "arguments": [{"type": "QQmlXmlListModel::Status"}], "index": 0, "name": "statusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "progress", "type": "qreal"}], "index": 1, "name": "progressChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "countChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "queryChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 5, "name": "reload", "returnType": "void"}, {"access": "private", "index": 6, "name": "requestFinished", "returnType": "void"}, {"access": "private", "arguments": [{"type": "qint64"}, {"type": "qint64"}], "index": 7, "name": "requestProgress", "returnType": "void"}, {"access": "private", "index": 8, "name": "dataCleared", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QQmlXmlListModelQueryResult"}], "index": 9, "name": "queryCompleted", "returnType": "void"}, {"access": "private", "arguments": [{"name": "object", "type": "void*"}, {"name": "error", "type": "QString"}], "index": 10, "name": "queryError", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractListModel"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qqmlxmllistmodel_p.h", "outputRevision": 69}]