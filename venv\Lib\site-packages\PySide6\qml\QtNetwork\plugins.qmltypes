import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qabstractsocket.h"
        name: "QAbstractSocket"
        accessSemantics: "reference"
        prototype: "QIODevice"
        Enum {
            name: "SocketType"
            values: [
                "TcpSocket",
                "UdpSocket",
                "SctpSocket",
                "UnknownSocketType"
            ]
        }
        Enum {
            name: "NetworkLayerProtocol"
            values: [
                "IPv4Protocol",
                "IPv6Protocol",
                "AnyIPProtocol",
                "UnknownNetworkLayerProtocol"
            ]
        }
        Enum {
            name: "SocketError"
            values: [
                "ConnectionRefusedError",
                "RemoteHostClosedError",
                "HostNotFoundError",
                "SocketAccessError",
                "SocketResourceError",
                "SocketTimeoutError",
                "DatagramTooLargeError",
                "NetworkError",
                "AddressInUseError",
                "SocketAddressNotAvailableError",
                "UnsupportedSocketOperationError",
                "UnfinishedSocketOperationError",
                "ProxyAuthenticationRequiredError",
                "SslHandshakeFailedError",
                "ProxyConnectionRefusedError",
                "ProxyConnectionClosedError",
                "ProxyConnectionTimeoutError",
                "ProxyNotFoundError",
                "ProxyProtocolError",
                "OperationError",
                "SslInternalError",
                "SslInvalidUserDataError",
                "TemporaryError",
                "UnknownSocketError"
            ]
        }
        Enum {
            name: "SocketState"
            values: [
                "UnconnectedState",
                "HostLookupState",
                "ConnectingState",
                "ConnectedState",
                "BoundState",
                "ListeningState",
                "ClosingState"
            ]
        }
        Enum {
            name: "SocketOption"
            values: [
                "LowDelayOption",
                "KeepAliveOption",
                "MulticastTtlOption",
                "MulticastLoopbackOption",
                "TypeOfServiceOption",
                "SendBufferSizeSocketOption",
                "ReceiveBufferSizeSocketOption",
                "PathMtuSocketOption"
            ]
        }
        Signal { name: "hostFound" }
        Signal { name: "connected" }
        Signal { name: "disconnected" }
        Signal {
            name: "stateChanged"
            Parameter { type: "QAbstractSocket::SocketState" }
        }
        Signal {
            name: "errorOccurred"
            Parameter { type: "QAbstractSocket::SocketError" }
        }
        Signal {
            name: "proxyAuthenticationRequired"
            Parameter { name: "proxy"; type: "QNetworkProxy" }
            Parameter { name: "authenticator"; type: "QAuthenticator"; isPointer: true }
        }
        Method { name: "_q_connectToNextAddress" }
        Method {
            name: "_q_startConnecting"
            Parameter { type: "QHostInfo" }
        }
        Method { name: "_q_abortConnectionAttempt" }
        Method { name: "_q_testConnection" }
    }
    Component {
        file: "qiodevice.h"
        name: "QIODevice"
        accessSemantics: "reference"
        prototype: "QObject"
        Signal { name: "readyRead" }
        Signal {
            name: "channelReadyRead"
            Parameter { name: "channel"; type: "int" }
        }
        Signal {
            name: "bytesWritten"
            Parameter { name: "bytes"; type: "qlonglong" }
        }
        Signal {
            name: "channelBytesWritten"
            Parameter { name: "channel"; type: "int" }
            Parameter { name: "bytes"; type: "qlonglong" }
        }
        Signal { name: "aboutToClose" }
        Signal { name: "readChannelFinished" }
    }
    Component {
        file: "private/qqmlnetworkinformation_p.h"
        name: "QNetworkInformation"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtNetwork/NetworkInformation 6.7"]
        isCreatable: false
        isSingleton: true
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1543]
        Enum {
            name: "Reachability"
            isScoped: true
            values: ["Unknown", "Disconnected", "Local", "Site", "Online"]
        }
        Enum {
            name: "TransportMedium"
            isScoped: true
            values: ["Unknown", "Ethernet", "Cellular", "WiFi", "Bluetooth"]
        }
        Enum {
            name: "Features"
            alias: "Feature"
            isFlag: true
            isScoped: true
            values: [
                "Reachability",
                "CaptivePortal",
                "TransportMedium",
                "Metered"
            ]
        }
        Property {
            name: "reachability"
            type: "Reachability"
            read: "reachability"
            notify: "reachabilityChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "isBehindCaptivePortal"
            type: "bool"
            read: "isBehindCaptivePortal"
            notify: "isBehindCaptivePortalChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "transportMedium"
            type: "TransportMedium"
            read: "transportMedium"
            notify: "transportMediumChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "isMetered"
            type: "bool"
            read: "isMetered"
            notify: "isMeteredChanged"
            index: 3
            isReadonly: true
        }
        Signal {
            name: "reachabilityChanged"
            Parameter { name: "newReachability"; type: "QNetworkInformation::Reachability" }
        }
        Signal {
            name: "isBehindCaptivePortalChanged"
            Parameter { name: "state"; type: "bool" }
        }
        Signal {
            name: "transportMediumChanged"
            Parameter { name: "current"; type: "QNetworkInformation::TransportMedium" }
        }
        Signal {
            name: "isMeteredChanged"
            Parameter { name: "isMetered"; type: "bool" }
        }
    }
    Component {
        file: "private/qqmlsslconfiguration_p.h"
        name: "QQmlSslConfiguration"
        accessSemantics: "value"
        Property { name: "ciphers"; type: "QString"; read: "ciphers"; write: "setCiphers"; index: 0 }
        Property {
            name: "sslOptions"
            type: "QSsl::SslOption"
            isList: true
            read: "sslOptions"
            write: "setSslOptions"
            index: 1
        }
        Property {
            name: "protocol"
            type: "QSsl::SslProtocol"
            read: "protocol"
            write: "setProtocol"
            index: 2
        }
        Property {
            name: "peerVerifyMode"
            type: "QSslSocket::PeerVerifyMode"
            read: "peerVerifyMode"
            write: "setPeerVerifyMode"
            index: 3
        }
        Property {
            name: "peerVerifyDepth"
            type: "int"
            read: "peerVerifyDepth"
            write: "setPeerVerifyDepth"
            index: 4
        }
        Property {
            name: "sessionTicket"
            type: "QByteArray"
            read: "sessionTicket"
            write: "setSessionTicket"
            index: 5
        }
        Method {
            name: "setCertificateFiles"
            Parameter { name: "certificateFiles"; type: "QStringList" }
        }
        Method {
            name: "setPrivateKey"
            Parameter { name: "privateKey"; type: "QQmlSslKey" }
        }
    }
    Component {
        file: "private/qqmlsslconfiguration_p.h"
        name: "QQmlSslDefaultConfiguration"
        accessSemantics: "value"
        prototype: "QQmlSslConfiguration"
        exports: ["QtNetwork/sslConfiguration 6.7"]
        isCreatable: false
        exportMetaObjectRevisions: [1543]
    }
    Component {
        file: "private/qqmlsslconfiguration_p.h"
        name: "QQmlSslDefaultDtlsConfiguration"
        accessSemantics: "value"
        prototype: "QQmlSslConfiguration"
        exports: ["QtNetwork/sslDtlsConfiguration 6.7"]
        isCreatable: false
        exportMetaObjectRevisions: [1543]
    }
    Component {
        file: "private/qqmlsslkey_p.h"
        name: "QQmlSslKey"
        accessSemantics: "value"
        exports: ["QtNetwork/sslKey 6.7"]
        isCreatable: false
        exportMetaObjectRevisions: [1543]
        Property { name: "keyFile"; type: "QString"; read: "keyFile"; write: "setKeyFile"; index: 0 }
        Property {
            name: "keyAlgorithm"
            type: "QSsl::KeyAlgorithm"
            read: "keyAlgorithm"
            write: "setKeyAlgorithm"
            index: 1
        }
        Property {
            name: "keyFormat"
            type: "QSsl::EncodingFormat"
            read: "keyFormat"
            write: "setKeyFormat"
            index: 2
        }
        Property {
            name: "keyPassPhrase"
            type: "QByteArray"
            read: "keyPassPhrase"
            write: "setKeyPassPhrase"
            index: 3
        }
        Property { name: "keyType"; type: "QSsl::KeyType"; read: "keyType"; write: "setKeyType"; index: 4 }
    }
    Component {
        file: "private/qqmlsslnamespace_p.h"
        name: "QSsl"
        accessSemantics: "none"
        exports: ["QtNetwork/Ssl 6.7"]
        isCreatable: false
        exportMetaObjectRevisions: [1543]
        Enum {
            name: "KeyType"
            values: ["PrivateKey", "PublicKey"]
        }
        Enum {
            name: "EncodingFormat"
            values: ["Pem", "Der"]
        }
        Enum {
            name: "KeyAlgorithm"
            values: ["Opaque", "Rsa", "Dsa", "Ec", "Dh"]
        }
        Enum {
            name: "AlternativeNameEntryType"
            values: ["EmailEntry", "DnsEntry", "IpAddressEntry"]
        }
        Enum {
            name: "SslProtocol"
            values: [
                "TlsV1_0",
                "TlsV1_1",
                "TlsV1_2",
                "AnyProtocol",
                "SecureProtocols",
                "TlsV1_0OrLater",
                "TlsV1_1OrLater",
                "TlsV1_2OrLater",
                "DtlsV1_0",
                "DtlsV1_0OrLater",
                "DtlsV1_2",
                "DtlsV1_2OrLater",
                "TlsV1_3",
                "TlsV1_3OrLater",
                "UnknownProtocol"
            ]
        }
        Enum {
            name: "SslOption"
            values: [
                "SslOptionDisableEmptyFragments",
                "SslOptionDisableSessionTickets",
                "SslOptionDisableCompression",
                "SslOptionDisableServerNameIndication",
                "SslOptionDisableLegacyRenegotiation",
                "SslOptionDisableSessionSharing",
                "SslOptionDisableSessionPersistence",
                "SslOptionDisableServerCipherPreference"
            ]
        }
        Enum {
            name: "AlertLevel"
            isScoped: true
            values: ["Warning", "Fatal", "Unknown"]
        }
        Enum {
            name: "AlertType"
            isScoped: true
            values: [
                "CloseNotify",
                "UnexpectedMessage",
                "BadRecordMac",
                "RecordOverflow",
                "DecompressionFailure",
                "HandshakeFailure",
                "NoCertificate",
                "BadCertificate",
                "UnsupportedCertificate",
                "CertificateRevoked",
                "CertificateExpired",
                "CertificateUnknown",
                "IllegalParameter",
                "UnknownCa",
                "AccessDenied",
                "DecodeError",
                "DecryptError",
                "ExportRestriction",
                "ProtocolVersion",
                "InsufficientSecurity",
                "InternalError",
                "InappropriateFallback",
                "UserCancelled",
                "NoRenegotiation",
                "MissingExtension",
                "UnsupportedExtension",
                "CertificateUnobtainable",
                "UnrecognizedName",
                "BadCertificateStatusResponse",
                "BadCertificateHashValue",
                "UnknownPskIdentity",
                "CertificateRequired",
                "NoApplicationProtocol",
                "UnknownAlertMessage"
            ]
        }
        Enum {
            name: "ImplementedClass"
            isScoped: true
            values: [
                "Key",
                "Certificate",
                "Socket",
                "DiffieHellman",
                "EllipticCurve",
                "Dtls",
                "DtlsCookie"
            ]
        }
        Enum {
            name: "SupportedFeature"
            isScoped: true
            values: [
                "CertificateVerification",
                "ClientSideAlpn",
                "ServerSideAlpn",
                "Ocsp",
                "Psk",
                "SessionTicket",
                "Alerts"
            ]
        }
    }
    Component {
        file: "qtcpsocket.h"
        name: "QTcpSocket"
        accessSemantics: "reference"
        prototype: "QAbstractSocket"
    }
    Component {
        file: "private/qqmlsslsocketnamespace_p.h"
        name: "QSslSocket"
        accessSemantics: "none"
        prototype: "QTcpSocket"
        exports: ["QtNetwork/SslSocket 6.7"]
        isCreatable: false
        exportMetaObjectRevisions: [1543]
        Enum {
            name: "SslMode"
            values: ["UnencryptedMode", "SslClientMode", "SslServerMode"]
        }
        Enum {
            name: "PeerVerifyMode"
            values: [
                "VerifyNone",
                "QueryPeer",
                "VerifyPeer",
                "AutoVerifyPeer"
            ]
        }
        Signal { name: "encrypted" }
        Signal {
            name: "peerVerifyError"
            Parameter { name: "error"; type: "QSslError" }
        }
        Signal {
            name: "sslErrors"
            Parameter { name: "errors"; type: "QSslError"; isList: true }
        }
        Signal {
            name: "modeChanged"
            Parameter { name: "newMode"; type: "QSslSocket::SslMode" }
        }
        Signal {
            name: "encryptedBytesWritten"
            Parameter { name: "totalBytes"; type: "qlonglong" }
        }
        Signal {
            name: "preSharedKeyAuthenticationRequired"
            Parameter { name: "authenticator"; type: "QSslPreSharedKeyAuthenticator"; isPointer: true }
        }
        Signal { name: "newSessionTicketReceived" }
        Signal {
            name: "alertSent"
            Parameter { name: "level"; type: "QSsl::AlertLevel" }
            Parameter { name: "type"; type: "QSsl::AlertType" }
            Parameter { name: "description"; type: "QString" }
        }
        Signal {
            name: "alertReceived"
            Parameter { name: "level"; type: "QSsl::AlertLevel" }
            Parameter { name: "type"; type: "QSsl::AlertType" }
            Parameter { name: "description"; type: "QString" }
        }
        Signal {
            name: "handshakeInterruptedOnError"
            Parameter { name: "error"; type: "QSslError" }
        }
        Method { name: "startClientEncryption" }
        Method { name: "startServerEncryption" }
        Method { name: "ignoreSslErrors" }
        Method { name: "_q_connectedSlot" }
        Method { name: "_q_hostFoundSlot" }
        Method { name: "_q_disconnectedSlot" }
        Method {
            name: "_q_stateChangedSlot"
            Parameter { type: "QAbstractSocket::SocketState" }
        }
        Method {
            name: "_q_errorSlot"
            Parameter { type: "QAbstractSocket::SocketError" }
        }
        Method { name: "_q_readyReadSlot" }
        Method {
            name: "_q_channelReadyReadSlot"
            Parameter { type: "int" }
        }
        Method {
            name: "_q_bytesWrittenSlot"
            Parameter { type: "qlonglong" }
        }
        Method {
            name: "_q_channelBytesWrittenSlot"
            Parameter { type: "int" }
            Parameter { type: "qlonglong" }
        }
        Method { name: "_q_readChannelFinishedSlot" }
        Method { name: "_q_flushWriteBuffer" }
        Method { name: "_q_flushReadBuffer" }
        Method { name: "_q_resumeImplementation" }
    }
}
