"""
配置管理器
提供配置的加载、保存、验证和管理功能
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from .settings_loader import get_settings, update_setting, save_settings, get_setting
    from src.pack.common_utils.database.app_db_manager import AppDBManager
except ImportError as e:
    print(f"导入模块失败: {e}")
    def get_settings(): return {}
    def update_setting(key, value): return True
    def save_settings(): return True
    def get_setting(key, default=None): return default
    class AppDBManager:
        def __init__(self): pass
        def initialize(self): return True

# 配置日志
logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器类"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.db_manager = AppDBManager()
        self.db_manager.initialize()
        
        # 配置验证规则
        self.validation_rules = {
            'api.timeout': {'type': int, 'min': 1, 'max': 300},
            'api.retry_count': {'type': int, 'min': 0, 'max': 10},
            'poller.interval': {'type': int, 'min': 5, 'max': 3600},
            'database.backup_interval': {'type': int, 'min': 1, 'max': 168},
            'search.bm25_k1': {'type': float, 'min': 0.1, 'max': 5.0},
            'search.bm25_b': {'type': float, 'min': 0.0, 'max': 1.0},
            'vector_db.chunk_size': {'type': int, 'min': 100, 'max': 10000},
            'vector_db.dimension': {'type': int, 'min': 128, 'max': 4096},
            'bulk_acquisition.send_interval': {'type': float, 'min': 0.1, 'max': 60.0},
            'bulk_acquisition.max_retries': {'type': int, 'min': 0, 'max': 10},
            'scheduler.max_concurrent_tasks': {'type': int, 'min': 1, 'max': 100}
        }
        
        logger.info("配置管理器已初始化")
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return get_setting(key, default)
    
    def set_config(self, key: str, value: Any, validate: bool = True) -> bool:
        """
        设置配置项
        
        Args:
            key: 配置键
            value: 配置值
            validate: 是否验证
            
        Returns:
            bool: 设置是否成功
        """
        try:
            # 验证配置值
            if validate and not self.validate_config(key, value):
                logger.error(f"配置值验证失败: {key} = {value}")
                return False
            
            # 更新配置
            success = update_setting(key, value)
            if success:
                logger.info(f"配置已更新: {key} = {value}")
            else:
                logger.error(f"配置更新失败: {key}")
            
            return success
            
        except Exception as e:
            logger.error(f"设置配置时出错: {str(e)}")
            return False
    
    def validate_config(self, key: str, value: Any) -> bool:
        """
        验证配置值
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            bool: 验证是否通过
        """
        try:
            if key not in self.validation_rules:
                return True  # 没有验证规则的配置项默认通过
            
            rule = self.validation_rules[key]
            
            # 检查类型
            if 'type' in rule:
                expected_type = rule['type']
                if not isinstance(value, expected_type):
                    logger.error(f"配置类型错误: {key} 期望 {expected_type.__name__}, 实际 {type(value).__name__}")
                    return False
            
            # 检查数值范围
            if isinstance(value, (int, float)):
                if 'min' in rule and value < rule['min']:
                    logger.error(f"配置值过小: {key} = {value}, 最小值 {rule['min']}")
                    return False
                if 'max' in rule and value > rule['max']:
                    logger.error(f"配置值过大: {key} = {value}, 最大值 {rule['max']}")
                    return False
            
            # 检查字符串长度
            if isinstance(value, str):
                if 'min_length' in rule and len(value) < rule['min_length']:
                    logger.error(f"配置字符串过短: {key}")
                    return False
                if 'max_length' in rule and len(value) > rule['max_length']:
                    logger.error(f"配置字符串过长: {key}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证配置时出错: {str(e)}")
            return False
    
    def save_config(self) -> bool:
        """
        保存配置到文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            return save_settings()
        except Exception as e:
            logger.error(f"保存配置时出错: {str(e)}")
            return False
    
    def reset_config(self, key: str = None) -> bool:
        """
        重置配置到默认值
        
        Args:
            key: 要重置的配置键，如果为None则重置所有配置
            
        Returns:
            bool: 重置是否成功
        """
        try:
            if key:
                # 重置单个配置项
                default_value = self._get_default_value(key)
                if default_value is not None:
                    return self.set_config(key, default_value, validate=False)
                else:
                    logger.warning(f"未找到配置项的默认值: {key}")
                    return False
            else:
                # 重置所有配置（重新加载默认配置）
                from .settings_loader import create_default_settings
                default_settings = create_default_settings()
                
                # 更新全局配置
                global _SETTINGS
                _SETTINGS = default_settings
                
                # 保存到文件
                return self.save_config()
                
        except Exception as e:
            logger.error(f"重置配置时出错: {str(e)}")
            return False
    
    def _get_default_value(self, key: str) -> Any:
        """
        获取配置项的默认值
        
        Args:
            key: 配置键
            
        Returns:
            默认值
        """
        try:
            from .settings_loader import create_default_settings
            default_settings = create_default_settings()
            
            keys = key.split('.')
            value = default_settings
            
            for k in keys:
                value = value[k]
            
            return value
            
        except (KeyError, TypeError):
            return None
    
    def export_config(self, file_path: str, format: str = 'json') -> bool:
        """
        导出配置到文件
        
        Args:
            file_path: 文件路径
            format: 文件格式 ('json' 或 'yaml')
            
        Returns:
            bool: 导出是否成功
        """
        try:
            settings = get_settings()
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            if format.lower() == 'json':
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, ensure_ascii=False, indent=2)
            elif format.lower() == 'yaml':
                try:
                    import yaml
                    with open(file_path, 'w', encoding='utf-8') as f:
                        yaml.dump(settings, f, default_flow_style=False, 
                                allow_unicode=True, indent=2)
                except ImportError:
                    logger.error("YAML模块未安装，无法导出YAML格式")
                    return False
            else:
                logger.error(f"不支持的导出格式: {format}")
                return False
            
            logger.info(f"配置已导出到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出配置时出错: {str(e)}")
            return False
    
    def import_config(self, file_path: str, merge: bool = True) -> bool:
        """
        从文件导入配置
        
        Args:
            file_path: 文件路径
            merge: 是否与现有配置合并
            
        Returns:
            bool: 导入是否成功
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"配置文件不存在: {file_path}")
                return False
            
            # 根据文件扩展名选择加载方式
            if file_path.endswith('.json'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_settings = json.load(f)
            elif file_path.endswith(('.yaml', '.yml')):
                try:
                    import yaml
                    with open(file_path, 'r', encoding='utf-8') as f:
                        imported_settings = yaml.safe_load(f)
                except ImportError:
                    logger.error("YAML模块未安装，无法导入YAML格式")
                    return False
            else:
                logger.error(f"不支持的文件格式: {file_path}")
                return False
            
            if merge:
                # 合并配置
                current_settings = get_settings()
                self._merge_dict(current_settings, imported_settings)
            else:
                # 替换配置
                global _SETTINGS
                _SETTINGS = imported_settings
            
            # 保存配置
            success = self.save_config()
            if success:
                logger.info(f"配置已从 {file_path} 导入")
            
            return success
            
        except Exception as e:
            logger.error(f"导入配置时出错: {str(e)}")
            return False
    
    def _merge_dict(self, target: dict, source: dict):
        """
        递归合并字典
        
        Args:
            target: 目标字典
            source: 源字典
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_dict(target[key], value)
            else:
                target[key] = value
    
    def get_config_schema(self) -> Dict[str, Any]:
        """
        获取配置模式（用于UI生成）
        
        Returns:
            Dict[str, Any]: 配置模式
        """
        return {
            'validation_rules': self.validation_rules,
            'categories': {
                'basic': ['app_name', 'version'],
                'database': ['database.path', 'database.backup_enabled', 'database.backup_interval'],
                'api': ['api.base_url', 'api.timeout', 'api.retry_count'],
                'poller': ['poller.interval', 'poller.enabled'],
                'search': ['search.bm25_k1', 'search.bm25_b', 'search.max_results'],
                'vector_db': ['vector_db.chunk_size', 'vector_db.dimension', 'vector_db.embedding_model'],
                'chat': ['chat.whatsapp.session_path', 'chat.message.max_context_length'],
                'llm': ['llm.qwen.api_key', 'llm.qwen.model', 'llm.qwen.temperature'],
                'bulk_acquisition': ['bulk_acquisition.send_interval', 'bulk_acquisition.max_retries'],
                'logging': ['logging.level', 'logging.file_enabled', 'logging.file_path']
            }
        }
    
    def update_api_key_from_db(self) -> bool:
        """
        从数据库更新API密钥
        
        Returns:
            bool: 更新是否成功
        """
        try:
            # 获取店铺信息
            stores = self.db_manager.get_all_stores()
            if stores:
                store = stores[0]  # 使用第一个店铺
                api_key = store.get('plg_apikey')
                
                if api_key:
                    # 更新LLM API密钥
                    success1 = self.set_config('llm.qwen.api_key', api_key, validate=False)
                    
                    # 如果有嵌入模型配置，也更新
                    success2 = self.set_config('embedding.api_key', api_key, validate=False)
                    
                    if success1:
                        logger.info("API密钥已从数据库更新")
                        return True
                    else:
                        logger.error("更新API密钥失败")
                        return False
                else:
                    logger.warning("数据库中没有找到API密钥")
                    return False
            else:
                logger.warning("数据库中没有找到店铺信息")
                return False
                
        except Exception as e:
            logger.error(f"从数据库更新API密钥时出错: {str(e)}")
            return False
