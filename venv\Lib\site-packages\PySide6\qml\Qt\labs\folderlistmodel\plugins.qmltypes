import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component { file: "private/fileproperty_p.h"; name: "FileProperty"; accessSemantics: "value" }
    Component {
        file: "private/qquickfolderlistmodel_p.h"
        name: "QQuickFolderListModel"
        accessSemantics: "reference"
        prototype: "QAbstractListModel"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "Qt.labs.folderlistmodel/FolderListModel 1.0",
            "Qt.labs.folderlistmodel/FolderListModel 2.1",
            "Qt.labs.folderlistmodel/FolderListModel 2.2",
            "Qt.labs.folderlistmodel/FolderListModel 2.11",
            "Qt.labs.folderlistmodel/FolderListModel 2.12",
            "Qt.labs.folderlistmodel/FolderListModel 6.0",
            "Qt.labs.folderlistmodel/FolderListModel 6.4"
        ]
        exportMetaObjectRevisions: [256, 513, 514, 523, 524, 1536, 1540]
        Enum {
            name: "SortField"
            values: ["Unsorted", "Name", "Time", "Size", "Type"]
        }
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading"]
        }
        Property {
            name: "folder"
            type: "QUrl"
            read: "folder"
            write: "setFolder"
            notify: "folderChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "rootFolder"
            type: "QUrl"
            read: "rootFolder"
            write: "setRootFolder"
            index: 1
            isFinal: true
        }
        Property {
            name: "parentFolder"
            type: "QUrl"
            read: "parentFolder"
            notify: "folderChanged"
            index: 2
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "nameFilters"
            type: "QStringList"
            read: "nameFilters"
            write: "setNameFilters"
            index: 3
            isFinal: true
        }
        Property {
            name: "sortField"
            type: "SortField"
            read: "sortField"
            write: "setSortField"
            index: 4
            isFinal: true
        }
        Property {
            name: "sortReversed"
            type: "bool"
            read: "sortReversed"
            write: "setSortReversed"
            index: 5
            isFinal: true
        }
        Property {
            name: "showFiles"
            revision: 513
            type: "bool"
            read: "showFiles"
            write: "setShowFiles"
            index: 6
            isFinal: true
        }
        Property {
            name: "showDirs"
            type: "bool"
            read: "showDirs"
            write: "setShowDirs"
            index: 7
            isFinal: true
        }
        Property {
            name: "showDirsFirst"
            type: "bool"
            read: "showDirsFirst"
            write: "setShowDirsFirst"
            index: 8
            isFinal: true
        }
        Property {
            name: "showDotAndDotDot"
            type: "bool"
            read: "showDotAndDotDot"
            write: "setShowDotAndDotDot"
            index: 9
            isFinal: true
        }
        Property {
            name: "showHidden"
            revision: 513
            type: "bool"
            read: "showHidden"
            write: "setShowHidden"
            index: 10
            isFinal: true
        }
        Property {
            name: "showOnlyReadable"
            type: "bool"
            read: "showOnlyReadable"
            write: "setShowOnlyReadable"
            index: 11
            isFinal: true
        }
        Property {
            name: "caseSensitive"
            revision: 514
            type: "bool"
            read: "caseSensitive"
            write: "setCaseSensitive"
            index: 12
            isFinal: true
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 13
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "status"
            revision: 523
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 14
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "sortCaseSensitive"
            revision: 524
            type: "bool"
            read: "sortCaseSensitive"
            write: "setSortCaseSensitive"
            index: 15
            isFinal: true
        }
        Signal { name: "folderChanged" }
        Signal { name: "rowCountChanged"; isMethodConstant: true }
        Signal { name: "countChanged"; revision: 513; isMethodConstant: true }
        Signal { name: "statusChanged"; revision: 523 }
        Method {
            name: "_q_directoryChanged"
            Parameter { name: "directory"; type: "QString" }
            Parameter { name: "list"; type: "FileProperty"; isList: true }
        }
        Method {
            name: "_q_directoryUpdated"
            Parameter { name: "directory"; type: "QString" }
            Parameter { name: "list"; type: "FileProperty"; isList: true }
            Parameter { name: "fromIndex"; type: "int" }
            Parameter { name: "toIndex"; type: "int" }
        }
        Method {
            name: "_q_sortFinished"
            Parameter { name: "list"; type: "FileProperty"; isList: true }
        }
        Method {
            name: "_q_statusChanged"
            Parameter { name: "s"; type: "QQuickFolderListModel::Status" }
        }
        Method {
            name: "isFolder"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "get"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "idx"; type: "int" }
            Parameter { name: "property"; type: "QString" }
        }
        Method {
            name: "indexOf"
            type: "int"
            isMethodConstant: true
            Parameter { name: "file"; type: "QUrl" }
        }
    }
}
