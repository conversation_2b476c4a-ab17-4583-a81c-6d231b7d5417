api:
  base_url: http://47.120.74.30:9090/api
  retry_count: 3
  timeout: 30
app_name: WOWCKER AGENT
bulk_acquisition:
  auto_add_customers: true
  batch_size: 10
  data_dir: data/customers
  default_source: bulk_acquisition
  max_retries: 3
  send_interval: 1.0
  timeout: 30
chat:
  message:
    auto_translate: true
    max_context_length: 4000
    response_timeout: 30
  whatsapp:
    session_path: data/whatsapp_session
    timeout: 30
copyright: ''
database:
  backup_enabled: true
  backup_interval: 24
  path: data/hermes.db
graph_file_path_core: data/graph/core
graph_file_path_help: data/graph/help
knowledge_graph:
  entity_extraction:
    custom_entities: []
    max_entity_length: 10
    min_entity_freq: 2
  relation_extraction:
    custom_relations:
    - 包含
    - 属于
    - 应用于
    - 等同于
    max_distance: 50
    min_relation_freq: 2
  use_multiprocessing: false
llm:
  qwen:
    api_key: ''
    base_url: https://dashscope.aliyuncs.com/api/v1
    max_tokens: 100
    model: qwen-plus-latest
    temperature: 0.1
logging:
  file_enabled: true
  file_path: logs/app.log
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  level: INFO
poller:
  enabled: true
  interval: 30
scheduler:
  check_interval: 1
  max_concurrent_tasks: 10
search:
  bm25_b: 0.75
  bm25_k1: 1.2
  default_kb_type: core
  default_top_k: 5
  default_type: hybrid
  graph_search:
    core_top_k: 5
    help_top_k: 5
    neighbor_limit: 3
  hybrid:
    bm25_weight: 0.4
    fusion_method: weighted_sum
    vector_weight: 0.6
  max_results: 10
  min_score_threshold: 0.1
  vector_search:
    core_top_k: 5
    help_top_k: 5
    min_score_threshold: 0.3
source_file_path_core: data/source/core
source_file_path_help: data/source/help
vector_db:
  chunk_overlap: 200
  chunk_size: 1000
  dimension: 1536
  embedding_model: text-embedding-ada-002
  index_type: Flat
  similarity_threshold: 0.7
vector_path_core: data/vector/core
vector_path_help: data/vector/help
version: v1.0.0
year: 2024
