# ======================================
# WOWCKER AGENT 配置文件
# ======================================

# 应用基本信息
app_name: "WOWCKER AGENT"
version: "v1.0.0"
copyright: ""
year: 2024

# ======================================
# 文件路径配置 - 定义数据源和存储位置
# ======================================
# 源文件路径
source_file_path_core: "data/source/core"  # 核心知识库文档位置(必需)：存放重要、权威的文档
source_file_path_help: "data/source/help"  # 辅助知识库文档位置(必需)：存放辅助、补充性质的文档

# 向量数据存储路径
vector_path_core: "data/vector/core"  # 核心知识库向量存储目录(必需)：存放核心文档的向量表示
vector_path_help: "data/vector/help"  # 辅助知识库向量存储目录(必需)：存放辅助文档的向量表示

# 知识图谱存储路径
graph_file_path_core: "data/graph/core"  # 核心知识图谱路径(必需)：存放从核心文档中提取的实体关系
graph_file_path_help: "data/graph/help"  # 辅助知识图谱路径(必需)：存放从辅助文档中提取的实体关系

# ======================================
# 数据库配置
# ======================================
database:
  path: "data/hermes.db"  # 数据库文件路径
  backup_enabled: true    # 是否启用备份
  backup_interval: 24     # 备份间隔（小时）

# ======================================
# API配置
# ======================================
api:
  base_url: "http://47.120.74.30:9090/api"  # API基础URL
  timeout: 30                                # 请求超时时间（秒）
  retry_count: 3                            # 重试次数

# ======================================
# 轮询配置
# ======================================
poller:
  interval: 30    # 轮询间隔（秒）
  enabled: true   # 是否启用轮询

# ======================================
# 知识图谱构建配置 - 控制知识图谱抽取和构建
# ======================================
knowledge_graph:
  # 处理性能设置
  use_multiprocessing: false      # 多进程设置：Excel文件处理时设为false避免内存问题
  
  # 实体提取设置
  entity_extraction:
    min_entity_freq: 2            # 实体最小出现频率：过滤低频实体
    max_entity_length: 10         # 实体最大长度(词数)：控制实体大小
    custom_entities: []           # 自定义实体列表：手动添加重要实体
  
  # 关系提取设置
  relation_extraction:
    min_relation_freq: 2          # 关系最小出现频率：过滤低频关系
    max_distance: 50              # 关系抽取最大词距离：控制关系提取范围
    custom_relations:             # 自定义关系类型：定义图谱中使用的关系类型
      - "包含"                    # 表示一个概念包含另一个概念
      - "属于"                    # 表示从属关系
      - "应用于"                  # 表示应用场景
      - "等同于"                  # 表示同义或等价关系

# ======================================
# 向量数据库配置
# ======================================
vector_db:
  chunk_size: 1000                    # 文本分块大小
  chunk_overlap: 200                  # 分块重叠大小
  embedding_model: "text-embedding-ada-002"  # 嵌入模型
  similarity_threshold: 0.7           # 相似度阈值

# ======================================
# 搜索配置
# ======================================
search:
  bm25_k1: 1.2        # BM25算法参数k1
  bm25_b: 0.75        # BM25算法参数b
  max_results: 10     # 最大搜索结果数

# ======================================
# 大模型API配置
# ======================================
llm:
  # Qwen API配置
  qwen:
    api_key: ""                           # API密钥
    base_url: "https://dashscope.aliyuncs.com/api/v1"  # API基础URL
    model: "qwen-turbo"                   # 模型名称
    max_tokens: 2000                      # 最大token数
    temperature: 0.7                      # 温度参数
    
  # OpenAI API配置
  openai:
    api_key: ""                           # API密钥
    base_url: "https://api.openai.com/v1" # API基础URL
    model: "gpt-3.5-turbo"               # 模型名称
    max_tokens: 2000                      # 最大token数
    temperature: 0.7                      # 温度参数

# ======================================
# 聊天配置
# ======================================
chat:
  # WhatsApp配置
  whatsapp:
    enabled: true                         # 是否启用WhatsApp
    session_path: "data/whatsapp_session" # 会话数据路径
    
  # 消息处理配置
  message:
    max_context_length: 4000              # 最大上下文长度
    response_timeout: 30                  # 响应超时时间（秒）
    auto_translate: true                  # 是否自动翻译

# ======================================
# 日志配置
# ======================================
logging:
  level: "INFO"                           # 日志级别
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"  # 日志格式
  file_enabled: true                      # 是否启用文件日志
  file_path: "logs/app.log"              # 日志文件路径

# ======================================
# 界面配置
# ======================================
ui:
  theme_name: "default"                   # 主题名称
  custom_title_bar: true                  # 是否使用自定义标题栏
  startup_size: [1600, 900]              # 启动窗口大小
  minimum_size: [1200, 700]              # 最小窗口大小
  
  # 左侧菜单配置
  left_menu_size:
    minimum: 50                           # 最小宽度
    maximum: 280                          # 最大宽度
  left_menu_content_margins: 3            # 内容边距
  
  # 左侧栏配置
  left_column_size:
    minimum: 0                            # 最小宽度
    maximum: 240                          # 最大宽度
    
  # 右侧栏配置
  right_column_size:
    minimum: 0                            # 最小宽度
    maximum: 240                          # 最大宽度
    
  # 动画配置
  time_animation: 250                     # 动画时间（毫秒）
  
  # 字体配置
  font:
    family: "Segoe UI"                    # 字体族
    title_size: 12                        # 标题字体大小
    text_size: 11                         # 正文字体大小

# ======================================
# 功能开关配置
# ======================================
features:
  knowledge_management: true              # 知识管理功能
  chat_service: true                      # 聊天服务功能
  bulk_messaging: true                    # 群发消息功能
  search_service: true                    # 搜索服务功能
  intent_analysis: true                   # 意图分析功能
  auto_reply: true                        # 自动回复功能
