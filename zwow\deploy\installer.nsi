; ZWOW 安装程序脚本
; 使用 NSIS (Nullsoft Scriptable Install System) 创建

!define APP_NAME "ZWOW"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "WOWCKER Team"
!define APP_URL "https://github.com/wowcker/zwow"
!define APP_DESCRIPTION "智能客服和获客系统"

; 安装程序属性
Name "${APP_NAME}"
OutFile "dist\${APP_NAME}_Setup_${APP_VERSION}.exe"
InstallDir "$PROGRAMFILES\${APP_NAME}"
InstallDirRegKey HKLM "Software\${APP_NAME}" "InstallDir"
RequestExecutionLevel admin

; 界面设置
!include "MUI2.nsh"
!define MUI_ABORTWARNING
!define MUI_ICON "src\pack\frontend\gui\images\icons\icon.ico"
!define MUI_UNICON "src\pack\frontend\gui\images\icons\icon.ico"

; 安装页面
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; 卸载页面
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; 语言
!insertmacro MUI_LANGUAGE "SimpChinese"

; 版本信息
VIProductVersion "${APP_VERSION}.0"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "ProductName" "${APP_NAME}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "Comments" "${APP_DESCRIPTION}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "CompanyName" "${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "LegalCopyright" "© 2024 ${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "FileDescription" "${APP_DESCRIPTION}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "FileVersion" "${APP_VERSION}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "ProductVersion" "${APP_VERSION}"

; 安装部分
Section "主程序" SecMain
    SectionIn RO
    
    ; 设置输出路径
    SetOutPath "$INSTDIR"
    
    ; 复制文件
    File /r "dist\${APP_NAME}\*.*"
    
    ; 创建开始菜单快捷方式
    CreateDirectory "$SMPROGRAMS\${APP_NAME}"
    CreateShortCut "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk" "$INSTDIR\${APP_NAME}.exe"
    CreateShortCut "$SMPROGRAMS\${APP_NAME}\卸载 ${APP_NAME}.lnk" "$INSTDIR\Uninstall.exe"
    
    ; 创建桌面快捷方式
    CreateShortCut "$DESKTOP\${APP_NAME}.lnk" "$INSTDIR\${APP_NAME}.exe"
    
    ; 写入注册表
    WriteRegStr HKLM "Software\${APP_NAME}" "InstallDir" "$INSTDIR"
    WriteRegStr HKLM "Software\${APP_NAME}" "Version" "${APP_VERSION}"
    
    ; 写入卸载信息
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayName" "${APP_NAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "UninstallString" "$INSTDIR\Uninstall.exe"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayIcon" "$INSTDIR\${APP_NAME}.exe"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "Publisher" "${APP_PUBLISHER}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "URLInfoAbout" "${APP_URL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayVersion" "${APP_VERSION}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoRepair" 1
    
    ; 创建卸载程序
    WriteUninstaller "$INSTDIR\Uninstall.exe"
    
SectionEnd

; 可选组件
Section "创建数据目录" SecData
    CreateDirectory "$INSTDIR\data"
    CreateDirectory "$INSTDIR\logs"
SectionEnd

Section "Visual C++ 运行库" SecVCRedist
    ; 检查是否需要安装 VC++ 运行库
    ; 这里可以添加检测和下载逻辑
SectionEnd

; 组件描述
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
    !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} "安装 ${APP_NAME} 主程序文件"
    !insertmacro MUI_DESCRIPTION_TEXT ${SecData} "创建数据和日志目录"
    !insertmacro MUI_DESCRIPTION_TEXT ${SecVCRedist} "安装必需的 Visual C++ 运行库"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; 卸载部分
Section "Uninstall"
    
    ; 删除文件
    RMDir /r "$INSTDIR"
    
    ; 删除开始菜单快捷方式
    RMDir /r "$SMPROGRAMS\${APP_NAME}"
    
    ; 删除桌面快捷方式
    Delete "$DESKTOP\${APP_NAME}.lnk"
    
    ; 删除注册表项
    DeleteRegKey HKLM "Software\${APP_NAME}"
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}"
    
SectionEnd

; 安装前检查
Function .onInit
    ; 检查是否已安装
    ReadRegStr $R0 HKLM "Software\${APP_NAME}" "InstallDir"
    StrCmp $R0 "" done
    
    MessageBox MB_OKCANCEL|MB_ICONEXCLAMATION \
        "${APP_NAME} 已经安装在 $R0。$\n$\n点击 '确定' 继续安装，或点击 '取消' 退出安装程序。" \
        IDOK done
    Abort
    
    done:
FunctionEnd

; 卸载前确认
Function un.onInit
    MessageBox MB_ICONQUESTION|MB_YESNO|MB_DEFBUTTON2 \
        "确定要完全卸载 ${APP_NAME} 及其所有组件吗？" \
        IDYES +2
    Abort
FunctionEnd

; 安装完成后
Function .onInstSuccess
    MessageBox MB_YESNO "${APP_NAME} 安装完成！$\n$\n是否立即运行程序？" IDNO NoRun
    Exec "$INSTDIR\${APP_NAME}.exe"
    NoRun:
FunctionEnd
