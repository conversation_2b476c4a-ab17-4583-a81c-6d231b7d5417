#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行轮询服务的脚本
"""

import os
import sys
import time
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    # Import and start the poller
    from src.pack.common_utils.poller.poller import StorePoller
    
    logger.info("正在启动轮询服务...")
    poller = StorePoller()
    success = poller.start()
    
    if success:
        logger.info("轮询服务已启动，每30秒轮询一次")
        
        # Keep the script running
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("接收到中断信号，正在停止轮询服务...")
            poller.stop()
            logger.info("轮询服务已停止")
    else:
        logger.error("轮询服务启动失败")
        
except Exception as e:
    logger.error(f"轮询服务出错: {str(e)}")
    import traceback
    traceback.print_exc()
