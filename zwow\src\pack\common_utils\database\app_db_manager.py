"""
应用数据库管理器
整合用户和店铺操作，提供简单的接口
"""
import logging
from typing import Dict, List, Tuple, Optional, Any
from .db_manager import DatabaseManager
from .user_operations import UserOperations
from .store_operations import StoreOperations
from .context_operations import ContextOperations
from .intent_operations import IntentOperations

logger = logging.getLogger(__name__)

class AppDBManager:
    """应用数据库管理器"""
    
    def __init__(self, db_path: str = "data/hermes.db"):
        """
        初始化应用数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_manager = DatabaseManager(db_path)
        self.user_ops = UserOperations(self.db_manager)
        self.store_ops = StoreOperations(self.db_manager)
        self.context_ops = ContextOperations(self.db_manager)
        self.intent_ops = IntentOperations()
        
    def initialize(self) -> bool:
        """初始化数据库"""
        return self.db_manager.init_database()
    
    # ====== 用户操作 ======
    
    def add_user(self, username: str, password: str, api_key: str = "") -> int:
        """添加用户"""
        return self.user_ops.add_user(username, password, api_key)
    
    def update_user(self, user_id: int, data: Dict[str, Any]) -> bool:
        """更新用户"""
        return self.user_ops.update_user(user_id, data)
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """用户认证"""
        return self.user_ops.authenticate_user(username, password)
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict]:
        """获取用户信息"""
        return self.user_ops.get_user_by_id(user_id)
    
    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """通过用户名获取用户"""
        return self.user_ops.get_user_by_username(username)
    
    def increment_call_count(self, user_id: int, increment: int = 1) -> bool:
        """增加API调用次数"""
        return self.user_ops.increment_call_count(user_id, increment)
    
    def delete_user(self, user_id: int) -> bool:
        """删除用户"""
        return self.user_ops.delete_user(user_id)
    
    def get_all_users(self) -> List[Dict]:
        """获取所有用户"""
        return self.user_ops.get_all_users()
    
    def user_exists(self, username: str) -> bool:
        """检查用户是否存在"""
        return self.user_ops.user_exists(username)
    
    def get_user_count(self) -> int:
        """获取用户总数"""
        return self.user_ops.get_user_count()
    
    # ====== 店铺操作 ======
    
    def add_store(self, store_name: str, is_active: bool = True, prompt: str = "", 
                  api_key: str = "", points: int = 0) -> int:
        """添加店铺"""
        return self.store_ops.add_store(store_name, is_active, prompt, api_key, points)
    
    def update_store(self, store_id: int, data: Dict[str, Any]) -> bool:
        """更新店铺"""
        return self.store_ops.update_store(store_id, data)
    
    def get_store_by_id(self, store_id: int) -> Optional[Dict]:
        """获取店铺信息"""
        return self.store_ops.get_store_by_id(store_id)
    
    def get_store_by_name(self, store_name: str) -> Optional[Dict]:
        """通过名称获取店铺"""
        return self.store_ops.get_store_by_name(store_name)
    
    def get_all_stores(self) -> List[Dict]:
        """获取所有店铺"""
        return self.store_ops.get_all_stores()
    
    def get_active_stores(self) -> List[Dict]:
        """获取所有激活的店铺"""
        return self.store_ops.get_active_stores()
    
    def delete_store(self, store_id: int) -> bool:
        """删除店铺"""
        return self.store_ops.delete_store(store_id)
    
    def toggle_store_status(self, store_id: int, is_active: bool) -> bool:
        """切换店铺状态"""
        return self.store_ops.toggle_store_status(store_id, is_active)
    
    def store_exists(self, store_name: str) -> bool:
        """检查店铺是否存在"""
        return self.store_ops.store_exists(store_name)
    
    def get_store_count(self) -> int:
        """获取店铺总数"""
        return self.store_ops.get_store_count()
    
    def update_points_atomically(self, store_id: int, points_increment: int = 1) -> Any:
        """原子性更新店铺积分"""
        return self.store_ops.update_points_atomically(store_id, points_increment)
    
    # ====== 上下文操作 ======
    
    def add_context(self, user_name: str, store_name: str, chat_id: str, context: str) -> int:
        """添加或更新上下文"""
        return self.context_ops.add_context(user_name, store_name, chat_id, context)
    
    def get_context(self, user_name: str, store_name: str, chat_id: str) -> Optional[Dict]:
        """获取特定的上下文"""
        return self.context_ops.get_context(user_name, store_name, chat_id)
    
    def update_context(self, user_name: str, store_name: str, chat_id: str, context: str) -> bool:
        """更新上下文内容"""
        return self.context_ops.update_context(user_name, store_name, chat_id, context)
    
    def get_contexts_by_user(self, user_name: str) -> List[Dict]:
        """获取用户的所有上下文"""
        return self.context_ops.get_contexts_by_user(user_name)
    
    def get_contexts_by_store(self, store_name: str) -> List[Dict]:
        """获取店铺的所有上下文"""
        return self.context_ops.get_contexts_by_store(store_name)
    
    def delete_context(self, user_name: str, store_name: str, chat_id: str) -> bool:
        """删除特定的上下文"""
        return self.context_ops.delete_context(user_name, store_name, chat_id)
    
    def delete_contexts_by_user(self, user_name: str) -> bool:
        """删除用户的所有上下文"""
        return self.context_ops.delete_contexts_by_user(user_name)
    
    def delete_contexts_by_store(self, store_name: str) -> bool:
        """删除店铺的所有上下文"""
        return self.context_ops.delete_contexts_by_store(store_name)
    
    def get_all_contexts(self) -> List[Dict]:
        """获取所有上下文"""
        return self.context_ops.get_all_contexts()
    
    def clear_old_contexts(self, days: int = 30) -> bool:
        """清理指定天数之前的上下文记录"""
        return self.context_ops.clear_old_contexts(days)
    
    def get_context_count(self) -> int:
        """获取上下文总数"""
        return self.context_ops.get_context_count()
    
    def context_exists(self, user_name: str, store_name: str, chat_id: str) -> bool:
        """检查上下文是否存在"""
        return self.context_ops.context_exists(user_name, store_name, chat_id)
    
    # ====== 意图分析操作 ======
    
    def save_intent_analysis(self, store_name: str, sender_id: str, intent_result: Dict[str, Any], 
                           message: str = '', context: str = '', manual: int = 0, username: str = '') -> bool:
        """保存意图分析结果"""
        return self.intent_ops.save_intent_analysis(store_name, sender_id, intent_result, 
                                                   message, context, manual, username)
    
    def get_intent_analysis_by_sender(self, store_name: str, sender_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取指定发送者的意图分析历史"""
        return self.intent_ops.get_intent_analysis_by_sender(store_name, sender_id, limit)
    
    def get_latest_intent_analysis(self, store_name: str, sender_id: str) -> Optional[Dict[str, Any]]:
        """获取指定发送者的最新意图分析结果"""
        return self.intent_ops.get_latest_intent_analysis(store_name, sender_id)
    
    def get_intent_analysis_by_store(self, store_name: str, limit: int = 100) -> List[Dict[str, Any]]:
        """获取指定店铺的意图分析历史"""
        return self.intent_ops.get_intent_analysis_by_store(store_name, limit)
    
    def delete_intent_analysis(self, store_name: str, sender_id: str) -> bool:
        """删除指定发送者的意图分析记录"""
        return self.intent_ops.delete_intent_analysis(store_name, sender_id)
    
    def delete_old_intent_analysis(self, store_name: str, days: int = 30) -> bool:
        """删除指定天数之前的意图分析记录"""
        return self.intent_ops.delete_old_intent_analysis(store_name, days)
    
    def get_intent_statistics(self, store_name: str) -> Dict[str, int]:
        """获取意图分析统计信息"""
        return self.intent_ops.get_intent_statistics(store_name)
    
    # ====== 登录后操作 ======
    
    def process_login(self, username: str, password: str) -> Tuple[bool, Dict, List[Dict]]:
        """
        处理登录操作
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Tuple[bool, Dict, List[Dict]]: 
                - 是否成功
                - 用户信息
                - 店铺列表
        """
        # 认证用户
        user = self.authenticate_user(username, password)
        if not user:
            return False, {}, []
        
        # 获取店铺列表
        stores = self.get_all_stores()
        
        return True, user, stores
    
    # ====== 数据库维护操作 ======
    
    def cleanup_old_data(self, days: int = 30) -> bool:
        """
        清理旧数据
        
        Args:
            days: 保留天数
            
        Returns:
            bool: 操作是否成功
        """
        try:
            # 清理旧的上下文记录
            context_result = self.clear_old_contexts(days)
            
            # 清理旧的意图分析记录（需要遍历所有店铺）
            stores = self.get_all_stores()
            intent_result = True
            for store in stores:
                store_name = store.get('store_name', store.get('plg_shopname', ''))
                if store_name:
                    result = self.delete_old_intent_analysis(store_name, days)
                    intent_result = intent_result and result
            
            return context_result and intent_result
        except Exception as e:
            logger.error(f"清理旧数据时出错: {str(e)}")
            return False
    
    def get_database_statistics(self) -> Dict[str, int]:
        """
        获取数据库统计信息
        
        Returns:
            Dict[str, int]: 统计信息字典
        """
        try:
            stats = {
                'user_count': self.get_user_count(),
                'store_count': self.get_store_count(),
                'context_count': self.get_context_count(),
                'active_store_count': len(self.get_active_stores())
            }
            return stats
        except Exception as e:
            logger.error(f"获取数据库统计信息时出错: {str(e)}")
            return {}
    
    def close(self):
        """关闭数据库连接"""
        if hasattr(self.db_manager, 'close'):
            self.db_manager.close()
