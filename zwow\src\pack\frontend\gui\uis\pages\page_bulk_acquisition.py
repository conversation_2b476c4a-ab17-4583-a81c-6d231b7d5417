# ///////////////////////////////////////////////////////////////
#
# 作者: WANDERSON M.PIMENTA
# 项目基于: Qt Designer 和 PySide6
# 版本: 1.0.0
#
# 本项目可以免费用于任何用途，但必须在Python脚本中保留相应的版权信息。
# 可以自由修改可视化界面(GUI)中的任何信息，不会有任何影响。
#
# 如果您想将产品商业化，Qt许可证有一些限制，
# 建议您阅读官方网站的相关内容：
# https://doc.qt.io/qtforpython/licenses.html
#
# ///////////////////////////////////////////////////////////////

# 导入包和模块
# ///////////////////////////////////////////////////////////////
from qt_core import *
from gui.widgets import *
from gui.core.json_themes import Themes
from gui.core.functions import Functions
import sys
from pathlib import Path

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.backend.bulk_acquisition.acquisition_service import AcquisitionService
except ImportError as e:
    print(f"导入获客服务失败: {e}")
    class AcquisitionService:
        def __init__(self): pass
        def send_bulk_messages(self, phones, message, **kwargs): return {'success': False}
        def get_sending_status(self): return {'is_sending': False}
        def cancel_sending(self): pass

# 群发获客页面主类
# ///////////////////////////////////////////////////////////////
class BulkAcquisitionPage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.phone_numbers = []  # 存储电话号码列表
        self.acquisition_service = AcquisitionService()
        self.setup_ui()
        
    def setup_ui(self):
        # 加载主题
        themes = Themes()
        self.themes = themes.items
        
        # 主布局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(20, 20, 20, 20)
        self.main_layout.setSpacing(20)
        
        # 页面标题
        self.setup_title()
        
        # 主要内容区域
        self.setup_content_area()
        
        # 设置整体样式
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {self.themes['app_color']['bg_one']};
                color: {self.themes['app_color']['text_foreground']};
            }}
        """)
        
    def setup_title(self):
        """设置页面标题"""
        self.title_label = QLabel("群发获客")
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet(f"""
            QLabel {{
                color: {self.themes['app_color']['text_foreground']};
                font-size: 24px;
                font-weight: bold;
                padding: 10px;
            }}
        """)
        self.main_layout.addWidget(self.title_label)
        
    def setup_content_area(self):
        """设置主要内容区域"""
        # 内容容器
        self.content_container = QFrame()
        self.content_container.setStyleSheet(f"""
            QFrame {{
                background-color: {self.themes['app_color']['bg_two']};
                border-radius: 10px;
                border: 1px solid {self.themes['app_color']['dark_three']};
            }}
        """)
        
        content_layout = QHBoxLayout(self.content_container)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(20)
        
        # 左侧区域 - 电话号码表格
        self.setup_phone_table_area(content_layout)
        
        # 右侧区域 - 操作面板
        self.setup_operation_area(content_layout)
        
        self.main_layout.addWidget(self.content_container)
        
    def setup_phone_table_area(self, parent_layout):
        """设置电话号码表格区域"""
        # 左侧容器
        left_container = QFrame()
        left_container.setMinimumWidth(400)
        
        left_layout = QVBoxLayout(left_container)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(10)
        
        # 表格标题
        table_title = QLabel("电话号码列表")
        table_title.setStyleSheet(f"""
            QLabel {{
                color: {self.themes['app_color']['text_foreground']};
                font-size: 16px;
                font-weight: bold;
                padding: 5px;
            }}
        """)
        left_layout.addWidget(table_title)
        
        # 电话号码表格
        self.phone_table = QTableWidget()
        self.phone_table.setColumnCount(3)
        self.phone_table.setHorizontalHeaderLabels(["序号", "电话号码", "状态"])
        
        # 设置表格样式
        self.phone_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {self.themes['app_color']['bg_one']};
                border: 1px solid {self.themes['app_color']['dark_three']};
                border-radius: 5px;
                gridline-color: {self.themes['app_color']['dark_three']};
                color: {self.themes['app_color']['text_foreground']};
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {self.themes['app_color']['dark_three']};
            }}
            QTableWidget::item:selected {{
                background-color: {self.themes['app_color']['context_color']};
            }}
            QHeaderView::section {{
                background-color: {self.themes['app_color']['bg_three']};
                color: {self.themes['app_color']['text_foreground']};
                padding: 8px;
                border: none;
                font-weight: bold;
            }}
        """)
        
        # 设置表格属性
        self.phone_table.horizontalHeader().setStretchLastSection(True)
        self.phone_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.phone_table.setAlternatingRowColors(True)
        
        left_layout.addWidget(self.phone_table)
        
        parent_layout.addWidget(left_container, 2)  # 占2/3宽度
        
    def setup_operation_area(self, parent_layout):
        """设置操作区域"""
        # 右侧容器
        right_container = QFrame()
        right_container.setMaximumWidth(300)
        
        right_layout = QVBoxLayout(right_container)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(15)
        
        # 操作区域标题
        operation_title = QLabel("操作区域")
        operation_title.setStyleSheet(f"""
            QLabel {{
                color: {self.themes['app_color']['text_foreground']};
                font-size: 16px;
                font-weight: bold;
                padding: 5px;
            }}
        """)
        right_layout.addWidget(operation_title)
        
        # 电话号码输入区域
        self.setup_phone_input_section(right_layout)
        
        # 消息输入区域
        self.setup_message_input_section(right_layout)
        
        # 操作按钮区域
        self.setup_action_buttons(right_layout)
        
        # 添加弹性空间
        right_layout.addStretch()
        
        parent_layout.addWidget(right_container, 1)  # 占1/3宽度
        
    def setup_phone_input_section(self, parent_layout):
        """设置电话号码输入区域"""
        # 输入标签
        phone_label = QLabel("添加电话号码:")
        phone_label.setStyleSheet(f"""
            QLabel {{
                color: {self.themes['app_color']['text_foreground']};
                font-size: 14px;
                font-weight: 500;
            }}
        """)
        parent_layout.addWidget(phone_label)
        
        # 电话号码输入框
        self.phone_input = PyLineEdit(
            place_holder_text="请输入电话号码",
            radius=8,
            border_size=2,
            color=self.themes['app_color']['text_foreground'],
            selection_color=self.themes['app_color']['text_foreground'],
            bg_color=self.themes['app_color']['bg_one'],
            bg_color_active=self.themes['app_color']['bg_three'],
            context_color=self.themes['app_color']['context_color']
        )
        self.phone_input.setFixedHeight(40)
        self.phone_input.returnPressed.connect(self.add_phone_number)
        parent_layout.addWidget(self.phone_input)
        
    def setup_message_input_section(self, parent_layout):
        """设置消息输入区域"""
        # 消息标签
        message_label = QLabel("群发消息内容:")
        message_label.setStyleSheet(f"""
            QLabel {{
                color: {self.themes['app_color']['text_foreground']};
                font-size: 14px;
                font-weight: 500;
            }}
        """)
        parent_layout.addWidget(message_label)
        
        # 消息输入框
        self.message_input = QTextEdit()
        self.message_input.setPlaceholderText("请输入要群发的消息内容...")
        self.message_input.setFixedHeight(120)
        self.message_input.setStyleSheet(f"""
            QTextEdit {{
                background-color: {self.themes['app_color']['bg_one']};
                border: 2px solid {self.themes['app_color']['bg_three']};
                border-radius: 8px;
                color: {self.themes['app_color']['text_foreground']};
                padding: 10px;
                font-size: 14px;
            }}
            QTextEdit:focus {{
                border: 2px solid {self.themes['app_color']['context_color']};
            }}
        """)
        parent_layout.addWidget(self.message_input)
        
    def setup_action_buttons(self, parent_layout):
        """设置操作按钮"""
        # 添加电话号码按钮
        self.add_phone_btn = PyPushButton(
            text="添加电话号码",
            radius=8,
            color=self.themes['app_color']['white'],
            bg_color=self.themes['app_color']['green'],
            bg_color_hover=self.themes['app_color']['context_hover'],
            bg_color_pressed=self.themes['app_color']['context_pressed']
        )
        self.add_phone_btn.setFixedHeight(40)
        self.add_phone_btn.clicked.connect(self.add_phone_number)
        parent_layout.addWidget(self.add_phone_btn)
        
        # 开始群发按钮
        self.start_bulk_btn = PyPushButton(
            text="开始群发",
            radius=8,
            color=self.themes['app_color']['white'],
            bg_color=self.themes['app_color']['context_color'],
            bg_color_hover=self.themes['app_color']['context_hover'],
            bg_color_pressed=self.themes['app_color']['context_pressed']
        )
        self.start_bulk_btn.setFixedHeight(40)
        self.start_bulk_btn.clicked.connect(self.start_bulk_send)
        parent_layout.addWidget(self.start_bulk_btn)
        
        # 清空列表按钮
        self.clear_list_btn = PyPushButton(
            text="清空列表",
            radius=8,
            color=self.themes['app_color']['white'],
            bg_color=self.themes['app_color']['red'],
            bg_color_hover="#e74c3c",
            bg_color_pressed="#c0392b"
        )
        self.clear_list_btn.setFixedHeight(40)
        self.clear_list_btn.clicked.connect(self.clear_phone_list)
        parent_layout.addWidget(self.clear_list_btn)
        
    def add_phone_number(self):
        """添加电话号码"""
        phone = self.phone_input.text().strip()
        if not phone:
            return
        
        # 验证电话号码格式
        if not self._validate_phone_number(phone):
            QMessageBox.warning(self, "格式错误", "请输入正确的电话号码格式")
            return
        
        # 检查重复
        if phone in self.phone_numbers:
            QMessageBox.information(self, "重复号码", "该电话号码已存在")
            return
        
        # 添加到列表
        self.phone_numbers.append(phone)
        self._update_phone_table()
        
        # 清空输入框
        self.phone_input.clear()
        
    def _validate_phone_number(self, phone: str) -> bool:
        """验证电话号码格式"""
        import re
        
        # 清理号码
        cleaned_phone = re.sub(r'[^\d+]', '', phone)
        
        # 验证国际格式 (+86138****8888)
        international_pattern = r'^\+\d{1,3}\d{10,14}$'
        if re.match(international_pattern, cleaned_phone):
            return True
        
        # 验证国内格式 (138****8888)
        domestic_pattern = r'^1[3-9]\d{9}$'
        if re.match(domestic_pattern, cleaned_phone):
            return True
        
        return False
        
    def _update_phone_table(self):
        """更新电话号码表格"""
        self.phone_table.setRowCount(len(self.phone_numbers))
        
        for i, phone in enumerate(self.phone_numbers):
            # 序号
            self.phone_table.setItem(i, 0, QTableWidgetItem(str(i + 1)))
            # 电话号码
            self.phone_table.setItem(i, 1, QTableWidgetItem(phone))
            # 状态
            self.phone_table.setItem(i, 2, QTableWidgetItem("待发送"))
            
    def start_bulk_send(self):
        """开始群发"""
        if not self.phone_numbers:
            QMessageBox.warning(self, "无号码", "请先添加电话号码")
            return
        
        message = self.message_input.toPlainText().strip()
        if not message:
            QMessageBox.warning(self, "无消息", "请输入要发送的消息内容")
            return
        
        # 确认发送
        reply = QMessageBox.question(
            self,
            "确认群发",
            f"确定要向 {len(self.phone_numbers)} 个号码发送消息吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
        
        # 禁用按钮
        self.start_bulk_btn.setEnabled(False)
        self.start_bulk_btn.setText("发送中...")
        
        # 开始发送
        result = self.acquisition_service.send_bulk_messages(
            self.phone_numbers,
            message,
            progress_callback=self.on_progress_updated,
            completion_callback=self.on_send_completed
        )
        
        if not result.get('success'):
            QMessageBox.critical(self, "发送失败", result.get('error', '未知错误'))
            self.start_bulk_btn.setEnabled(True)
            self.start_bulk_btn.setText("开始群发")
            
    def on_progress_updated(self, progress: int, status: str):
        """进度更新回调"""
        self.start_bulk_btn.setText(f"发送中... {progress}%")
        
    def on_send_completed(self, result):
        """发送完成回调"""
        self.start_bulk_btn.setEnabled(True)
        self.start_bulk_btn.setText("开始群发")
        
        if result:
            QMessageBox.information(
                self,
                "发送完成",
                f"群发完成！\n成功: {result.success_count}\n失败: {result.failed_count}"
            )
        else:
            QMessageBox.critical(self, "发送失败", "群发过程中出现错误")
            
    def clear_phone_list(self):
        """清空电话号码列表"""
        if not self.phone_numbers:
            return
            
        reply = QMessageBox.question(
            self,
            "确认清空",
            "确定要清空所有电话号码吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.phone_numbers.clear()
            self._update_phone_table()

# 创建群发获客页面实例的函数
# ///////////////////////////////////////////////////////////////
def create_bulk_acquisition_page():
    """创建群发获客页面实例"""
    return BulkAcquisitionPage()
