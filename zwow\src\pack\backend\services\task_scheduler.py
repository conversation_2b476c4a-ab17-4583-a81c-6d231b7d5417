"""
任务调度器
管理定时任务和后台任务的调度执行
"""

import time
import threading
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.settings_loader import get_setting
except ImportError as e:
    print(f"导入配置模块失败: {e}")
    def get_setting(key, default=None): return default

# 配置日志
logger = logging.getLogger(__name__)

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskType(Enum):
    """任务类型枚举"""
    ONCE = "once"          # 一次性任务
    INTERVAL = "interval"  # 间隔任务
    CRON = "cron"         # 定时任务

@dataclass
class Task:
    """任务数据类"""
    id: str
    name: str
    func: Callable
    task_type: TaskType
    status: TaskStatus = TaskStatus.PENDING
    
    # 调度参数
    interval: Optional[int] = None  # 间隔秒数
    cron_expression: Optional[str] = None  # cron表达式
    next_run_time: Optional[datetime] = None
    
    # 执行信息
    created_time: datetime = None
    last_run_time: Optional[datetime] = None
    run_count: int = 0
    error_count: int = 0
    last_error: Optional[str] = None
    
    # 其他参数
    args: tuple = ()
    kwargs: dict = None
    max_retries: int = 3
    timeout: Optional[int] = None
    
    def __post_init__(self):
        if self.kwargs is None:
            self.kwargs = {}
        if self.created_time is None:
            self.created_time = datetime.now()

class TaskScheduler:
    """任务调度器类"""
    
    def __init__(self):
        """初始化任务调度器"""
        # 任务存储
        self.tasks = {}  # task_id -> Task
        
        # 运行状态
        self.running = False
        self.scheduler_thread = None
        
        # 配置
        self.check_interval = get_setting("scheduler.check_interval", 1)  # 检查间隔（秒）
        self.max_concurrent_tasks = get_setting("scheduler.max_concurrent_tasks", 10)
        
        # 执行器
        self.executor_threads = {}  # task_id -> thread
        
        logger.info("任务调度器已初始化")
    
    def start(self) -> bool:
        """
        启动调度器
        
        Returns:
            bool: 启动是否成功
        """
        if self.running:
            logger.warning("任务调度器已在运行")
            return True
        
        try:
            self.running = True
            self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
            self.scheduler_thread.start()
            logger.info("任务调度器已启动")
            return True
        except Exception as e:
            logger.error(f"启动任务调度器时出错: {str(e)}")
            self.running = False
            return False
    
    def stop(self) -> bool:
        """
        停止调度器
        
        Returns:
            bool: 停止是否成功
        """
        if not self.running:
            logger.warning("任务调度器未在运行")
            return True
        
        try:
            self.running = False
            
            # 等待调度器线程结束
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=5)
            
            # 停止所有正在执行的任务
            self._stop_all_running_tasks()
            
            logger.info("任务调度器已停止")
            return True
        except Exception as e:
            logger.error(f"停止任务调度器时出错: {str(e)}")
            return False
    
    def add_task(self, task_id: str, name: str, func: Callable, 
                task_type: TaskType, **kwargs) -> bool:
        """
        添加任务
        
        Args:
            task_id: 任务ID
            name: 任务名称
            func: 任务函数
            task_type: 任务类型
            **kwargs: 其他任务参数
            
        Returns:
            bool: 添加是否成功
        """
        try:
            if task_id in self.tasks:
                logger.warning(f"任务 {task_id} 已存在")
                return False
            
            # 创建任务
            task = Task(
                id=task_id,
                name=name,
                func=func,
                task_type=task_type,
                **kwargs
            )
            
            # 设置下次运行时间
            self._set_next_run_time(task)
            
            # 添加到任务列表
            self.tasks[task_id] = task
            
            logger.info(f"已添加任务: {name} ({task_id})")
            return True
            
        except Exception as e:
            logger.error(f"添加任务时出错: {str(e)}")
            return False
    
    def remove_task(self, task_id: str) -> bool:
        """
        移除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 移除是否成功
        """
        try:
            if task_id not in self.tasks:
                logger.warning(f"任务 {task_id} 不存在")
                return False
            
            task = self.tasks[task_id]
            
            # 如果任务正在运行，先停止
            if task.status == TaskStatus.RUNNING:
                self._stop_task(task_id)
            
            # 从任务列表中移除
            del self.tasks[task_id]
            
            logger.info(f"已移除任务: {task.name} ({task_id})")
            return True
            
        except Exception as e:
            logger.error(f"移除任务时出错: {str(e)}")
            return False
    
    def add_interval_task(self, task_id: str, name: str, func: Callable, 
                         interval: int, **kwargs) -> bool:
        """
        添加间隔任务
        
        Args:
            task_id: 任务ID
            name: 任务名称
            func: 任务函数
            interval: 间隔秒数
            **kwargs: 其他参数
            
        Returns:
            bool: 添加是否成功
        """
        return self.add_task(
            task_id, name, func, TaskType.INTERVAL,
            interval=interval, **kwargs
        )
    
    def add_once_task(self, task_id: str, name: str, func: Callable, 
                     delay: int = 0, **kwargs) -> bool:
        """
        添加一次性任务
        
        Args:
            task_id: 任务ID
            name: 任务名称
            func: 任务函数
            delay: 延迟秒数
            **kwargs: 其他参数
            
        Returns:
            bool: 添加是否成功
        """
        next_run_time = datetime.now() + timedelta(seconds=delay)
        return self.add_task(
            task_id, name, func, TaskType.ONCE,
            next_run_time=next_run_time, **kwargs
        )
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """
        获取任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Task]: 任务对象
        """
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> List[Task]:
        """
        获取所有任务
        
        Returns:
            List[Task]: 任务列表
        """
        return list(self.tasks.values())
    
    def get_tasks_by_status(self, status: TaskStatus) -> List[Task]:
        """
        根据状态获取任务
        
        Args:
            status: 任务状态
            
        Returns:
            List[Task]: 任务列表
        """
        return [task for task in self.tasks.values() if task.status == status]
    
    def _scheduler_loop(self):
        """调度器主循环"""
        logger.info("任务调度器主循环已启动")
        
        while self.running:
            try:
                current_time = datetime.now()
                
                # 检查需要执行的任务
                for task in self.tasks.values():
                    if self._should_run_task(task, current_time):
                        self._execute_task(task)
                
                # 清理已完成的一次性任务
                self._cleanup_completed_tasks()
                
            except Exception as e:
                logger.error(f"调度器循环中出错: {str(e)}")
            
            # 等待下次检查
            time.sleep(self.check_interval)
        
        logger.info("任务调度器主循环已结束")
    
    def _should_run_task(self, task: Task, current_time: datetime) -> bool:
        """
        检查任务是否应该运行
        
        Args:
            task: 任务对象
            current_time: 当前时间
            
        Returns:
            bool: 是否应该运行
        """
        # 检查任务状态
        if task.status == TaskStatus.RUNNING:
            return False
        
        if task.status in [TaskStatus.CANCELLED, TaskStatus.COMPLETED]:
            return False
        
        # 检查运行时间
        if task.next_run_time and current_time >= task.next_run_time:
            return True
        
        return False
    
    def _execute_task(self, task: Task):
        """
        执行任务
        
        Args:
            task: 任务对象
        """
        try:
            # 检查并发限制
            running_count = len([t for t in self.tasks.values() if t.status == TaskStatus.RUNNING])
            if running_count >= self.max_concurrent_tasks:
                logger.warning(f"达到最大并发任务数限制: {self.max_concurrent_tasks}")
                return
            
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.last_run_time = datetime.now()
            task.run_count += 1
            
            # 创建执行线程
            executor_thread = threading.Thread(
                target=self._task_executor,
                args=(task,),
                daemon=True
            )
            
            self.executor_threads[task.id] = executor_thread
            executor_thread.start()
            
            logger.debug(f"开始执行任务: {task.name} ({task.id})")
            
        except Exception as e:
            logger.error(f"执行任务时出错: {str(e)}")
            task.status = TaskStatus.FAILED
            task.error_count += 1
            task.last_error = str(e)
    
    def _task_executor(self, task: Task):
        """
        任务执行器
        
        Args:
            task: 任务对象
        """
        try:
            # 执行任务函数
            if task.timeout:
                # 带超时的执行
                import signal
                
                def timeout_handler(signum, frame):
                    raise TimeoutError(f"任务执行超时: {task.timeout}秒")
                
                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(task.timeout)
                
                try:
                    task.func(*task.args, **task.kwargs)
                finally:
                    signal.alarm(0)
            else:
                # 普通执行
                task.func(*task.args, **task.kwargs)
            
            # 任务执行成功
            if task.task_type == TaskType.ONCE:
                task.status = TaskStatus.COMPLETED
            else:
                task.status = TaskStatus.PENDING
                self._set_next_run_time(task)
            
            logger.debug(f"任务执行成功: {task.name} ({task.id})")
            
        except Exception as e:
            # 任务执行失败
            task.error_count += 1
            task.last_error = str(e)
            
            logger.error(f"任务执行失败: {task.name} ({task.id}) - {str(e)}")
            
            # 检查重试
            if task.error_count < task.max_retries:
                task.status = TaskStatus.PENDING
                # 延迟重试
                task.next_run_time = datetime.now() + timedelta(seconds=60)
            else:
                task.status = TaskStatus.FAILED
                logger.error(f"任务达到最大重试次数: {task.name} ({task.id})")
        
        finally:
            # 清理执行线程记录
            if task.id in self.executor_threads:
                del self.executor_threads[task.id]
    
    def _set_next_run_time(self, task: Task):
        """
        设置任务的下次运行时间
        
        Args:
            task: 任务对象
        """
        if task.task_type == TaskType.INTERVAL and task.interval:
            task.next_run_time = datetime.now() + timedelta(seconds=task.interval)
        elif task.task_type == TaskType.CRON and task.cron_expression:
            # 这里可以实现cron表达式解析
            # 目前简化为每小时执行一次
            task.next_run_time = datetime.now() + timedelta(hours=1)
    
    def _cleanup_completed_tasks(self):
        """清理已完成的一次性任务"""
        try:
            completed_tasks = [
                task_id for task_id, task in self.tasks.items()
                if task.task_type == TaskType.ONCE and task.status == TaskStatus.COMPLETED
            ]
            
            for task_id in completed_tasks:
                # 保留任务一段时间后再删除
                task = self.tasks[task_id]
                if task.last_run_time:
                    elapsed = datetime.now() - task.last_run_time
                    if elapsed.total_seconds() > 3600:  # 1小时后删除
                        del self.tasks[task_id]
                        logger.debug(f"清理已完成任务: {task.name} ({task_id})")
                        
        except Exception as e:
            logger.error(f"清理任务时出错: {str(e)}")
    
    def _stop_task(self, task_id: str):
        """
        停止指定任务
        
        Args:
            task_id: 任务ID
        """
        try:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                task.status = TaskStatus.CANCELLED
                
                # 如果有执行线程，尝试停止
                if task_id in self.executor_threads:
                    thread = self.executor_threads[task_id]
                    # 注意：Python线程无法强制停止，只能设置标志
                    logger.warning(f"无法强制停止任务线程: {task_id}")
                    
        except Exception as e:
            logger.error(f"停止任务时出错: {str(e)}")
    
    def _stop_all_running_tasks(self):
        """停止所有正在运行的任务"""
        try:
            running_tasks = [
                task_id for task_id, task in self.tasks.items()
                if task.status == TaskStatus.RUNNING
            ]
            
            for task_id in running_tasks:
                self._stop_task(task_id)
                
        except Exception as e:
            logger.error(f"停止所有任务时出错: {str(e)}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取调度器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            total_tasks = len(self.tasks)
            status_counts = {}
            
            for task in self.tasks.values():
                status = task.status.value
                status_counts[status] = status_counts.get(status, 0) + 1
            
            return {
                'running': self.running,
                'total_tasks': total_tasks,
                'status_counts': status_counts,
                'running_threads': len(self.executor_threads),
                'check_interval': self.check_interval,
                'max_concurrent_tasks': self.max_concurrent_tasks
            }
            
        except Exception as e:
            logger.error(f"获取统计信息时出错: {str(e)}")
            return {}
