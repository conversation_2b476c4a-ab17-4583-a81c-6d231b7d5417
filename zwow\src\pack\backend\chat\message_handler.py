"""
消息处理器
处理聊天消息的核心逻辑，包括意图分析、知识库搜索、回复生成等
"""

import logging
import time
from typing import Dict, List, Any, Optional
from pathlib import Path
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.common_utils.database.app_db_manager import AppDBManager
    from src.pack.config.settings_loader import get_setting
except ImportError as e:
    print(f"导入错误: {e}")
    # 创建模拟类以避免错误
    class AppDBManager:
        def __init__(self): pass
        def initialize(self): return True
        def get_store_by_name(self, name): return {"plg_status": 1}
        def get_context(self, user, store, chat): return None
        def add_context(self, user, store, chat, context): return True
    def get_setting(key, default=None): return default

logger = logging.getLogger(__name__)

class MessageHandler:
    """消息处理器类"""
    
    def __init__(self, store_name: str, username: str = None):
        """
        初始化消息处理器
        
        Args:
            store_name: 店铺名称
            username: 用户名
        """
        self.store_name = store_name
        self.username = username or "default_user"
        
        # 初始化数据库管理器
        self.db_manager = AppDBManager()
        self.db_manager.initialize()
        
        # 获取配置
        self.max_context_length = get_setting("chat.message.max_context_length", 4000)
        self.response_timeout = get_setting("chat.message.response_timeout", 30)
        self.auto_translate = get_setting("chat.message.auto_translate", True)
        
        logger.info(f"消息处理器已初始化，店铺: {store_name}, 用户: {self.username}")
    
    def process_message(self, message: Dict[str, Any]) -> Optional[str]:
        """
        处理消息并生成回复
        
        Args:
            message: 消息字典，包含from（发送者ID）和body（消息内容）
            
        Returns:
            Optional[str]: 生成的回复内容，如果不需要回复则返回None
        """
        try:
            sender_id = message.get("from")
            message_body = message.get("body", "")
            
            if not sender_id or not message_body:
                logger.warning("收到无效消息")
                return None
            
            logger.info(f"处理来自 {sender_id} 的消息: {message_body}")
            
            # 1. 检查店铺状态
            if not self._check_store_status():
                logger.warning(f"店铺 {self.store_name} 状态异常，拒绝处理消息")
                return None
            
            # 2. 检查是否处于人工介入状态
            if self._check_manual_intervention(sender_id):
                logger.info(f"用户 {sender_id} 处于人工介入模式，跳过自动回复")
                return None
            
            # 3. 获取上下文
            context = self._get_context(sender_id)
            
            # 4. 翻译消息（如果启用）
            translated_message = self._translate_message(message_body) if self.auto_translate else message_body
            
            # 5. 进行意图分析
            intent_result = self._analyze_intent(translated_message, context)
            
            # 6. 保存意图分析结果
            self._save_intent_analysis(sender_id, intent_result, message_body, context)
            
            # 7. 检查是否需要人工介入
            if intent_result.get("stage") == "post-sales":
                logger.info(f"用户 {sender_id} 需要人工介入，不生成自动回复")
                return None
            
            # 8. 生成回复
            reply = self._generate_reply(translated_message, context, intent_result)
            
            # 9. 更新上下文
            self._update_context(sender_id, message_body, reply)
            
            return reply
            
        except Exception as e:
            logger.error(f"处理消息时出错: {str(e)}")
            return None
    
    def _check_store_status(self) -> bool:
        """
        检查店铺状态
        
        Returns:
            bool: 店铺是否可用
        """
        try:
            store_info = self.db_manager.get_store_by_name(self.store_name)
            if not store_info:
                logger.error(f"找不到店铺: {self.store_name}")
                return False
            
            status = store_info.get('plg_status', 1)
            if status == 0:
                logger.warning(f"店铺 {self.store_name} 已关闭")
                return False
            
            return True
        except Exception as e:
            logger.error(f"检查店铺状态时出错: {str(e)}")
            return False
    
    def _check_manual_intervention(self, sender_id: str) -> bool:
        """
        检查是否处于人工介入状态
        
        Args:
            sender_id: 发送者ID
            
        Returns:
            bool: 是否处于人工介入状态
        """
        try:
            # 获取最新的意图分析结果
            intent_analysis = self.db_manager.get_latest_intent_analysis(self.store_name, sender_id)
            if intent_analysis and intent_analysis.get("manual", 0) == 1:
                return True
            return False
        except Exception as e:
            logger.error(f"检查人工介入状态时出错: {str(e)}")
            return False
    
    def _get_context(self, sender_id: str) -> str:
        """
        获取聊天上下文
        
        Args:
            sender_id: 发送者ID
            
        Returns:
            str: 上下文内容
        """
        try:
            context_record = self.db_manager.get_context(self.username, self.store_name, sender_id)
            return context_record.get("context", "") if context_record else ""
        except Exception as e:
            logger.error(f"获取上下文时出错: {str(e)}")
            return ""
    
    def _translate_message(self, message: str) -> str:
        """
        翻译消息（模拟实现）
        
        Args:
            message: 原始消息
            
        Returns:
            str: 翻译后的消息
        """
        # 这里应该调用实际的翻译服务
        # 目前返回原消息作为占位符
        logger.debug(f"翻译消息: {message}")
        return message
    
    def _analyze_intent(self, message: str, context: str) -> Dict[str, Any]:
        """
        分析用户意图（模拟实现）
        
        Args:
            message: 消息内容
            context: 上下文
            
        Returns:
            Dict[str, Any]: 意图分析结果
        """
        # 这里应该调用实际的意图分析服务
        # 目前返回模拟结果
        logger.debug(f"分析意图: {message}")
        
        # 简单的关键词匹配
        if any(keyword in message.lower() for keyword in ["hello", "hi", "你好", "您好"]):
            return {
                "stage": "pre-sales",
                "specific_intent": "greeting",
                "source": "rule-based",
                "reasoning": "Greeting detected"
            }
        elif any(keyword in message.lower() for keyword in ["price", "cost", "价格", "多少钱"]):
            return {
                "stage": "pre-sales", 
                "specific_intent": "price_inquiry",
                "source": "rule-based",
                "reasoning": "Price inquiry detected"
            }
        elif any(keyword in message.lower() for keyword in ["problem", "issue", "问题", "故障"]):
            return {
                "stage": "post-sales",
                "specific_intent": "technical_support",
                "source": "rule-based", 
                "reasoning": "Technical support needed"
            }
        else:
            return {
                "stage": "general",
                "specific_intent": "general_inquiry",
                "source": "rule-based",
                "reasoning": "General inquiry"
            }
    
    def _save_intent_analysis(self, sender_id: str, intent_result: Dict[str, Any], 
                            message: str, context: str) -> None:
        """
        保存意图分析结果
        
        Args:
            sender_id: 发送者ID
            intent_result: 意图分析结果
            message: 原始消息
            context: 上下文
        """
        try:
            manual = 1 if intent_result.get("stage") == "post-sales" else 0
            self.db_manager.save_intent_analysis(
                self.store_name,
                sender_id,
                intent_result,
                message,
                context,
                manual,
                self.username
            )
            logger.debug(f"已保存意图分析结果: {intent_result}")
        except Exception as e:
            logger.error(f"保存意图分析结果时出错: {str(e)}")
    
    def _generate_reply(self, message: str, context: str, intent_result: Dict[str, Any]) -> str:
        """
        生成回复（模拟实现）
        
        Args:
            message: 消息内容
            context: 上下文
            intent_result: 意图分析结果
            
        Returns:
            str: 生成的回复
        """
        # 这里应该调用实际的回复生成服务（如LLM API）
        # 目前返回基于意图的模板回复
        
        stage = intent_result.get("stage", "general")
        specific_intent = intent_result.get("specific_intent", "general_inquiry")
        
        if stage == "pre-sales":
            if specific_intent == "greeting":
                return "您好！欢迎咨询我们的产品和服务，有什么可以帮助您的吗？"
            elif specific_intent == "price_inquiry":
                return "感谢您对我们产品的关注！具体价格请根据您的需求来定制，请告诉我您的具体要求。"
            else:
                return "感谢您的咨询！我们会尽快为您提供详细信息。"
        elif stage == "post-sales":
            return "我已经记录了您的问题，我们的技术支持团队会尽快与您联系。"
        else:
            return "感谢您的消息！如果您有任何问题，请随时告诉我。"
    
    def _update_context(self, sender_id: str, user_message: str, bot_reply: str) -> None:
        """
        更新聊天上下文
        
        Args:
            sender_id: 发送者ID
            user_message: 用户消息
            bot_reply: 机器人回复
        """
        try:
            # 获取现有上下文
            existing_context = self._get_context(sender_id)
            
            # 构建新的上下文条目
            new_entry = f"用户: {user_message}\n助手: {bot_reply}\n"
            
            # 合并上下文
            updated_context = existing_context + new_entry
            
            # 如果上下文过长，截断旧的部分
            if len(updated_context) > self.max_context_length:
                # 保留最近的对话
                lines = updated_context.split('\n')
                while len('\n'.join(lines)) > self.max_context_length and len(lines) > 2:
                    lines.pop(0)
                updated_context = '\n'.join(lines)
            
            # 保存更新后的上下文
            self.db_manager.add_context(self.username, self.store_name, sender_id, updated_context)
            logger.debug(f"已更新用户 {sender_id} 的上下文")
            
        except Exception as e:
            logger.error(f"更新上下文时出错: {str(e)}")
    
    def get_conversation_history(self, sender_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取对话历史
        
        Args:
            sender_id: 发送者ID
            limit: 返回的历史记录数量限制
            
        Returns:
            List[Dict[str, Any]]: 对话历史列表
        """
        try:
            # 获取意图分析历史作为对话历史的代理
            return self.db_manager.get_intent_analysis_by_sender(self.store_name, sender_id, limit)
        except Exception as e:
            logger.error(f"获取对话历史时出错: {str(e)}")
            return []
    
    def clear_context(self, sender_id: str) -> bool:
        """
        清空指定用户的上下文
        
        Args:
            sender_id: 发送者ID
            
        Returns:
            bool: 操作是否成功
        """
        try:
            return self.db_manager.delete_context(self.username, self.store_name, sender_id)
        except Exception as e:
            logger.error(f"清空上下文时出错: {str(e)}")
            return False
