[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "LocationPermission"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Extended", "value": "QLocationPermission"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}], "className": "QQmlQLocationPermission", "lineNumber": 68, "methods": [{"access": "public", "index": 3, "name": "request", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Qt::PermissionStatus", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "availability", "notify": "availabilityChanged", "read": "availability", "required": false, "scriptable": true, "stored": true, "type": "QLocationPermission::Availability", "user": false, "write": "setAvailability"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "accuracy", "notify": "accuracyChanged", "read": "accuracy", "required": false, "scriptable": true, "stored": true, "type": "QLocationPermission::Accuracy", "user": false, "write": "setAccuracy"}], "qualifiedClassName": "QQmlQLocationPermission", "signals": [{"access": "public", "index": 0, "name": "statusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "availabilityChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "accuracyChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "CalendarPermission"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Extended", "value": "QCalendarPermission"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}], "className": "QQmlCalendarPermission", "lineNumber": 77, "methods": [{"access": "public", "index": 2, "name": "request", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Qt::PermissionStatus", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "accessMode", "notify": "accessModeChanged", "read": "accessMode", "required": false, "scriptable": true, "stored": true, "type": "QCalendarPermission::AccessMode", "user": false, "write": "setAccessMode"}], "qualifiedClassName": "QQmlCalendarPermission", "signals": [{"access": "public", "index": 0, "name": "statusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "accessModeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "ContactsPermission"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Extended", "value": "QContactsPermission"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}], "className": "QQmlContactsPermission", "lineNumber": 85, "methods": [{"access": "public", "index": 2, "name": "request", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Qt::PermissionStatus", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "accessMode", "notify": "accessModeChanged", "read": "accessMode", "required": false, "scriptable": true, "stored": true, "type": "QContactsPermission::AccessMode", "user": false, "write": "setAccessMode"}], "qualifiedClassName": "QQmlContactsPermission", "signals": [{"access": "public", "index": 0, "name": "statusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "accessModeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "BluetoothPermission"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "QML.Extended", "value": "QBluetoothPermission"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}], "className": "QQmlBluetoothPermission", "lineNumber": 93, "methods": [{"access": "public", "index": 2, "name": "request", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Qt::PermissionStatus", "user": false}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "communicationModes", "notify": "communicationModesChanged", "read": "communicationModes", "required": false, "scriptable": true, "stored": true, "type": "QBluetoothPermission::CommunicationModes", "user": false, "write": "setCommunicationModes"}], "qualifiedClassName": "QQmlBluetoothPermission", "signals": [{"access": "public", "index": 0, "name": "statusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "communicationModesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "CameraPermission"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QQmlCameraPermission", "lineNumber": 101, "methods": [{"access": "public", "index": 1, "name": "request", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Qt::PermissionStatus", "user": false}], "qualifiedClassName": "QQmlCameraPermission", "signals": [{"access": "public", "index": 0, "name": "statusChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "MicrophonePermission"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QQmlMicrophonePermission", "lineNumber": 107, "methods": [{"access": "public", "index": 1, "name": "request", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Qt::PermissionStatus", "user": false}], "qualifiedClassName": "QQmlMicrophonePermission", "signals": [{"access": "public", "index": 0, "name": "statusChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlpermissions_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Settings"}, {"name": "QML.AddedInVersion", "value": "1541"}], "className": "QQmlSettings", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 29, "methods": [{"access": "public", "arguments": [{"name": "key", "type": "QString"}, {"name": "defaultValue", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 3, "isConst": true, "name": "value", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "key", "type": "QString"}], "index": 4, "isCloned": true, "isConst": true, "name": "value", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "key", "type": "QString"}, {"name": "value", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 5, "name": "setValue", "returnType": "void"}, {"access": "public", "index": 6, "name": "sync", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "category", "notify": "categoryChanged", "read": "category", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setCategory"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "location", "notify": "locationChanged", "read": "location", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setLocation"}], "qualifiedClassName": "QQmlSettings", "signals": [{"access": "public", "arguments": [{"name": "arg", "type": "QString"}], "index": 0, "name": "categoryChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "arg", "type": "QUrl"}], "index": 1, "name": "locationChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 2, "name": "_q_propertyChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qqmlsettings_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Singleton", "value": "true"}, {"name": "QML.Element", "value": "StandardPaths"}, {"name": "QML.AddedInVersion", "value": "1538"}, {"name": "QML.Extended", "value": "QS<PERSON>dard<PERSON><PERSON><PERSON>"}, {"name": "QML.ExtensionIsNamespace", "value": "true"}], "className": "QQmlStandardPaths", "lineNumber": 29, "methods": [{"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}], "index": 0, "isConst": true, "name": "displayName", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "executableName", "type": "QString"}, {"name": "paths", "type": "QStringList"}], "index": 1, "isConst": true, "name": "findExecutable", "returnType": "QUrl"}, {"access": "public", "arguments": [{"name": "executableName", "type": "QString"}], "index": 2, "isCloned": true, "isConst": true, "name": "findExecutable", "returnType": "QUrl"}, {"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}, {"name": "fileName", "type": "QString"}, {"name": "options", "type": "QStandardPaths::LocateOptions"}], "index": 3, "isConst": true, "name": "locate", "returnType": "QUrl"}, {"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}, {"name": "fileName", "type": "QString"}], "index": 4, "isCloned": true, "isConst": true, "name": "locate", "returnType": "QUrl"}, {"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}, {"name": "fileName", "type": "QString"}, {"name": "options", "type": "QStandardPaths::LocateOptions"}], "index": 5, "isConst": true, "name": "locateAll", "returnType": "QList<QUrl>"}, {"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}, {"name": "fileName", "type": "QString"}], "index": 6, "isCloned": true, "isConst": true, "name": "locateAll", "returnType": "QList<QUrl>"}, {"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}], "index": 7, "isConst": true, "name": "standardLocations", "returnType": "QList<QUrl>"}, {"access": "public", "arguments": [{"name": "type", "type": "QStandardPaths::StandardLocation"}], "index": 8, "isConst": true, "name": "writableLocation", "returnType": "QUrl"}], "object": true, "qualifiedClassName": "QQmlStandardPaths", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlstandardpaths_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Singleton", "value": "true"}, {"name": "QML.Element", "value": "SystemInformation"}, {"name": "QML.AddedInVersion", "value": "1540"}], "className": "QQmlSystemInformation", "enums": [{"isClass": true, "isFlag": false, "name": "<PERSON><PERSON>", "values": ["Big", "<PERSON>"]}], "lineNumber": 23, "object": true, "properties": [{"constant": true, "designable": true, "final": true, "index": 0, "name": "wordSize", "read": "wordSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": true, "index": 1, "name": "byteOrder", "read": "byteOrder", "required": false, "scriptable": true, "stored": true, "type": "QQmlSystemInformation::<PERSON>ian", "user": false}, {"constant": true, "designable": true, "final": true, "index": 2, "name": "buildCpuArchitecture", "read": "buildCpuArchitecture", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 3, "name": "currentCpuArchitecture", "read": "currentCpuArchitecture", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 4, "name": "buildAbi", "read": "buildAbi", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 5, "name": "kernelType", "read": "kernelType", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 6, "name": "kernelVersion", "read": "kernelVersion", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 7, "name": "productType", "read": "productType", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 8, "name": "productVersion", "read": "productVersion", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 9, "name": "prettyProductName", "read": "prettyProductName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 10, "name": "machineHostName", "read": "machineHostName", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": true, "index": 11, "name": "machineUniqueId", "read": "machineUniqueId", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false}, {"constant": true, "designable": true, "final": true, "index": 12, "name": "bootUniqueId", "read": "bootUniqueId", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false}], "qualifiedClassName": "QQmlSystemInformation", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qqmlsysteminformation_p.h", "outputRevision": 69}]