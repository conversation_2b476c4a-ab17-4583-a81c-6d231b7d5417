# ZWOW - 智能客服和获客系统

ZWOW是一个基于PySide6的现代化智能客服和获客系统，集成了WhatsApp聊天、知识管理、群发获客等多种功能。

## 功能特性

### 🤖 智能聊天客服
- WhatsApp集成支持
- 智能消息处理和回复
- 上下文管理和对话历史
- 多平台消息聚合

### 📚 知识管理系统
- 文档处理和向量化
- 知识图谱构建
- 多种搜索方式（向量搜索、BM25、混合搜索）
- RAG（检索增强生成）支持

### 📢 群发获客功能
- 批量消息发送
- 客户管理和分类
- 黑名单管理
- 发送统计和报告

### ⚙️ 系统管理
- 配置管理和设置
- 服务状态监控
- 任务调度和轮询
- 用户认证和权限管理

## 快速开始

### 1. 运行测试
```bash
python run_tests.py
```

### 2. 启动应用程序
```bash
python src/pack/frontend/main.py
```

## 项目结构

```
zwow/
├── src/pack/
│   ├── backend/                 # 后端模块
│   │   ├── chat/               # 聊天功能
│   │   ├── knowledge/          # 知识管理
│   │   ├── search/             # 搜索功能
│   │   ├── bulk_acquisition/   # 群发获客
│   │   └── services/           # 服务管理
│   ├── frontend/               # 前端界面
│   ├── config/                 # 配置管理
│   └── common_utils/           # 通用工具
├── tests/                      # 测试文件
├── data/                       # 数据目录
└── logs/                       # 日志目录
```

## 使用指南

1. **登录**: 输入用户名和密码进行登录
2. **聊天客服**: 管理WhatsApp对话和智能回复
3. **知识管理**: 搜索知识库和管理文档
4. **群发获客**: 批量发送消息和管理客户
5. **系统设置**: 配置API密钥和系统参数

详细使用说明请参考应用程序内的帮助文档。