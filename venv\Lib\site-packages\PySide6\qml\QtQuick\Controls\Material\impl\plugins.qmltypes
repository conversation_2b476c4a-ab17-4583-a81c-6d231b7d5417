import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickmaterialbusyindicator_p.h"
        name: "QQuickMaterialBusyIndicator"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Controls.Material.impl/BusyIndicatorImpl 2.0",
            "QtQuick.Controls.Material.impl/BusyIndicatorImpl 2.1",
            "QtQuick.Controls.Material.impl/BusyIndicatorImpl 2.4",
            "QtQuick.Controls.Material.impl/BusyIndicatorImpl 2.7",
            "QtQuick.Controls.Material.impl/BusyIndicatorImpl 2.11",
            "QtQuick.Controls.Material.impl/BusyIndicatorImpl 6.0",
            "QtQuick.Controls.Material.impl/BusyIndicatorImpl 6.3",
            "QtQuick.Controls.Material.impl/BusyIndicatorImpl 6.7"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539, 1543]
        Property { name: "color"; type: "QColor"; read: "color"; write: "setColor"; index: 0; isFinal: true }
        Property {
            name: "running"
            type: "bool"
            read: "isRunning"
            write: "setRunning"
            index: 1
            isFinal: true
        }
    }
    Component {
        file: "private/qquickmaterialplaceholdertext_p.h"
        name: "QQuickMaterialPlaceholderText"
        accessSemantics: "reference"
        prototype: "QQuickPlaceholderText"
        exports: [
            "QtQuick.Controls.Material.impl/FloatingPlaceholderText 6.5",
            "QtQuick.Controls.Material.impl/FloatingPlaceholderText 6.7"
        ]
        exportMetaObjectRevisions: [1541, 1543]
        Property {
            name: "filled"
            type: "bool"
            read: "isFilled"
            write: "setFilled"
            notify: "filledChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "controlHasActiveFocus"
            type: "bool"
            read: "controlHasActiveFocus"
            write: "setControlHasActiveFocus"
            notify: "controlHasActiveFocusChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "controlHasText"
            type: "bool"
            read: "controlHasText"
            write: "setControlHasText"
            notify: "controlHasTextChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "largestHeight"
            type: "int"
            read: "largestHeight"
            notify: "largestHeightChanged"
            index: 3
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "verticalPadding"
            type: "double"
            read: "verticalPadding"
            write: "setVerticalPadding"
            notify: "verticalPaddingChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "controlImplicitBackgroundHeight"
            type: "double"
            read: "controlImplicitBackgroundHeight"
            write: "setControlImplicitBackgroundHeight"
            notify: "controlImplicitBackgroundHeightChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "controlHeight"
            type: "double"
            read: "controlHeight"
            write: "setControlHeight"
            index: 6
            isFinal: true
        }
        Property { name: "leftPadding"; type: "int"; write: "setLeftPadding"; index: 7; isFinal: true }
        Property {
            name: "floatingLeftPadding"
            type: "int"
            write: "setFloatingLeftPadding"
            index: 8
            isFinal: true
        }
        Signal { name: "filledChanged" }
        Signal { name: "largestHeightChanged" }
        Signal { name: "controlHasActiveFocusChanged" }
        Signal { name: "controlHasTextChanged" }
        Signal { name: "controlImplicitBackgroundHeightChanged" }
        Signal { name: "verticalPaddingChanged" }
        Method { name: "adjustTransformOrigin" }
    }
    Component {
        file: "private/qquickmaterialprogressbar_p.h"
        name: "QQuickMaterialProgressBar"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Controls.Material.impl/ProgressBarImpl 2.0",
            "QtQuick.Controls.Material.impl/ProgressBarImpl 2.1",
            "QtQuick.Controls.Material.impl/ProgressBarImpl 2.4",
            "QtQuick.Controls.Material.impl/ProgressBarImpl 2.7",
            "QtQuick.Controls.Material.impl/ProgressBarImpl 2.11",
            "QtQuick.Controls.Material.impl/ProgressBarImpl 6.0",
            "QtQuick.Controls.Material.impl/ProgressBarImpl 6.3",
            "QtQuick.Controls.Material.impl/ProgressBarImpl 6.7"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539, 1543]
        Property { name: "color"; type: "QColor"; read: "color"; write: "setColor"; index: 0; isFinal: true }
        Property {
            name: "progress"
            type: "double"
            read: "progress"
            write: "setProgress"
            index: 1
            isFinal: true
        }
        Property {
            name: "indeterminate"
            type: "bool"
            read: "isIndeterminate"
            write: "setIndeterminate"
            index: 2
            isFinal: true
        }
    }
    Component {
        file: "private/qquickmaterialripple_p.h"
        name: "QQuickMaterialRipple"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick.Controls.Material.impl/Ripple 2.0",
            "QtQuick.Controls.Material.impl/Ripple 2.1",
            "QtQuick.Controls.Material.impl/Ripple 2.4",
            "QtQuick.Controls.Material.impl/Ripple 2.7",
            "QtQuick.Controls.Material.impl/Ripple 2.11",
            "QtQuick.Controls.Material.impl/Ripple 6.0",
            "QtQuick.Controls.Material.impl/Ripple 6.3",
            "QtQuick.Controls.Material.impl/Ripple 6.7"
        ]
        exportMetaObjectRevisions: [512, 513, 516, 519, 523, 1536, 1539, 1543]
        Enum {
            name: "Trigger"
            values: ["Press", "Release"]
        }
        Property { name: "color"; type: "QColor"; read: "color"; write: "setColor"; index: 0; isFinal: true }
        Property {
            name: "clipRadius"
            type: "double"
            read: "clipRadius"
            write: "setClipRadius"
            index: 1
            isFinal: true
        }
        Property {
            name: "pressed"
            type: "bool"
            read: "isPressed"
            write: "setPressed"
            index: 2
            isFinal: true
        }
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            write: "setActive"
            index: 3
            isFinal: true
        }
        Property {
            name: "anchor"
            type: "QQuickItem"
            isPointer: true
            read: "anchor"
            write: "setAnchor"
            index: 4
            isFinal: true
        }
        Property {
            name: "trigger"
            type: "Trigger"
            read: "trigger"
            write: "setTrigger"
            index: 5
            isFinal: true
        }
    }
    Component {
        file: "private/qquickmaterialtextcontainer_p.h"
        name: "QQuickMaterialTextContainer"
        accessSemantics: "reference"
        prototype: "QQuickPaintedItem"
        exports: [
            "QtQuick.Controls.Material.impl/MaterialTextContainer 6.5",
            "QtQuick.Controls.Material.impl/MaterialTextContainer 6.7"
        ]
        exportMetaObjectRevisions: [1541, 1543]
        Enum {
            name: "PlaceHolderHAlignment"
            values: [
                "AlignLeft",
                "AlignRight",
                "AlignHCenter",
                "AlignJustify"
            ]
        }
        Property {
            name: "filled"
            type: "bool"
            read: "isFilled"
            write: "setFilled"
            index: 0
            isFinal: true
        }
        Property {
            name: "controlHasActiveFocus"
            type: "bool"
            read: "controlHasActiveFocus"
            write: "setControlHasActiveFocus"
            notify: "controlHasActiveFocusChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "fillColor"
            type: "QColor"
            read: "fillColor"
            write: "setFillColor"
            index: 2
            isFinal: true
        }
        Property {
            name: "outlineColor"
            type: "QColor"
            read: "outlineColor"
            write: "setOutlineColor"
            index: 3
            isFinal: true
        }
        Property {
            name: "focusedOutlineColor"
            type: "QColor"
            read: "focusedOutlineColor"
            write: "setFocusedOutlineColor"
            index: 4
            isFinal: true
        }
        Property {
            name: "focusAnimationProgress"
            type: "double"
            read: "focusAnimationProgress"
            write: "setFocusAnimationProgress"
            index: 5
            isFinal: true
        }
        Property {
            name: "placeholderTextWidth"
            type: "double"
            read: "placeholderTextWidth"
            write: "setPlaceholderTextWidth"
            index: 6
            isFinal: true
        }
        Property {
            name: "placeholderTextHAlign"
            type: "PlaceHolderHAlignment"
            read: "placeholderTextHAlign"
            write: "setPlaceholderTextHAlign"
            index: 7
            isFinal: true
        }
        Property {
            name: "controlHasText"
            type: "bool"
            read: "controlHasText"
            write: "setControlHasText"
            notify: "controlHasTextChanged"
            index: 8
            isFinal: true
        }
        Property {
            name: "placeholderHasText"
            type: "bool"
            read: "placeholderHasText"
            write: "setPlaceholderHasText"
            notify: "placeholderHasTextChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "horizontalPadding"
            type: "int"
            read: "horizontalPadding"
            write: "setHorizontalPadding"
            notify: "horizontalPaddingChanged"
            index: 10
            isFinal: true
        }
        Signal { name: "animateChanged" }
        Signal { name: "controlHasActiveFocusChanged" }
        Signal { name: "controlHasTextChanged" }
        Signal { name: "placeholderHasTextChanged" }
        Signal { name: "horizontalPaddingChanged" }
    }
}
