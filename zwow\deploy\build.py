"""
ZWOW 应用程序构建脚本
使用PyInstaller创建可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# 项目配置
PROJECT_NAME = "ZWOW"
PROJECT_ROOT = Path(__file__).parent.parent
DIST_DIR = PROJECT_ROOT / "dist"
BUILD_DIR = PROJECT_ROOT / "build"
MAIN_SCRIPT = PROJECT_ROOT / "src" / "pack" / "frontend" / "main.py"

def clean_build_dirs():
    """清理构建目录"""
    print("清理构建目录...")
    
    for dir_path in [DIST_DIR, BUILD_DIR]:
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"已删除: {dir_path}")
    
    # 清理__pycache__目录
    for root, dirs, files in os.walk(PROJECT_ROOT):
        for dir_name in dirs:
            if dir_name == "__pycache__":
                pycache_path = Path(root) / dir_name
                shutil.rmtree(pycache_path)
                print(f"已删除: {pycache_path}")

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = f'''
# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 项目路径
project_root = Path(r"{PROJECT_ROOT}")
src_path = project_root / "src"

block_cipher = None

a = Analysis(
    [r"{MAIN_SCRIPT}"],
    pathex=[str(src_path)],
    binaries=[],
    datas=[
        (str(project_root / "src" / "pack" / "frontend" / "gui" / "images"), "gui/images"),
        (str(project_root / "src" / "pack" / "frontend" / "gui" / "themes"), "gui/themes"),
        (str(project_root / "src" / "pack" / "config"), "config"),
        (str(project_root / "data"), "data"),
    ],
    hiddenimports=[
        "PySide6.QtCore",
        "PySide6.QtWidgets", 
        "PySide6.QtGui",
        "PySide6.QtSvg",
        "requests",
        "jieba",
        "sqlite3",
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name="{PROJECT_NAME}",
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(project_root / "src" / "pack" / "frontend" / "gui" / "images" / "icons" / "icon.ico") if (project_root / "src" / "pack" / "frontend" / "gui" / "images" / "icons" / "icon.ico").exists() else None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name="{PROJECT_NAME}",
)
'''
    
    spec_file = PROJECT_ROOT / f"{PROJECT_NAME}.spec"
    with open(spec_file, "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print(f"已创建规格文件: {spec_file}")
    return spec_file

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    # 检查PyInstaller是否安装
    try:
        subprocess.run(["pyinstaller", "--version"], check=True, capture_output=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("PyInstaller未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
    
    # 创建规格文件
    spec_file = create_spec_file()
    
    # 运行PyInstaller
    cmd = [
        "pyinstaller",
        "--clean",
        "--noconfirm",
        str(spec_file)
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=PROJECT_ROOT)
    
    if result.returncode == 0:
        print("✓ 可执行文件构建成功")
        exe_path = DIST_DIR / PROJECT_NAME / f"{PROJECT_NAME}.exe"
        if exe_path.exists():
            print(f"✓ 可执行文件位置: {exe_path}")
            return True
    else:
        print("✗ 可执行文件构建失败")
        return False

def create_installer():
    """创建安装程序"""
    print("创建安装程序...")
    
    # 检查NSIS是否可用
    try:
        subprocess.run(["makensis", "/VERSION"], check=True, capture_output=True)
        nsis_available = True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("NSIS未安装，跳过安装程序创建")
        nsis_available = False
    
    if nsis_available:
        nsi_file = PROJECT_ROOT / "deploy" / "installer.nsi"
        if nsi_file.exists():
            cmd = ["makensis", str(nsi_file)]
            result = subprocess.run(cmd, cwd=PROJECT_ROOT)
            
            if result.returncode == 0:
                print("✓ 安装程序创建成功")
                return True
            else:
                print("✗ 安装程序创建失败")
                return False
    
    return False

def copy_additional_files():
    """复制额外文件"""
    print("复制额外文件...")
    
    dist_app_dir = DIST_DIR / PROJECT_NAME
    if not dist_app_dir.exists():
        print("✗ 分发目录不存在")
        return False
    
    # 复制README和许可证
    files_to_copy = [
        ("README.md", "README.md"),
        ("requirements.txt", "requirements.txt"),
    ]
    
    for src_name, dst_name in files_to_copy:
        src_path = PROJECT_ROOT / src_name
        dst_path = dist_app_dir / dst_name
        
        if src_path.exists():
            shutil.copy2(src_path, dst_path)
            print(f"✓ 已复制: {src_name}")
    
    # 创建启动脚本
    start_script = dist_app_dir / "start.bat"
    with open(start_script, "w", encoding="utf-8") as f:
        f.write(f"""@echo off
cd /d "%~dp0"
"{PROJECT_NAME}.exe"
pause
""")
    print("✓ 已创建启动脚本")
    
    return True

def main():
    """主函数"""
    print(f"开始构建 {PROJECT_NAME} 应用程序")
    print("=" * 50)
    
    try:
        # 清理构建目录
        clean_build_dirs()
        
        # 构建可执行文件
        if not build_executable():
            return False
        
        # 复制额外文件
        copy_additional_files()
        
        # 创建安装程序
        create_installer()
        
        print("=" * 50)
        print("✓ 构建完成！")
        print(f"输出目录: {DIST_DIR}")
        
        return True
        
    except Exception as e:
        print(f"✗ 构建过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
