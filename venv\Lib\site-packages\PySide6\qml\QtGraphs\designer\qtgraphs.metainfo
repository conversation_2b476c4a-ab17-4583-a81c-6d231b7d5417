MetaInfo {
    Type {
        name: "QtGraphs.Bars3D"
        icon: "images/bars3d-icon16.png"

        ItemLibraryEntry {
            name: "Bars3D"
            category: "Qt Graphs"
            libraryIcon: "images/bars3d-icon.png"
            version: "1.0"
            requiredImport: "QtGraphs"

            QmlSource { source: "default/Bars3D.qml" }
        }
    }
    Type {
        name: "QtGraphs.Scatter3D"
        icon: "images/scatter3d-icon16.png"

        ItemLibraryEntry {
            name: "Scatter3D"
            category: "Qt Graphs"
            libraryIcon: "images/scatter3d-icon.png"
            version: "1.0"
            requiredImport: "QtGraphs"

            QmlSource { source: "default/Scatter3D.qml" }
        }
    }
    Type {
        name: "QtGraphs.Surface3D"
        icon: "images/surface3d-icon16.png"

        ItemLibraryEntry {
            name: "Surface3D"
            category: "Qt Graphs"
            libraryIcon: "images/surface3d-icon.png"
            version: "1.0"
            requiredImport: "QtGraphs"

            QmlSource { source: "default/Surface3D.qml" }
        }
    }
}
