import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QConeGeometry"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QGeometry"
        exports: [
            "Qt3D.Extras/ConeGeometry 2.0",
            "Qt3D.Extras/ConeGeometry 2.13",
            "Qt3D.Extras/ConeGeometry 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
        Property {
            name: "hasTopEndcap"
            type: "bool"
            read: "hasTopEndcap"
            write: "setHasTopEndcap"
            notify: "hasTopEndcapChanged"
            index: 0
        }
        Property {
            name: "hasBottomEndcap"
            type: "bool"
            read: "hasBottomEndcap"
            write: "setHasBottomEndcap"
            notify: "hasBottomEndcapChanged"
            index: 1
        }
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 2
        }
        Property {
            name: "slices"
            type: "int"
            read: "slices"
            write: "setSlices"
            notify: "slicesChanged"
            index: 3
        }
        Property {
            name: "topRadius"
            type: "float"
            read: "topRadius"
            write: "setTopRadius"
            notify: "topRadiusChanged"
            index: 4
        }
        Property {
            name: "bottomRadius"
            type: "float"
            read: "bottomRadius"
            write: "setBottomRadius"
            notify: "bottomRadiusChanged"
            index: 5
        }
        Property {
            name: "length"
            type: "float"
            read: "length"
            write: "setLength"
            notify: "lengthChanged"
            index: 6
        }
        Property {
            name: "positionAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "positionAttribute"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "normalAttribute"
            index: 8
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "texCoordAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "texCoordAttribute"
            index: 9
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "indexAttribute"
            index: 10
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "hasTopEndcapChanged"
            Parameter { name: "hasTopEndcap"; type: "bool" }
        }
        Signal {
            name: "hasBottomEndcapChanged"
            Parameter { name: "hasBottomEndcap"; type: "bool" }
        }
        Signal {
            name: "topRadiusChanged"
            Parameter { name: "topRadius"; type: "float" }
        }
        Signal {
            name: "bottomRadiusChanged"
            Parameter { name: "bottomRadius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "lengthChanged"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setHasTopEndcap"
            Parameter { name: "hasTopEndcap"; type: "bool" }
        }
        Method {
            name: "setHasBottomEndcap"
            Parameter { name: "hasBottomEndcap"; type: "bool" }
        }
        Method {
            name: "setTopRadius"
            Parameter { name: "topRadius"; type: "float" }
        }
        Method {
            name: "setBottomRadius"
            Parameter { name: "bottomRadius"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setLength"
            Parameter { name: "length"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QConeGeometryView"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QGeometryView"
        exports: [
            "Qt3D.Extras/ConeGeometryView 2.16",
            "Qt3D.Extras/ConeGeometryView 6.0"
        ]
        exportMetaObjectRevisions: [528, 1536]
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 0
        }
        Property {
            name: "slices"
            type: "int"
            read: "slices"
            write: "setSlices"
            notify: "slicesChanged"
            index: 1
        }
        Property {
            name: "hasTopEndcap"
            type: "bool"
            read: "hasTopEndcap"
            write: "setHasTopEndcap"
            notify: "hasTopEndcapChanged"
            index: 2
        }
        Property {
            name: "hasBottomEndcap"
            type: "bool"
            read: "hasBottomEndcap"
            write: "setHasBottomEndcap"
            notify: "hasBottomEndcapChanged"
            index: 3
        }
        Property {
            name: "topRadius"
            type: "float"
            read: "topRadius"
            write: "setTopRadius"
            notify: "topRadiusChanged"
            index: 4
        }
        Property {
            name: "bottomRadius"
            type: "float"
            read: "bottomRadius"
            write: "setBottomRadius"
            notify: "bottomRadiusChanged"
            index: 5
        }
        Property {
            name: "length"
            type: "float"
            read: "length"
            write: "setLength"
            notify: "lengthChanged"
            index: 6
        }
        Signal {
            name: "hasTopEndcapChanged"
            Parameter { name: "hasTopEndcap"; type: "bool" }
        }
        Signal {
            name: "hasBottomEndcapChanged"
            Parameter { name: "hasBottomEndcap"; type: "bool" }
        }
        Signal {
            name: "topRadiusChanged"
            Parameter { name: "topRadius"; type: "float" }
        }
        Signal {
            name: "bottomRadiusChanged"
            Parameter { name: "bottomRadius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "lengthChanged"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setHasTopEndcap"
            Parameter { name: "hasTopEndcap"; type: "bool" }
        }
        Method {
            name: "setHasBottomEndcap"
            Parameter { name: "hasBottomEndcap"; type: "bool" }
        }
        Method {
            name: "setTopRadius"
            Parameter { name: "topRadius"; type: "float" }
        }
        Method {
            name: "setBottomRadius"
            Parameter { name: "bottomRadius"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setLength"
            Parameter { name: "length"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QConeMesh"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Extras/ConeMesh 2.0", "Qt3D.Extras/ConeMesh 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 0
        }
        Property {
            name: "slices"
            type: "int"
            read: "slices"
            write: "setSlices"
            notify: "slicesChanged"
            index: 1
        }
        Property {
            name: "hasTopEndcap"
            type: "bool"
            read: "hasTopEndcap"
            write: "setHasTopEndcap"
            notify: "hasTopEndcapChanged"
            index: 2
        }
        Property {
            name: "hasBottomEndcap"
            type: "bool"
            read: "hasBottomEndcap"
            write: "setHasBottomEndcap"
            notify: "hasBottomEndcapChanged"
            index: 3
        }
        Property {
            name: "topRadius"
            type: "float"
            read: "topRadius"
            write: "setTopRadius"
            notify: "topRadiusChanged"
            index: 4
        }
        Property {
            name: "bottomRadius"
            type: "float"
            read: "bottomRadius"
            write: "setBottomRadius"
            notify: "bottomRadiusChanged"
            index: 5
        }
        Property {
            name: "length"
            type: "float"
            read: "length"
            write: "setLength"
            notify: "lengthChanged"
            index: 6
        }
        Signal {
            name: "hasTopEndcapChanged"
            Parameter { name: "hasTopEndcap"; type: "bool" }
        }
        Signal {
            name: "hasBottomEndcapChanged"
            Parameter { name: "hasBottomEndcap"; type: "bool" }
        }
        Signal {
            name: "topRadiusChanged"
            Parameter { name: "topRadius"; type: "float" }
        }
        Signal {
            name: "bottomRadiusChanged"
            Parameter { name: "bottomRadius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "lengthChanged"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setHasTopEndcap"
            Parameter { name: "hasTopEndcap"; type: "bool" }
        }
        Method {
            name: "setHasBottomEndcap"
            Parameter { name: "hasBottomEndcap"; type: "bool" }
        }
        Method {
            name: "setTopRadius"
            Parameter { name: "topRadius"; type: "float" }
        }
        Method {
            name: "setBottomRadius"
            Parameter { name: "bottomRadius"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setLength"
            Parameter { name: "length"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QCuboidGeometry"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QGeometry"
        exports: [
            "Qt3D.Extras/CuboidGeometry 2.0",
            "Qt3D.Extras/CuboidGeometry 2.13",
            "Qt3D.Extras/CuboidGeometry 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
        Property {
            name: "xExtent"
            type: "float"
            read: "xExtent"
            write: "setXExtent"
            notify: "xExtentChanged"
            index: 0
        }
        Property {
            name: "yExtent"
            type: "float"
            read: "yExtent"
            write: "setYExtent"
            notify: "yExtentChanged"
            index: 1
        }
        Property {
            name: "zExtent"
            type: "float"
            read: "zExtent"
            write: "setZExtent"
            notify: "zExtentChanged"
            index: 2
        }
        Property {
            name: "xyMeshResolution"
            type: "QSize"
            read: "xyMeshResolution"
            write: "setXYMeshResolution"
            notify: "xyMeshResolutionChanged"
            index: 3
        }
        Property {
            name: "yzMeshResolution"
            type: "QSize"
            read: "yzMeshResolution"
            write: "setYZMeshResolution"
            notify: "yzMeshResolutionChanged"
            index: 4
        }
        Property {
            name: "xzMeshResolution"
            type: "QSize"
            read: "xzMeshResolution"
            write: "setXZMeshResolution"
            notify: "xzMeshResolutionChanged"
            index: 5
        }
        Property {
            name: "positionAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "positionAttribute"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "normalAttribute"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "texCoordAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "texCoordAttribute"
            index: 8
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "tangentAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "tangentAttribute"
            index: 9
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "indexAttribute"
            index: 10
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "xExtentChanged"
            Parameter { name: "xExtent"; type: "float" }
        }
        Signal {
            name: "yExtentChanged"
            Parameter { name: "yExtent"; type: "float" }
        }
        Signal {
            name: "zExtentChanged"
            Parameter { name: "zExtent"; type: "float" }
        }
        Signal {
            name: "yzMeshResolutionChanged"
            Parameter { name: "yzMeshResolution"; type: "QSize" }
        }
        Signal {
            name: "xzMeshResolutionChanged"
            Parameter { name: "xzMeshResolution"; type: "QSize" }
        }
        Signal {
            name: "xyMeshResolutionChanged"
            Parameter { name: "xyMeshResolution"; type: "QSize" }
        }
        Method {
            name: "setXExtent"
            Parameter { name: "xExtent"; type: "float" }
        }
        Method {
            name: "setYExtent"
            Parameter { name: "yExtent"; type: "float" }
        }
        Method {
            name: "setZExtent"
            Parameter { name: "zExtent"; type: "float" }
        }
        Method {
            name: "setYZMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setXZMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setXYMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QCuboidGeometryView"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QGeometryView"
        exports: [
            "Qt3D.Extras/CuboidGeometryView 2.16",
            "Qt3D.Extras/CuboidGeometryView 6.0"
        ]
        exportMetaObjectRevisions: [528, 1536]
        Property {
            name: "xExtent"
            type: "float"
            read: "xExtent"
            write: "setXExtent"
            notify: "xExtentChanged"
            index: 0
        }
        Property {
            name: "yExtent"
            type: "float"
            read: "yExtent"
            write: "setYExtent"
            notify: "yExtentChanged"
            index: 1
        }
        Property {
            name: "zExtent"
            type: "float"
            read: "zExtent"
            write: "setZExtent"
            notify: "zExtentChanged"
            index: 2
        }
        Property {
            name: "yzMeshResolution"
            type: "QSize"
            read: "yzMeshResolution"
            write: "setYZMeshResolution"
            notify: "yzMeshResolutionChanged"
            index: 3
        }
        Property {
            name: "xzMeshResolution"
            type: "QSize"
            read: "xzMeshResolution"
            write: "setXZMeshResolution"
            notify: "xzMeshResolutionChanged"
            index: 4
        }
        Property {
            name: "xyMeshResolution"
            type: "QSize"
            read: "xyMeshResolution"
            write: "setXYMeshResolution"
            notify: "xyMeshResolutionChanged"
            index: 5
        }
        Signal {
            name: "xExtentChanged"
            Parameter { name: "xExtent"; type: "float" }
        }
        Signal {
            name: "yExtentChanged"
            Parameter { name: "yExtent"; type: "float" }
        }
        Signal {
            name: "zExtentChanged"
            Parameter { name: "zExtent"; type: "float" }
        }
        Signal {
            name: "yzMeshResolutionChanged"
            Parameter { name: "yzMeshResolution"; type: "QSize" }
        }
        Signal {
            name: "xzMeshResolutionChanged"
            Parameter { name: "xzMeshResolution"; type: "QSize" }
        }
        Signal {
            name: "xyMeshResolutionChanged"
            Parameter { name: "xyMeshResolution"; type: "QSize" }
        }
        Method {
            name: "setXExtent"
            Parameter { name: "xExtent"; type: "float" }
        }
        Method {
            name: "setYExtent"
            Parameter { name: "yExtent"; type: "float" }
        }
        Method {
            name: "setZExtent"
            Parameter { name: "zExtent"; type: "float" }
        }
        Method {
            name: "setYZMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setXZMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setXYMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QCuboidMesh"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Extras/CuboidMesh 2.0", "Qt3D.Extras/CuboidMesh 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "xExtent"
            type: "float"
            read: "xExtent"
            write: "setXExtent"
            notify: "xExtentChanged"
            index: 0
        }
        Property {
            name: "yExtent"
            type: "float"
            read: "yExtent"
            write: "setYExtent"
            notify: "yExtentChanged"
            index: 1
        }
        Property {
            name: "zExtent"
            type: "float"
            read: "zExtent"
            write: "setZExtent"
            notify: "zExtentChanged"
            index: 2
        }
        Property {
            name: "yzMeshResolution"
            type: "QSize"
            read: "yzMeshResolution"
            write: "setYZMeshResolution"
            notify: "yzMeshResolutionChanged"
            index: 3
        }
        Property {
            name: "xzMeshResolution"
            type: "QSize"
            read: "xzMeshResolution"
            write: "setXZMeshResolution"
            notify: "xzMeshResolutionChanged"
            index: 4
        }
        Property {
            name: "xyMeshResolution"
            type: "QSize"
            read: "xyMeshResolution"
            write: "setXYMeshResolution"
            notify: "xyMeshResolutionChanged"
            index: 5
        }
        Signal {
            name: "xExtentChanged"
            Parameter { name: "xExtent"; type: "float" }
        }
        Signal {
            name: "yExtentChanged"
            Parameter { name: "yExtent"; type: "float" }
        }
        Signal {
            name: "zExtentChanged"
            Parameter { name: "zExtent"; type: "float" }
        }
        Signal {
            name: "yzMeshResolutionChanged"
            Parameter { name: "yzMeshResolution"; type: "QSize" }
        }
        Signal {
            name: "xzMeshResolutionChanged"
            Parameter { name: "xzMeshResolution"; type: "QSize" }
        }
        Signal {
            name: "xyMeshResolutionChanged"
            Parameter { name: "xyMeshResolution"; type: "QSize" }
        }
        Method {
            name: "setXExtent"
            Parameter { name: "xExtent"; type: "float" }
        }
        Method {
            name: "setYExtent"
            Parameter { name: "yExtent"; type: "float" }
        }
        Method {
            name: "setZExtent"
            Parameter { name: "zExtent"; type: "float" }
        }
        Method {
            name: "setYZMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setXZMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setXYMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QCylinderGeometry"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QGeometry"
        exports: [
            "Qt3D.Extras/CylinderGeometry 2.0",
            "Qt3D.Extras/CylinderGeometry 2.13",
            "Qt3D.Extras/CylinderGeometry 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 0
        }
        Property {
            name: "slices"
            type: "int"
            read: "slices"
            write: "setSlices"
            notify: "slicesChanged"
            index: 1
        }
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 2
        }
        Property {
            name: "length"
            type: "float"
            read: "length"
            write: "setLength"
            notify: "lengthChanged"
            index: 3
        }
        Property {
            name: "positionAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "positionAttribute"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "normalAttribute"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "texCoordAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "texCoordAttribute"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "indexAttribute"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "lengthChanged"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setLength"
            Parameter { name: "length"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QCylinderGeometryView"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QGeometryView"
        exports: [
            "Qt3D.Extras/CylinderGeometryView 2.16",
            "Qt3D.Extras/CylinderGeometryView 6.0"
        ]
        exportMetaObjectRevisions: [528, 1536]
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 0
        }
        Property {
            name: "slices"
            type: "int"
            read: "slices"
            write: "setSlices"
            notify: "slicesChanged"
            index: 1
        }
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 2
        }
        Property {
            name: "length"
            type: "float"
            read: "length"
            write: "setLength"
            notify: "lengthChanged"
            index: 3
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "lengthChanged"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setLength"
            Parameter { name: "length"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QCylinderMesh"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: [
            "Qt3D.Extras/CylinderMesh 2.0",
            "Qt3D.Extras/CylinderMesh 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 0
        }
        Property {
            name: "slices"
            type: "int"
            read: "slices"
            write: "setSlices"
            notify: "slicesChanged"
            index: 1
        }
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 2
        }
        Property {
            name: "length"
            type: "float"
            read: "length"
            write: "setLength"
            notify: "lengthChanged"
            index: 3
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "lengthChanged"
            Parameter { name: "length"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setLength"
            Parameter { name: "length"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QDiffuseMapMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QMaterial"
        exports: [
            "Qt3D.Extras/DiffuseMapMaterial 2.0",
            "Qt3D.Extras/DiffuseMapMaterial 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "ambient"
            type: "QColor"
            read: "ambient"
            write: "setAmbient"
            notify: "ambientChanged"
            index: 0
        }
        Property {
            name: "specular"
            type: "QColor"
            read: "specular"
            write: "setSpecular"
            notify: "specularChanged"
            index: 1
        }
        Property {
            name: "shininess"
            type: "float"
            read: "shininess"
            write: "setShininess"
            notify: "shininessChanged"
            index: 2
        }
        Property {
            name: "diffuse"
            type: "Qt3DRender::QAbstractTexture"
            isPointer: true
            read: "diffuse"
            write: "setDiffuse"
            notify: "diffuseChanged"
            index: 3
        }
        Property {
            name: "textureScale"
            type: "float"
            read: "textureScale"
            write: "setTextureScale"
            notify: "textureScaleChanged"
            index: 4
        }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "QColor" }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Signal {
            name: "textureScaleChanged"
            Parameter { name: "textureScale"; type: "float" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "color"; type: "QColor" }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "QColor" }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setTextureScale"
            Parameter { name: "textureScale"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QDiffuseSpecularMapMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QMaterial"
        exports: [
            "Qt3D.Extras/DiffuseSpecularMapMaterial 2.0",
            "Qt3D.Extras/DiffuseSpecularMapMaterial 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "ambient"
            type: "QColor"
            read: "ambient"
            write: "setAmbient"
            notify: "ambientChanged"
            index: 0
        }
        Property {
            name: "shininess"
            type: "float"
            read: "shininess"
            write: "setShininess"
            notify: "shininessChanged"
            index: 1
        }
        Property {
            name: "specular"
            type: "Qt3DRender::QAbstractTexture"
            isPointer: true
            read: "specular"
            write: "setSpecular"
            notify: "specularChanged"
            index: 2
        }
        Property {
            name: "diffuse"
            type: "Qt3DRender::QAbstractTexture"
            isPointer: true
            read: "diffuse"
            write: "setDiffuse"
            notify: "diffuseChanged"
            index: 3
        }
        Property {
            name: "textureScale"
            type: "float"
            read: "textureScale"
            write: "setTextureScale"
            notify: "textureScaleChanged"
            index: 4
        }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Signal {
            name: "textureScaleChanged"
            Parameter { name: "textureScale"; type: "float" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setTextureScale"
            Parameter { name: "textureScale"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QDiffuseSpecularMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QMaterial"
        exports: [
            "Qt3D.Extras/DiffuseSpecularMaterial 2.10",
            "Qt3D.Extras/DiffuseSpecularMaterial 6.0"
        ]
        exportMetaObjectRevisions: [522, 1536]
        Property {
            name: "ambient"
            type: "QColor"
            read: "ambient"
            write: "setAmbient"
            notify: "ambientChanged"
            index: 0
        }
        Property {
            name: "diffuse"
            type: "QVariant"
            read: "diffuse"
            write: "setDiffuse"
            notify: "diffuseChanged"
            index: 1
        }
        Property {
            name: "specular"
            type: "QVariant"
            read: "specular"
            write: "setSpecular"
            notify: "specularChanged"
            index: 2
        }
        Property {
            name: "shininess"
            type: "float"
            read: "shininess"
            write: "setShininess"
            notify: "shininessChanged"
            index: 3
        }
        Property {
            name: "normal"
            type: "QVariant"
            read: "normal"
            write: "setNormal"
            notify: "normalChanged"
            index: 4
        }
        Property {
            name: "textureScale"
            type: "float"
            read: "textureScale"
            write: "setTextureScale"
            notify: "textureScaleChanged"
            index: 5
        }
        Property {
            name: "alphaBlending"
            type: "bool"
            read: "isAlphaBlendingEnabled"
            write: "setAlphaBlendingEnabled"
            notify: "alphaBlendingEnabledChanged"
            index: 6
        }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "QVariant" }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "QVariant" }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Signal {
            name: "normalChanged"
            Parameter { name: "normal"; type: "QVariant" }
        }
        Signal {
            name: "textureScaleChanged"
            Parameter { name: "textureScale"; type: "float" }
        }
        Signal {
            name: "alphaBlendingEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "QVariant" }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "QVariant" }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setNormal"
            Parameter { name: "normal"; type: "QVariant" }
        }
        Method {
            name: "setTextureScale"
            Parameter { name: "textureScale"; type: "float" }
        }
        Method {
            name: "setAlphaBlendingEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QExtrudedTextGeometry"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QGeometry"
        exports: [
            "Qt3D.Extras/ExtrudedTextGeometry 2.9",
            "Qt3D.Extras/ExtrudedTextGeometry 2.13",
            "Qt3D.Extras/ExtrudedTextGeometry 6.0"
        ]
        exportMetaObjectRevisions: [521, 525, 1536]
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 0
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 1
        }
        Property {
            name: "extrusionLength"
            type: "float"
            read: "extrusionLength"
            write: "setDepth"
            notify: "depthChanged"
            index: 2
        }
        Property {
            name: "positionAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "positionAttribute"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "normalAttribute"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "indexAttribute"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "QString" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "depthChanged"
            Parameter { name: "extrusionLength"; type: "float" }
        }
        Method {
            name: "setText"
            Parameter { name: "text"; type: "QString" }
        }
        Method {
            name: "setFont"
            Parameter { name: "font"; type: "QFont" }
        }
        Method {
            name: "setDepth"
            Parameter { name: "extrusionLength"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QExtrudedTextMesh"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: [
            "Qt3D.Extras/ExtrudedTextMesh 2.9",
            "Qt3D.Extras/ExtrudedTextMesh 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 0
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 1
        }
        Property {
            name: "depth"
            type: "float"
            read: "depth"
            write: "setDepth"
            notify: "depthChanged"
            index: 2
        }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "QString" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "depthChanged"
            Parameter { name: "depth"; type: "float" }
        }
        Method {
            name: "setText"
            Parameter { name: "text"; type: "QString" }
        }
        Method {
            name: "setFont"
            Parameter { name: "font"; type: "QFont" }
        }
        Method {
            name: "setDepth"
            Parameter { name: "depth"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QFirstPersonCameraController"
        accessSemantics: "reference"
        prototype: "Qt3DExtras::QAbstractCameraController"
        exports: [
            "Qt3D.Extras/FirstPersonCameraController 2.0",
            "Qt3D.Extras/FirstPersonCameraController 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QForwardRenderer"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QTechniqueFilter"
        exports: [
            "Qt3D.Extras/ForwardRenderer 2.0",
            "Qt3D.Extras/ForwardRenderer 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "surface"
            type: "QObject"
            isPointer: true
            read: "surface"
            write: "setSurface"
            notify: "surfaceChanged"
            index: 0
        }
        Property {
            name: "window"
            type: "QObject"
            isPointer: true
            read: "surface"
            write: "setSurface"
            notify: "surfaceChanged"
            index: 1
        }
        Property {
            name: "viewportRect"
            type: "QRectF"
            read: "viewportRect"
            write: "setViewportRect"
            notify: "viewportRectChanged"
            index: 2
        }
        Property {
            name: "clearColor"
            type: "QColor"
            read: "clearColor"
            write: "setClearColor"
            notify: "clearColorChanged"
            index: 3
        }
        Property {
            name: "buffersToClear"
            revision: 65294
            type: "Qt3DRender::QClearBuffers::BufferType"
            read: "buffersToClear"
            write: "setBuffersToClear"
            notify: "buffersToClearChanged"
            index: 4
        }
        Property {
            name: "camera"
            type: "Qt3DCore::QEntity"
            isPointer: true
            read: "camera"
            write: "setCamera"
            notify: "cameraChanged"
            index: 5
        }
        Property {
            name: "externalRenderTargetSize"
            type: "QSize"
            read: "externalRenderTargetSize"
            write: "setExternalRenderTargetSize"
            notify: "externalRenderTargetSizeChanged"
            index: 6
        }
        Property {
            name: "frustumCulling"
            type: "bool"
            read: "isFrustumCullingEnabled"
            write: "setFrustumCullingEnabled"
            notify: "frustumCullingEnabledChanged"
            index: 7
        }
        Property {
            name: "gamma"
            revision: 65289
            type: "float"
            read: "gamma"
            write: "setGamma"
            notify: "gammaChanged"
            index: 8
        }
        Property {
            name: "showDebugOverlay"
            revision: 65295
            type: "bool"
            read: "showDebugOverlay"
            write: "setShowDebugOverlay"
            notify: "showDebugOverlayChanged"
            index: 9
        }
        Signal {
            name: "viewportRectChanged"
            Parameter { name: "viewportRect"; type: "QRectF" }
        }
        Signal {
            name: "clearColorChanged"
            Parameter { name: "clearColor"; type: "QColor" }
        }
        Signal {
            name: "buffersToClearChanged"
            Parameter { type: "Qt3DRender::QClearBuffers::BufferType" }
        }
        Signal {
            name: "cameraChanged"
            Parameter { name: "camera"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Signal {
            name: "surfaceChanged"
            Parameter { name: "surface"; type: "QObject"; isPointer: true }
        }
        Signal {
            name: "externalRenderTargetSizeChanged"
            Parameter { name: "size"; type: "QSize" }
        }
        Signal {
            name: "frustumCullingEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "gammaChanged"
            Parameter { name: "gamma"; type: "float" }
        }
        Signal {
            name: "showDebugOverlayChanged"
            Parameter { name: "showDebugOverlay"; type: "bool" }
        }
        Method {
            name: "setViewportRect"
            Parameter { name: "viewportRect"; type: "QRectF" }
        }
        Method {
            name: "setClearColor"
            Parameter { name: "clearColor"; type: "QColor" }
        }
        Method {
            name: "setBuffersToClear"
            Parameter { type: "Qt3DRender::QClearBuffers::BufferType" }
        }
        Method {
            name: "setCamera"
            Parameter { name: "camera"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Method {
            name: "setSurface"
            Parameter { name: "surface"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "setExternalRenderTargetSize"
            Parameter { name: "size"; type: "QSize" }
        }
        Method {
            name: "setFrustumCullingEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setGamma"
            Parameter { name: "gamma"; type: "float" }
        }
        Method {
            name: "setShowDebugOverlay"
            Parameter { name: "showDebugOverlay"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QGoochMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QMaterial"
        exports: [
            "Qt3D.Extras/GoochMaterial 2.0",
            "Qt3D.Extras/GoochMaterial 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "diffuse"
            type: "QColor"
            read: "diffuse"
            write: "setDiffuse"
            notify: "diffuseChanged"
            index: 0
        }
        Property {
            name: "specular"
            type: "QColor"
            read: "specular"
            write: "setSpecular"
            notify: "specularChanged"
            index: 1
        }
        Property {
            name: "cool"
            type: "QColor"
            read: "cool"
            write: "setCool"
            notify: "coolChanged"
            index: 2
        }
        Property {
            name: "warm"
            type: "QColor"
            read: "warm"
            write: "setWarm"
            notify: "warmChanged"
            index: 3
        }
        Property {
            name: "alpha"
            type: "float"
            read: "alpha"
            write: "setAlpha"
            notify: "alphaChanged"
            index: 4
        }
        Property {
            name: "beta"
            type: "float"
            read: "beta"
            write: "setBeta"
            notify: "betaChanged"
            index: 5
        }
        Property {
            name: "shininess"
            type: "float"
            read: "shininess"
            write: "setShininess"
            notify: "shininessChanged"
            index: 6
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "QColor" }
        }
        Signal {
            name: "coolChanged"
            Parameter { name: "cool"; type: "QColor" }
        }
        Signal {
            name: "warmChanged"
            Parameter { name: "warm"; type: "QColor" }
        }
        Signal {
            name: "alphaChanged"
            Parameter { name: "alpha"; type: "float" }
        }
        Signal {
            name: "betaChanged"
            Parameter { name: "beta"; type: "float" }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "QColor" }
        }
        Method {
            name: "setCool"
            Parameter { name: "cool"; type: "QColor" }
        }
        Method {
            name: "setWarm"
            Parameter { name: "warm"; type: "QColor" }
        }
        Method {
            name: "setAlpha"
            Parameter { name: "alpha"; type: "float" }
        }
        Method {
            name: "setBeta"
            Parameter { name: "beta"; type: "float" }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QMetalRoughMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QMaterial"
        exports: [
            "Qt3D.Extras/MetalRoughMaterial 2.9",
            "Qt3D.Extras/MetalRoughMaterial 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "baseColor"
            type: "QVariant"
            read: "baseColor"
            write: "setBaseColor"
            notify: "baseColorChanged"
            index: 0
        }
        Property {
            name: "metalness"
            type: "QVariant"
            read: "metalness"
            write: "setMetalness"
            notify: "metalnessChanged"
            index: 1
        }
        Property {
            name: "roughness"
            type: "QVariant"
            read: "roughness"
            write: "setRoughness"
            notify: "roughnessChanged"
            index: 2
        }
        Property {
            name: "ambientOcclusion"
            revision: 65290
            type: "QVariant"
            read: "ambientOcclusion"
            write: "setAmbientOcclusion"
            notify: "ambientOcclusionChanged"
            index: 3
        }
        Property {
            name: "normal"
            revision: 65290
            type: "QVariant"
            read: "normal"
            write: "setNormal"
            notify: "normalChanged"
            index: 4
        }
        Property {
            name: "textureScale"
            revision: 65290
            type: "float"
            read: "textureScale"
            write: "setTextureScale"
            notify: "textureScaleChanged"
            index: 5
        }
        Signal {
            name: "baseColorChanged"
            Parameter { name: "baseColor"; type: "QVariant" }
        }
        Signal {
            name: "metalnessChanged"
            Parameter { name: "metalness"; type: "QVariant" }
        }
        Signal {
            name: "roughnessChanged"
            Parameter { name: "roughness"; type: "QVariant" }
        }
        Signal {
            name: "ambientOcclusionChanged"
            Parameter { name: "ambientOcclusion"; type: "QVariant" }
        }
        Signal {
            name: "normalChanged"
            Parameter { name: "normal"; type: "QVariant" }
        }
        Signal {
            name: "textureScaleChanged"
            Parameter { name: "textureScale"; type: "float" }
        }
        Method {
            name: "setBaseColor"
            Parameter { name: "baseColor"; type: "QVariant" }
        }
        Method {
            name: "setMetalness"
            Parameter { name: "metalness"; type: "QVariant" }
        }
        Method {
            name: "setRoughness"
            Parameter { name: "roughness"; type: "QVariant" }
        }
        Method {
            name: "setAmbientOcclusion"
            Parameter { name: "ambientOcclusion"; type: "QVariant" }
        }
        Method {
            name: "setNormal"
            Parameter { name: "normal"; type: "QVariant" }
        }
        Method {
            name: "setTextureScale"
            Parameter { name: "textureScale"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QNormalDiffuseMapAlphaMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DExtras::QNormalDiffuseMapMaterial"
        exports: [
            "Qt3D.Extras/NormalDiffuseMapAlphaMaterial 2.0",
            "Qt3D.Extras/NormalDiffuseMapAlphaMaterial 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QNormalDiffuseMapMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QMaterial"
        exports: [
            "Qt3D.Extras/NormalDiffuseMapMaterial 2.0",
            "Qt3D.Extras/NormalDiffuseMapMaterial 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "ambient"
            type: "QColor"
            read: "ambient"
            write: "setAmbient"
            notify: "ambientChanged"
            index: 0
        }
        Property {
            name: "specular"
            type: "QColor"
            read: "specular"
            write: "setSpecular"
            notify: "specularChanged"
            index: 1
        }
        Property {
            name: "diffuse"
            type: "Qt3DRender::QAbstractTexture"
            isPointer: true
            read: "diffuse"
            write: "setDiffuse"
            notify: "diffuseChanged"
            index: 2
        }
        Property {
            name: "normal"
            type: "Qt3DRender::QAbstractTexture"
            isPointer: true
            read: "normal"
            write: "setNormal"
            notify: "normalChanged"
            index: 3
        }
        Property {
            name: "shininess"
            type: "float"
            read: "shininess"
            write: "setShininess"
            notify: "shininessChanged"
            index: 4
        }
        Property {
            name: "textureScale"
            type: "float"
            read: "textureScale"
            write: "setTextureScale"
            notify: "textureScaleChanged"
            index: 5
        }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "normalChanged"
            Parameter { name: "normal"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "QColor" }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Signal {
            name: "textureScaleChanged"
            Parameter { name: "textureScale"; type: "float" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "QColor" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setNormal"
            Parameter { name: "normal"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setTextureScale"
            Parameter { name: "textureScale"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QNormalDiffuseSpecularMapMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QMaterial"
        exports: [
            "Qt3D.Extras/NormalDiffuseSpecularMapMaterial 2.0",
            "Qt3D.Extras/NormalDiffuseSpecularMapMaterial 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "ambient"
            type: "QColor"
            read: "ambient"
            write: "setAmbient"
            notify: "ambientChanged"
            index: 0
        }
        Property {
            name: "diffuse"
            type: "Qt3DRender::QAbstractTexture"
            isPointer: true
            read: "diffuse"
            write: "setDiffuse"
            notify: "diffuseChanged"
            index: 1
        }
        Property {
            name: "normal"
            type: "Qt3DRender::QAbstractTexture"
            isPointer: true
            read: "normal"
            write: "setNormal"
            notify: "normalChanged"
            index: 2
        }
        Property {
            name: "specular"
            type: "Qt3DRender::QAbstractTexture"
            isPointer: true
            read: "specular"
            write: "setSpecular"
            notify: "specularChanged"
            index: 3
        }
        Property {
            name: "shininess"
            type: "float"
            read: "shininess"
            write: "setShininess"
            notify: "shininessChanged"
            index: 4
        }
        Property {
            name: "textureScale"
            type: "float"
            read: "textureScale"
            write: "setTextureScale"
            notify: "textureScaleChanged"
            index: 5
        }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "normalChanged"
            Parameter { name: "normal"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Signal {
            name: "textureScaleChanged"
            Parameter { name: "textureScale"; type: "float" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setNormal"
            Parameter { name: "normal"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setTextureScale"
            Parameter { name: "textureScale"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QOrbitCameraController"
        accessSemantics: "reference"
        prototype: "Qt3DExtras::QAbstractCameraController"
        exports: [
            "Qt3D.Extras/OrbitCameraController 2.0",
            "Qt3D.Extras/OrbitCameraController 6.0",
            "Qt3D.Extras/OrbitCameraController 6.7"
        ]
        exportMetaObjectRevisions: [512, 1536, 1543]
        Property {
            name: "zoomInLimit"
            type: "float"
            read: "zoomInLimit"
            write: "setZoomInLimit"
            notify: "zoomInLimitChanged"
            index: 0
        }
        Property {
            name: "upVector"
            revision: 1543
            type: "QVector3D"
            read: "upVector"
            write: "setUpVector"
            notify: "upVectorChanged"
            index: 1
        }
        Property {
            name: "inverseXTranslate"
            revision: 1543
            type: "bool"
            read: "inverseXTranslate"
            write: "setInverseXTranslate"
            notify: "inverseXTranslateChanged"
            index: 2
        }
        Property {
            name: "inverseYTranslate"
            revision: 1543
            type: "bool"
            read: "inverseYTranslate"
            write: "setInverseYTranslate"
            notify: "inverseYTranslateChanged"
            index: 3
        }
        Property {
            name: "inversePan"
            revision: 1543
            type: "bool"
            read: "inversePan"
            write: "setInversePan"
            notify: "inversePanChanged"
            index: 4
        }
        Property {
            name: "inverseTilt"
            revision: 1543
            type: "bool"
            read: "inverseTilt"
            write: "setInverseTilt"
            notify: "inverseTiltChanged"
            index: 5
        }
        Property {
            name: "zoomTranslateViewCenter"
            revision: 1543
            type: "bool"
            read: "zoomTranslateViewCenter"
            write: "setZoomTranslateViewCenter"
            notify: "zoomTranslateViewCenterChanged"
            index: 6
        }
        Signal { name: "zoomInLimitChanged" }
        Signal {
            name: "upVectorChanged"
            Parameter { name: "upVector"; type: "QVector3D" }
        }
        Signal {
            name: "inverseXTranslateChanged"
            Parameter { name: "isInverse"; type: "bool" }
        }
        Signal {
            name: "inverseYTranslateChanged"
            Parameter { name: "isInverse"; type: "bool" }
        }
        Signal {
            name: "inversePanChanged"
            Parameter { name: "isInverse"; type: "bool" }
        }
        Signal {
            name: "inverseTiltChanged"
            Parameter { name: "isInverse"; type: "bool" }
        }
        Signal {
            name: "zoomTranslateViewCenterChanged"
            Parameter { name: "isTranslate"; type: "bool" }
        }
        Method {
            name: "setZoomInLimit"
            Parameter { name: "zoomInLimit"; type: "float" }
        }
        Method {
            name: "setUpVector"
            Parameter { name: "upVector"; type: "QVector3D" }
        }
        Method {
            name: "setInverseXTranslate"
            Parameter { name: "isInverse"; type: "bool" }
        }
        Method {
            name: "setInverseYTranslate"
            Parameter { name: "isInverse"; type: "bool" }
        }
        Method {
            name: "setInversePan"
            Parameter { name: "isInverse"; type: "bool" }
        }
        Method {
            name: "setInverseTilt"
            Parameter { name: "isInverse"; type: "bool" }
        }
        Method {
            name: "setZoomTranslateViewCenter"
            Parameter { name: "isTranslate"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QPerVertexColorMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QMaterial"
        exports: [
            "Qt3D.Extras/PerVertexColorMaterial 2.0",
            "Qt3D.Extras/PerVertexColorMaterial 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QPhongAlphaMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QMaterial"
        exports: [
            "Qt3D.Extras/PhongAlphaMaterial 2.0",
            "Qt3D.Extras/PhongAlphaMaterial 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "ambient"
            type: "QColor"
            read: "ambient"
            write: "setAmbient"
            notify: "ambientChanged"
            index: 0
        }
        Property {
            name: "diffuse"
            type: "QColor"
            read: "diffuse"
            write: "setDiffuse"
            notify: "diffuseChanged"
            index: 1
        }
        Property {
            name: "specular"
            type: "QColor"
            read: "specular"
            write: "setSpecular"
            notify: "specularChanged"
            index: 2
        }
        Property {
            name: "shininess"
            type: "float"
            read: "shininess"
            write: "setShininess"
            notify: "shininessChanged"
            index: 3
        }
        Property {
            name: "alpha"
            type: "float"
            read: "alpha"
            write: "setAlpha"
            notify: "alphaChanged"
            index: 4
        }
        Property {
            name: "sourceRgbArg"
            type: "Qt3DRender::QBlendEquationArguments::Blending"
            read: "sourceRgbArg"
            write: "setSourceRgbArg"
            notify: "sourceRgbArgChanged"
            index: 5
        }
        Property {
            name: "destinationRgbArg"
            type: "Qt3DRender::QBlendEquationArguments::Blending"
            read: "destinationRgbArg"
            write: "setDestinationRgbArg"
            notify: "destinationRgbArgChanged"
            index: 6
        }
        Property {
            name: "sourceAlphaArg"
            type: "Qt3DRender::QBlendEquationArguments::Blending"
            read: "sourceAlphaArg"
            write: "setSourceAlphaArg"
            notify: "sourceAlphaArgChanged"
            index: 7
        }
        Property {
            name: "destinationAlphaArg"
            type: "Qt3DRender::QBlendEquationArguments::Blending"
            read: "destinationAlphaArg"
            write: "setDestinationAlphaArg"
            notify: "destinationAlphaArgChanged"
            index: 8
        }
        Property {
            name: "blendFunctionArg"
            type: "Qt3DRender::QBlendEquation::BlendFunction"
            read: "blendFunctionArg"
            write: "setBlendFunctionArg"
            notify: "blendFunctionArgChanged"
            index: 9
        }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "QColor" }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Signal {
            name: "alphaChanged"
            Parameter { name: "alpha"; type: "float" }
        }
        Signal {
            name: "sourceRgbArgChanged"
            Parameter { name: "sourceRgbArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        }
        Signal {
            name: "destinationRgbArgChanged"
            Parameter { name: "destinationRgbArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        }
        Signal {
            name: "sourceAlphaArgChanged"
            Parameter { name: "sourceAlphaArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        }
        Signal {
            name: "destinationAlphaArgChanged"
            Parameter {
                name: "destinationAlphaArg"
                type: "Qt3DRender::QBlendEquationArguments::Blending"
            }
        }
        Signal {
            name: "blendFunctionArgChanged"
            Parameter { name: "blendFunctionArg"; type: "Qt3DRender::QBlendEquation::BlendFunction" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "QColor" }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setAlpha"
            Parameter { name: "alpha"; type: "float" }
        }
        Method {
            name: "setSourceRgbArg"
            Parameter { name: "sourceRgbArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        }
        Method {
            name: "setDestinationRgbArg"
            Parameter { name: "destinationRgbArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        }
        Method {
            name: "setSourceAlphaArg"
            Parameter { name: "sourceAlphaArg"; type: "Qt3DRender::QBlendEquationArguments::Blending" }
        }
        Method {
            name: "setDestinationAlphaArg"
            Parameter {
                name: "destinationAlphaArg"
                type: "Qt3DRender::QBlendEquationArguments::Blending"
            }
        }
        Method {
            name: "setBlendFunctionArg"
            Parameter { name: "blendFunctionArg"; type: "Qt3DRender::QBlendEquation::BlendFunction" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QPhongMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QMaterial"
        exports: [
            "Qt3D.Extras/PhongMaterial 2.0",
            "Qt3D.Extras/PhongMaterial 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "ambient"
            type: "QColor"
            read: "ambient"
            write: "setAmbient"
            notify: "ambientChanged"
            index: 0
        }
        Property {
            name: "diffuse"
            type: "QColor"
            read: "diffuse"
            write: "setDiffuse"
            notify: "diffuseChanged"
            index: 1
        }
        Property {
            name: "specular"
            type: "QColor"
            read: "specular"
            write: "setSpecular"
            notify: "specularChanged"
            index: 2
        }
        Property {
            name: "shininess"
            type: "float"
            read: "shininess"
            write: "setShininess"
            notify: "shininessChanged"
            index: 3
        }
        Signal {
            name: "ambientChanged"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Signal {
            name: "diffuseChanged"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Signal {
            name: "specularChanged"
            Parameter { name: "specular"; type: "QColor" }
        }
        Signal {
            name: "shininessChanged"
            Parameter { name: "shininess"; type: "float" }
        }
        Method {
            name: "setAmbient"
            Parameter { name: "ambient"; type: "QColor" }
        }
        Method {
            name: "setDiffuse"
            Parameter { name: "diffuse"; type: "QColor" }
        }
        Method {
            name: "setSpecular"
            Parameter { name: "specular"; type: "QColor" }
        }
        Method {
            name: "setShininess"
            Parameter { name: "shininess"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QPlaneGeometry"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QGeometry"
        exports: [
            "Qt3D.Extras/PlaneGeometry 2.0",
            "Qt3D.Extras/PlaneGeometry 2.13",
            "Qt3D.Extras/PlaneGeometry 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
        Property {
            name: "width"
            type: "float"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 0
        }
        Property {
            name: "height"
            type: "float"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 1
        }
        Property {
            name: "resolution"
            type: "QSize"
            read: "resolution"
            write: "setResolution"
            notify: "resolutionChanged"
            index: 2
        }
        Property {
            name: "mirrored"
            revision: 65289
            type: "bool"
            read: "mirrored"
            write: "setMirrored"
            notify: "mirroredChanged"
            index: 3
        }
        Property {
            name: "positionAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "positionAttribute"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "normalAttribute"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "texCoordAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "texCoordAttribute"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "tangentAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "tangentAttribute"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "indexAttribute"
            index: 8
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "resolutionChanged"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "float" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "height"; type: "float" }
        }
        Signal {
            name: "mirroredChanged"
            Parameter { name: "mirrored"; type: "bool" }
        }
        Method {
            name: "setResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "width"; type: "float" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "height"; type: "float" }
        }
        Method {
            name: "setMirrored"
            Parameter { name: "mirrored"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QPlaneGeometryView"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QGeometryView"
        exports: [
            "Qt3D.Extras/PlaneGeometryView 2.16",
            "Qt3D.Extras/PlaneGeometryView 6.0"
        ]
        exportMetaObjectRevisions: [528, 1536]
        Property {
            name: "width"
            type: "float"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 0
        }
        Property {
            name: "height"
            type: "float"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 1
        }
        Property {
            name: "meshResolution"
            type: "QSize"
            read: "meshResolution"
            write: "setMeshResolution"
            notify: "meshResolutionChanged"
            index: 2
        }
        Property {
            name: "mirrored"
            revision: 65289
            type: "bool"
            read: "mirrored"
            write: "setMirrored"
            notify: "mirroredChanged"
            index: 3
        }
        Signal {
            name: "meshResolutionChanged"
            Parameter { name: "meshResolution"; type: "QSize" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "float" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "height"; type: "float" }
        }
        Signal {
            name: "mirroredChanged"
            Parameter { name: "mirrored"; type: "bool" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "width"; type: "float" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "height"; type: "float" }
        }
        Method {
            name: "setMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setMirrored"
            Parameter { name: "mirrored"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QPlaneMesh"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Extras/PlaneMesh 2.0", "Qt3D.Extras/PlaneMesh 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "width"
            type: "float"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 0
        }
        Property {
            name: "height"
            type: "float"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 1
        }
        Property {
            name: "meshResolution"
            type: "QSize"
            read: "meshResolution"
            write: "setMeshResolution"
            notify: "meshResolutionChanged"
            index: 2
        }
        Property {
            name: "mirrored"
            revision: 65289
            type: "bool"
            read: "mirrored"
            write: "setMirrored"
            notify: "mirroredChanged"
            index: 3
        }
        Signal {
            name: "meshResolutionChanged"
            Parameter { name: "meshResolution"; type: "QSize" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "float" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "height"; type: "float" }
        }
        Signal {
            name: "mirroredChanged"
            Parameter { name: "mirrored"; type: "bool" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "width"; type: "float" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "height"; type: "float" }
        }
        Method {
            name: "setMeshResolution"
            Parameter { name: "resolution"; type: "QSize" }
        }
        Method {
            name: "setMirrored"
            Parameter { name: "mirrored"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QSkyboxEntity"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QEntity"
        exports: [
            "Qt3D.Extras/SkyboxEntity 2.0",
            "Qt3D.Extras/SkyboxEntity 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "baseName"
            type: "QString"
            read: "baseName"
            write: "setBaseName"
            notify: "baseNameChanged"
            index: 0
        }
        Property {
            name: "extension"
            type: "QString"
            read: "extension"
            write: "setExtension"
            notify: "extensionChanged"
            index: 1
        }
        Property {
            name: "gammaCorrect"
            revision: 65289
            type: "bool"
            read: "isGammaCorrectEnabled"
            write: "setGammaCorrectEnabled"
            notify: "gammaCorrectEnabledChanged"
            index: 2
        }
        Signal {
            name: "baseNameChanged"
            Parameter { name: "path"; type: "QString" }
        }
        Signal {
            name: "extensionChanged"
            Parameter { name: "extension"; type: "QString" }
        }
        Signal {
            name: "gammaCorrectEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setBaseName"
            Parameter { name: "path"; type: "QString" }
        }
        Method {
            name: "setExtension"
            Parameter { name: "extension"; type: "QString" }
        }
        Method {
            name: "setGammaCorrectEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QSphereGeometry"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QGeometry"
        exports: [
            "Qt3D.Extras/SphereGeometry 2.0",
            "Qt3D.Extras/SphereGeometry 2.13",
            "Qt3D.Extras/SphereGeometry 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 0
        }
        Property {
            name: "slices"
            type: "int"
            read: "slices"
            write: "setSlices"
            notify: "slicesChanged"
            index: 1
        }
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 2
        }
        Property {
            name: "generateTangents"
            type: "bool"
            read: "generateTangents"
            write: "setGenerateTangents"
            notify: "generateTangentsChanged"
            index: 3
        }
        Property {
            name: "positionAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "positionAttribute"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "normalAttribute"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "texCoordAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "texCoordAttribute"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "tangentAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "tangentAttribute"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "indexAttribute"
            index: 8
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "generateTangentsChanged"
            Parameter { name: "generateTangents"; type: "bool" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setGenerateTangents"
            Parameter { name: "gen"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QSphereGeometryView"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QGeometryView"
        exports: [
            "Qt3D.Extras/SphereGeometryView 2.16",
            "Qt3D.Extras/SphereGeometryView 6.0"
        ]
        exportMetaObjectRevisions: [528, 1536]
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 0
        }
        Property {
            name: "slices"
            type: "int"
            read: "slices"
            write: "setSlices"
            notify: "slicesChanged"
            index: 1
        }
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 2
        }
        Property {
            name: "generateTangents"
            type: "bool"
            read: "generateTangents"
            write: "setGenerateTangents"
            notify: "generateTangentsChanged"
            index: 3
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "generateTangentsChanged"
            Parameter { name: "generateTangents"; type: "bool" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setGenerateTangents"
            Parameter { name: "gen"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QSphereMesh"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Extras/SphereMesh 2.0", "Qt3D.Extras/SphereMesh 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 0
        }
        Property {
            name: "slices"
            type: "int"
            read: "slices"
            write: "setSlices"
            notify: "slicesChanged"
            index: 1
        }
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 2
        }
        Property {
            name: "generateTangents"
            type: "bool"
            read: "generateTangents"
            write: "setGenerateTangents"
            notify: "generateTangentsChanged"
            index: 3
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "generateTangentsChanged"
            Parameter { name: "generateTangents"; type: "bool" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setGenerateTangents"
            Parameter { name: "gen"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QSpriteGrid"
        accessSemantics: "reference"
        prototype: "Qt3DExtras::QAbstractSpriteSheet"
        exports: ["Qt3D.Extras/SpriteGrid 2.10", "Qt3D.Extras/SpriteGrid 6.0"]
        exportMetaObjectRevisions: [522, 1536]
        Property {
            name: "rows"
            type: "int"
            read: "rows"
            write: "setRows"
            notify: "rowsChanged"
            index: 0
        }
        Property {
            name: "columns"
            type: "int"
            read: "columns"
            write: "setColumns"
            notify: "columnsChanged"
            index: 1
        }
        Signal {
            name: "rowsChanged"
            Parameter { name: "rows"; type: "int" }
        }
        Signal {
            name: "columnsChanged"
            Parameter { name: "columns"; type: "int" }
        }
        Method {
            name: "setRows"
            Parameter { name: "rows"; type: "int" }
        }
        Method {
            name: "setColumns"
            Parameter { name: "columns"; type: "int" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QSpriteSheetItem"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Extras/SpriteItem 2.10", "Qt3D.Extras/SpriteItem 6.0"]
        exportMetaObjectRevisions: [522, 1536]
        Property { name: "x"; type: "int"; read: "x"; write: "setX"; notify: "xChanged"; index: 0 }
        Property { name: "y"; type: "int"; read: "y"; write: "setY"; notify: "yChanged"; index: 1 }
        Property {
            name: "width"
            type: "int"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 2
        }
        Property {
            name: "height"
            type: "int"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 3
        }
        Signal {
            name: "xChanged"
            Parameter { name: "x"; type: "int" }
        }
        Signal {
            name: "yChanged"
            Parameter { name: "y"; type: "int" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "int" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "height"; type: "int" }
        }
        Method {
            name: "setX"
            Parameter { name: "x"; type: "int" }
        }
        Method {
            name: "setY"
            Parameter { name: "y"; type: "int" }
        }
        Method {
            name: "setWidth"
            Parameter { name: "width"; type: "int" }
        }
        Method {
            name: "setHeight"
            Parameter { name: "height"; type: "int" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QText2DEntity"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QEntity"
        exports: [
            "Qt3D.Extras/Text2DEntity 2.9",
            "Qt3D.Extras/Text2DEntity 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 0
        }
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 1
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 2
        }
        Property {
            name: "width"
            type: "float"
            read: "width"
            write: "setWidth"
            notify: "widthChanged"
            index: 3
        }
        Property {
            name: "height"
            type: "float"
            read: "height"
            write: "setHeight"
            notify: "heightChanged"
            index: 4
        }
        Property {
            name: "alignment"
            type: "Qt::Alignment"
            read: "alignment"
            write: "setAlignment"
            index: 5
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "QString" }
        }
        Signal {
            name: "widthChanged"
            Parameter { name: "width"; type: "float" }
        }
        Signal {
            name: "heightChanged"
            Parameter { name: "height"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QTextureMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QMaterial"
        exports: [
            "Qt3D.Extras/TextureMaterial 2.0",
            "Qt3D.Extras/TextureMaterial 6.0"
        ]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "texture"
            type: "Qt3DRender::QAbstractTexture"
            isPointer: true
            read: "texture"
            write: "setTexture"
            notify: "textureChanged"
            index: 0
        }
        Property {
            name: "textureOffset"
            type: "QVector2D"
            read: "textureOffset"
            write: "setTextureOffset"
            notify: "textureOffsetChanged"
            index: 1
        }
        Property {
            name: "textureTransform"
            revision: 65290
            type: "QMatrix3x3"
            read: "textureTransform"
            write: "setTextureTransform"
            notify: "textureTransformChanged"
            index: 2
        }
        Property {
            name: "alphaBlending"
            revision: 65291
            type: "bool"
            read: "isAlphaBlendingEnabled"
            write: "setAlphaBlendingEnabled"
            notify: "alphaBlendingEnabledChanged"
            index: 3
        }
        Signal {
            name: "textureChanged"
            Parameter { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "textureOffsetChanged"
            Parameter { name: "textureOffset"; type: "QVector2D" }
        }
        Signal {
            name: "textureTransformChanged"
            Parameter { name: "textureTransform"; type: "QMatrix3x3" }
        }
        Signal {
            name: "alphaBlendingEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setTexture"
            Parameter { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setTextureOffset"
            Parameter { name: "textureOffset"; type: "QVector2D" }
        }
        Method {
            name: "setTextureTransform"
            Parameter { name: "matrix"; type: "QMatrix3x3" }
        }
        Method {
            name: "setAlphaBlendingEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QTorusGeometry"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QGeometry"
        exports: [
            "Qt3D.Extras/TorusGeometry 2.0",
            "Qt3D.Extras/TorusGeometry 2.13",
            "Qt3D.Extras/TorusGeometry 6.0"
        ]
        exportMetaObjectRevisions: [512, 525, 1536]
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 0
        }
        Property {
            name: "slices"
            type: "int"
            read: "slices"
            write: "setSlices"
            notify: "slicesChanged"
            index: 1
        }
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 2
        }
        Property {
            name: "minorRadius"
            type: "float"
            read: "minorRadius"
            write: "setMinorRadius"
            notify: "minorRadiusChanged"
            index: 3
        }
        Property {
            name: "positionAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "positionAttribute"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "normalAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "normalAttribute"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "texCoordAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "texCoordAttribute"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "indexAttribute"
            type: "Qt3DCore::QAttribute"
            isPointer: true
            read: "indexAttribute"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "minorRadiusChanged"
            Parameter { name: "minorRadius"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setMinorRadius"
            Parameter { name: "minorRadius"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QTorusGeometryView"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QGeometryView"
        exports: [
            "Qt3D.Extras/TorusGeometryView 2.16",
            "Qt3D.Extras/TorusGeometryView 6.0"
        ]
        exportMetaObjectRevisions: [528, 1536]
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 0
        }
        Property {
            name: "slices"
            type: "int"
            read: "slices"
            write: "setSlices"
            notify: "slicesChanged"
            index: 1
        }
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 2
        }
        Property {
            name: "minorRadius"
            type: "float"
            read: "minorRadius"
            write: "setMinorRadius"
            notify: "minorRadiusChanged"
            index: 3
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "minorRadiusChanged"
            Parameter { name: "minorRadius"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setMinorRadius"
            Parameter { name: "minorRadius"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquick3dextrasforeign_p.h"
        name: "Qt3DExtras::QTorusMesh"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QGeometryRenderer"
        exports: ["Qt3D.Extras/TorusMesh 2.0", "Qt3D.Extras/TorusMesh 6.0"]
        exportMetaObjectRevisions: [512, 1536]
        Property {
            name: "rings"
            type: "int"
            read: "rings"
            write: "setRings"
            notify: "ringsChanged"
            index: 0
        }
        Property {
            name: "slices"
            type: "int"
            read: "slices"
            write: "setSlices"
            notify: "slicesChanged"
            index: 1
        }
        Property {
            name: "radius"
            type: "float"
            read: "radius"
            write: "setRadius"
            notify: "radiusChanged"
            index: 2
        }
        Property {
            name: "minorRadius"
            type: "float"
            read: "minorRadius"
            write: "setMinorRadius"
            notify: "minorRadiusChanged"
            index: 3
        }
        Signal {
            name: "radiusChanged"
            Parameter { name: "radius"; type: "float" }
        }
        Signal {
            name: "ringsChanged"
            Parameter { name: "rings"; type: "int" }
        }
        Signal {
            name: "slicesChanged"
            Parameter { name: "slices"; type: "int" }
        }
        Signal {
            name: "minorRadiusChanged"
            Parameter { name: "minorRadius"; type: "float" }
        }
        Method {
            name: "setRings"
            Parameter { name: "rings"; type: "int" }
        }
        Method {
            name: "setSlices"
            Parameter { name: "slices"; type: "int" }
        }
        Method {
            name: "setRadius"
            Parameter { name: "radius"; type: "float" }
        }
        Method {
            name: "setMinorRadius"
            Parameter { name: "minorRadius"; type: "float" }
        }
    }
    Component {
        file: "private/quick3dspritesheet_p.h"
        name: "Qt3DExtras::QSpriteSheet"
        accessSemantics: "reference"
        prototype: "Qt3DExtras::QAbstractSpriteSheet"
        extension: "Qt3DExtras::Extras::Quick::Quick3DSpriteSheet"
        exports: [
            "Qt3D.Extras/SpriteSheet 2.10",
            "Qt3D.Extras/SpriteSheet 6.0"
        ]
        exportMetaObjectRevisions: [522, 1536]
        Property {
            name: "sprites"
            type: "QList<QSpriteSheetItem*>"
            read: "sprites"
            write: "setSprites"
            notify: "spritesChanged"
            index: 0
        }
        Signal {
            name: "spritesChanged"
            Parameter { name: "sprites"; type: "QList<QSpriteSheetItem*>" }
        }
        Method {
            name: "setSprites"
            Parameter { name: "sprites"; type: "QList<QSpriteSheetItem*>" }
        }
    }
    Component {
        file: "private/quick3dlevelofdetailloader_p.h"
        name: "Qt3DExtras::Extras::Quick::Quick3DLevelOfDetailLoader"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QEntity"
        exports: [
            "Qt3D.Extras/LevelOfDetailLoader 2.9",
            "Qt3D.Extras/LevelOfDetailLoader 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "sources"
            type: "QVariantList"
            read: "sources"
            write: "setSources"
            notify: "sourcesChanged"
            index: 0
        }
        Property {
            name: "camera"
            type: "Qt3DRender::QCamera"
            isPointer: true
            read: "camera"
            write: "setCamera"
            notify: "cameraChanged"
            index: 1
        }
        Property {
            name: "currentIndex"
            type: "int"
            read: "currentIndex"
            write: "setCurrentIndex"
            notify: "currentIndexChanged"
            index: 2
        }
        Property {
            name: "thresholdType"
            type: "Qt3DRender::QLevelOfDetail::ThresholdType"
            read: "thresholdType"
            write: "setThresholdType"
            notify: "thresholdTypeChanged"
            index: 3
        }
        Property {
            name: "thresholds"
            type: "double"
            isList: true
            read: "thresholds"
            write: "setThresholds"
            notify: "thresholdsChanged"
            index: 4
        }
        Property {
            name: "volumeOverride"
            type: "Qt3DRender::QLevelOfDetailBoundingSphere"
            read: "volumeOverride"
            write: "setVolumeOverride"
            notify: "volumeOverrideChanged"
            index: 5
        }
        Property {
            name: "entity"
            type: "QObject"
            isPointer: true
            read: "entity"
            notify: "entityChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            notify: "sourceChanged"
            index: 7
            isReadonly: true
        }
        Signal { name: "sourcesChanged" }
        Signal { name: "cameraChanged" }
        Signal { name: "currentIndexChanged" }
        Signal { name: "thresholdTypeChanged" }
        Signal { name: "thresholdsChanged" }
        Signal { name: "volumeOverrideChanged" }
        Signal { name: "entityChanged" }
        Signal { name: "sourceChanged" }
        Method {
            name: "createBoundingSphere"
            type: "Qt3DRender::QLevelOfDetailBoundingSphere"
            Parameter { name: "center"; type: "QVector3D" }
            Parameter { name: "radius"; type: "float" }
        }
    }
    Component {
        file: "private/quick3dspritesheet_p.h"
        name: "Qt3DExtras::Extras::Quick::Quick3DSpriteSheet"
        accessSemantics: "reference"
        defaultProperty: "sprites"
        prototype: "QObject"
        Property {
            name: "sprites"
            type: "Qt3DExtras::QSpriteSheetItem"
            isList: true
            read: "sprites"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "qabstractcameracontroller.h"
        name: "Qt3DExtras::QAbstractCameraController"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QEntity"
        Property {
            name: "camera"
            type: "Qt3DRender::QCamera"
            isPointer: true
            read: "camera"
            write: "setCamera"
            notify: "cameraChanged"
            index: 0
        }
        Property {
            name: "linearSpeed"
            type: "float"
            read: "linearSpeed"
            write: "setLinearSpeed"
            notify: "linearSpeedChanged"
            index: 1
        }
        Property {
            name: "lookSpeed"
            type: "float"
            read: "lookSpeed"
            write: "setLookSpeed"
            notify: "lookSpeedChanged"
            index: 2
        }
        Property {
            name: "acceleration"
            type: "float"
            read: "acceleration"
            write: "setAcceleration"
            notify: "accelerationChanged"
            index: 3
        }
        Property {
            name: "deceleration"
            type: "float"
            read: "deceleration"
            write: "setDeceleration"
            notify: "decelerationChanged"
            index: 4
        }
        Signal { name: "cameraChanged" }
        Signal { name: "linearSpeedChanged" }
        Signal { name: "lookSpeedChanged" }
        Signal {
            name: "accelerationChanged"
            Parameter { name: "acceleration"; type: "float" }
        }
        Signal {
            name: "decelerationChanged"
            Parameter { name: "deceleration"; type: "float" }
        }
    }
    Component {
        file: "qabstractspritesheet.h"
        name: "Qt3DExtras::QAbstractSpriteSheet"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        Property {
            name: "texture"
            type: "Qt3DRender::QAbstractTexture"
            isPointer: true
            read: "texture"
            write: "setTexture"
            notify: "textureChanged"
            index: 0
        }
        Property {
            name: "textureTransform"
            type: "QMatrix3x3"
            read: "textureTransform"
            notify: "textureTransformChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "currentIndex"
            type: "int"
            read: "currentIndex"
            write: "setCurrentIndex"
            notify: "currentIndexChanged"
            index: 2
        }
        Signal {
            name: "textureChanged"
            Parameter { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Signal {
            name: "textureTransformChanged"
            Parameter { name: "textureTransform"; type: "QMatrix3x3" }
        }
        Signal {
            name: "currentIndexChanged"
            Parameter { name: "currentIndex"; type: "int" }
        }
        Method {
            name: "setTexture"
            Parameter { name: "texture"; type: "Qt3DRender::QAbstractTexture"; isPointer: true }
        }
        Method {
            name: "setCurrentIndex"
            Parameter { name: "currentIndex"; type: "int" }
        }
    }
    Component {
        file: "qframegraphnode.h"
        name: "Qt3DRender::QFrameGraphNode"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        Method {
            name: "onParentChanged"
            Parameter { type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "qgeometryrenderer.h"
        name: "Qt3DRender::QGeometryRenderer"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QBoundingVolume"
        Enum {
            name: "PrimitiveType"
            values: [
                "Points",
                "Lines",
                "LineLoop",
                "LineStrip",
                "Triangles",
                "TriangleStrip",
                "TriangleFan",
                "LinesAdjacency",
                "TrianglesAdjacency",
                "LineStripAdjacency",
                "TriangleStripAdjacency",
                "Patches"
            ]
        }
        Property {
            name: "instanceCount"
            type: "int"
            read: "instanceCount"
            write: "setInstanceCount"
            notify: "instanceCountChanged"
            index: 0
        }
        Property {
            name: "vertexCount"
            type: "int"
            read: "vertexCount"
            write: "setVertexCount"
            notify: "vertexCountChanged"
            index: 1
        }
        Property {
            name: "indexOffset"
            type: "int"
            read: "indexOffset"
            write: "setIndexOffset"
            notify: "indexOffsetChanged"
            index: 2
        }
        Property {
            name: "firstInstance"
            type: "int"
            read: "firstInstance"
            write: "setFirstInstance"
            notify: "firstInstanceChanged"
            index: 3
        }
        Property {
            name: "firstVertex"
            type: "int"
            read: "firstVertex"
            write: "setFirstVertex"
            notify: "firstVertexChanged"
            index: 4
        }
        Property {
            name: "indexBufferByteOffset"
            type: "int"
            read: "indexBufferByteOffset"
            write: "setIndexBufferByteOffset"
            notify: "indexBufferByteOffsetChanged"
            index: 5
        }
        Property {
            name: "restartIndexValue"
            type: "int"
            read: "restartIndexValue"
            write: "setRestartIndexValue"
            notify: "restartIndexValueChanged"
            index: 6
        }
        Property {
            name: "verticesPerPatch"
            type: "int"
            read: "verticesPerPatch"
            write: "setVerticesPerPatch"
            notify: "verticesPerPatchChanged"
            index: 7
        }
        Property {
            name: "primitiveRestartEnabled"
            type: "bool"
            read: "primitiveRestartEnabled"
            write: "setPrimitiveRestartEnabled"
            notify: "primitiveRestartEnabledChanged"
            index: 8
        }
        Property {
            name: "geometry"
            type: "Qt3DCore::QGeometry"
            isPointer: true
            read: "geometry"
            write: "setGeometry"
            notify: "geometryChanged"
            index: 9
        }
        Property {
            name: "primitiveType"
            type: "PrimitiveType"
            read: "primitiveType"
            write: "setPrimitiveType"
            notify: "primitiveTypeChanged"
            index: 10
        }
        Property {
            name: "sortIndex"
            type: "float"
            read: "sortIndex"
            write: "setSortIndex"
            notify: "sortIndexChanged"
            index: 11
        }
        Signal {
            name: "instanceCountChanged"
            Parameter { name: "instanceCount"; type: "int" }
        }
        Signal {
            name: "vertexCountChanged"
            Parameter { name: "vertexCount"; type: "int" }
        }
        Signal {
            name: "indexOffsetChanged"
            Parameter { name: "indexOffset"; type: "int" }
        }
        Signal {
            name: "firstInstanceChanged"
            Parameter { name: "firstInstance"; type: "int" }
        }
        Signal {
            name: "firstVertexChanged"
            Parameter { name: "firstVertex"; type: "int" }
        }
        Signal {
            name: "indexBufferByteOffsetChanged"
            Parameter { name: "offset"; type: "int" }
        }
        Signal {
            name: "restartIndexValueChanged"
            Parameter { name: "restartIndexValue"; type: "int" }
        }
        Signal {
            name: "verticesPerPatchChanged"
            Parameter { name: "verticesPerPatch"; type: "int" }
        }
        Signal {
            name: "primitiveRestartEnabledChanged"
            Parameter { name: "primitiveRestartEnabled"; type: "bool" }
        }
        Signal {
            name: "geometryChanged"
            Parameter { name: "geometry"; type: "Qt3DCore::QGeometry"; isPointer: true }
        }
        Signal {
            name: "primitiveTypeChanged"
            Parameter { name: "primitiveType"; type: "PrimitiveType" }
        }
        Signal {
            name: "sortIndexChanged"
            Parameter { name: "sortIndex"; type: "float" }
        }
        Method {
            name: "setInstanceCount"
            Parameter { name: "instanceCount"; type: "int" }
        }
        Method {
            name: "setVertexCount"
            Parameter { name: "vertexCount"; type: "int" }
        }
        Method {
            name: "setIndexOffset"
            Parameter { name: "indexOffset"; type: "int" }
        }
        Method {
            name: "setFirstInstance"
            Parameter { name: "firstInstance"; type: "int" }
        }
        Method {
            name: "setFirstVertex"
            Parameter { name: "firstVertex"; type: "int" }
        }
        Method {
            name: "setIndexBufferByteOffset"
            Parameter { name: "offset"; type: "int" }
        }
        Method {
            name: "setRestartIndexValue"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "setVerticesPerPatch"
            Parameter { name: "verticesPerPatch"; type: "int" }
        }
        Method {
            name: "setPrimitiveRestartEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "geometry"; type: "Qt3DCore::QGeometry"; isPointer: true }
        }
        Method {
            name: "setPrimitiveType"
            Parameter { name: "primitiveType"; type: "PrimitiveType" }
        }
        Method {
            name: "setSortIndex"
            Parameter { name: "sortIndex"; type: "float" }
        }
    }
    Component {
        file: "qmaterial.h"
        name: "Qt3DRender::QMaterial"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        Property {
            name: "effect"
            type: "Qt3DRender::QEffect"
            isPointer: true
            read: "effect"
            write: "setEffect"
            notify: "effectChanged"
            index: 0
        }
        Signal {
            name: "effectChanged"
            Parameter { name: "effect"; type: "QEffect"; isPointer: true }
        }
        Method {
            name: "setEffect"
            Parameter { name: "effect"; type: "QEffect"; isPointer: true }
        }
    }
    Component {
        file: "qtechniquefilter.h"
        name: "Qt3DRender::QTechniqueFilter"
        accessSemantics: "reference"
        prototype: "Qt3DRender::QFrameGraphNode"
    }
}
