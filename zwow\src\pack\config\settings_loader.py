"""
配置文件加载器
提供配置文件的加载、保存和管理功能
"""

import os
import yaml
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# 配置日志
logger = logging.getLogger(__name__)

# 全局变量用于存储设置
_SETTINGS: Dict[str, Any] = {}

def get_project_root() -> Path:
    """获取项目根目录"""
    current_file = Path(__file__)
    # 从 zwow/src/pack/config/settings_loader.py 回到 zwow/
    return current_file.parent.parent.parent.parent

def get_resource_path(relative_path: str) -> str:
    """获取资源绝对路径，兼容PyInstaller打包后的路径"""
    try:
        # PyInstaller创建临时文件夹，将路径存储在_MEIPASS中
        import sys
        base_path = sys._MEIPASS
    except Exception:
        # 如果不是打包环境，则使用项目根目录
        base_path = get_project_root()
    
    return os.path.join(base_path, relative_path)

def load_settings(config_path: str = None) -> Dict[str, Any]:
    """
    加载配置文件并返回配置字典
    
    Args:
        config_path: 配置文件路径，默认自动寻找
        
    Returns:
        包含配置信息的字典
    """
    global _SETTINGS
    
    try:
        # 如果没有提供路径，使用默认路径
        if config_path is None:
            # 首先尝试加载YAML配置文件
            config_path = get_resource_path("config/settings.yaml")
            
            # 如果YAML文件不存在，尝试加载JSON配置文件
            if not os.path.exists(config_path):
                config_path = get_resource_path("config/settings.json")
                
            # 如果都不存在，创建默认配置
            if not os.path.exists(config_path):
                logger.warning("配置文件不存在，创建默认配置")
                return create_default_settings()
        
        # 检查文件是否存在
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件 '{config_path}' 不存在")
        
        # 根据文件扩展名选择加载方式
        if config_path.endswith('.yaml') or config_path.endswith('.yml'):
            with open(config_path, 'r', encoding='utf-8') as file:
                settings = yaml.safe_load(file)
        elif config_path.endswith('.json'):
            with open(config_path, 'r', encoding='utf-8') as file:
                settings = json.load(file)
        else:
            raise ValueError(f"不支持的配置文件格式: {config_path}")
        
        _SETTINGS = settings or {}
        logger.info(f"成功加载配置文件: {config_path}")
        return _SETTINGS
        
    except Exception as e:
        logger.error(f"加载配置文件时出错: {str(e)}")
        # 返回默认配置
        _SETTINGS = create_default_settings()
        return _SETTINGS

def create_default_settings() -> Dict[str, Any]:
    """创建默认配置"""
    default_settings = {
        # 应用基本信息
        "app_name": "WOWCKER AGENT",
        "version": "v1.0.0",
        "copyright": "",
        "year": 2024,
        
        # 文件路径配置
        "source_file_path_core": "data/source/core",
        "source_file_path_help": "data/source/help",
        "vector_path_core": "data/vector/core",
        "vector_path_help": "data/vector/help",
        "graph_file_path_core": "data/graph/core",
        "graph_file_path_help": "data/graph/help",
        
        # 数据库配置
        "database": {
            "path": "data/hermes.db",
            "backup_enabled": True,
            "backup_interval": 24  # 小时
        },
        
        # API配置
        "api": {
            "base_url": "http://************:9090/api",
            "timeout": 30,
            "retry_count": 3
        },
        
        # 轮询配置
        "poller": {
            "interval": 30,  # 秒
            "enabled": True
        },
        
        # 知识图谱配置
        "knowledge_graph": {
            "use_multiprocessing": False,
            "entity_extraction": {
                "min_entity_freq": 2,
                "max_entity_length": 10,
                "custom_entities": []
            },
            "relation_extraction": {
                "min_relation_freq": 2,
                "max_distance": 50,
                "custom_relations": ["包含", "属于", "应用于", "等同于"]
            }
        },
        
        # 向量数据库配置
        "vector_db": {
            "chunk_size": 1000,
            "chunk_overlap": 200,
            "embedding_model": "text-embedding-ada-002",
            "dimension": 1536,
            "index_type": "Flat",
            "similarity_threshold": 0.7
        },
        
        # 搜索配置
        "search": {
            "bm25_k1": 1.2,
            "bm25_b": 0.75,
            "max_results": 10,
            "min_score_threshold": 0.1,
            "vector_search": {
                "core_top_k": 5,
                "help_top_k": 5,
                "min_score_threshold": 0.3
            },
            "graph_search": {
                "core_top_k": 5,
                "help_top_k": 5,
                "neighbor_limit": 3
            },
            "hybrid": {
                "vector_weight": 0.6,
                "bm25_weight": 0.4,
                "fusion_method": "weighted_sum"
            },
            "default_type": "hybrid",
            "default_kb_type": "core",
            "default_top_k": 5
        },
        
        # 日志配置
        "logging": {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file_enabled": True,
            "file_path": "logs/app.log"
        }
    }
    
    # 确保配置目录存在并保存默认配置
    try:
        config_dir = get_resource_path("config")
        os.makedirs(config_dir, exist_ok=True)
        
        config_file = os.path.join(config_dir, "settings.yaml")
        save_settings(default_settings, config_file)
        logger.info(f"已创建默认配置文件: {config_file}")
    except Exception as e:
        logger.error(f"保存默认配置文件时出错: {str(e)}")
    
    return default_settings

def get_settings(reload: bool = False) -> Dict[str, Any]:
    """
    获取全局设置，如果尚未加载或需要重新加载则从文件加载
    
    Args:
        reload: 是否强制重新加载设置
        
    Returns:
        包含配置信息的字典
    """
    global _SETTINGS
    if not _SETTINGS or reload:
        _SETTINGS = load_settings()
    return _SETTINGS

def get_setting(key: str, default: Any = None) -> Any:
    """
    获取特定配置项的值
    
    Args:
        key: 配置项键名，支持点号分隔的嵌套键名，如 'database.path'
        default: 默认值
        
    Returns:
        配置项的值
    """
    settings = get_settings()
    
    # 支持嵌套键名
    keys = key.split('.')
    value = settings
    
    try:
        for k in keys:
            value = value[k]
        return value
    except (KeyError, TypeError):
        return default

def update_setting(key: str, value: Any) -> bool:
    """
    更新配置项的值
    
    Args:
        key: 配置项键名，支持点号分隔的嵌套键名
        value: 新值
        
    Returns:
        是否更新成功
    """
    global _SETTINGS
    settings = get_settings()
    
    # 支持嵌套键名
    keys = key.split('.')
    current = settings
    
    try:
        # 导航到目标位置
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        # 设置值
        current[keys[-1]] = value
        _SETTINGS = settings
        return True
    except Exception as e:
        logger.error(f"更新配置项 '{key}' 时出错: {str(e)}")
        return False

def save_settings(settings: Dict[str, Any] = None, config_path: str = None) -> bool:
    """
    保存配置到文件
    
    Args:
        settings: 要保存的配置字典，默认使用当前全局配置
        config_path: 配置文件路径，默认使用默认路径
        
    Returns:
        是否保存成功
    """
    if settings is None:
        settings = get_settings()
    
    if config_path is None:
        config_path = get_resource_path("config/settings.yaml")
    
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        # 根据文件扩展名选择保存方式
        if config_path.endswith('.yaml') or config_path.endswith('.yml'):
            with open(config_path, 'w', encoding='utf-8') as file:
                yaml.dump(settings, file, default_flow_style=False, 
                         allow_unicode=True, indent=2)
        elif config_path.endswith('.json'):
            with open(config_path, 'w', encoding='utf-8') as file:
                json.dump(settings, file, ensure_ascii=False, indent=2)
        else:
            raise ValueError(f"不支持的配置文件格式: {config_path}")
        
        logger.info(f"配置已保存到: {config_path}")
        return True
        
    except Exception as e:
        logger.error(f"保存配置文件时出错: {str(e)}")
        return False

def get_absolute_path(relative_path: str) -> str:
    """
    将相对路径转换为绝对路径，兼容打包环境
    
    Args:
        relative_path: 相对路径
        
    Returns:
        绝对路径
    """
    return get_resource_path(relative_path)

# 初始化时自动加载设置
try:
    _SETTINGS = load_settings()
except Exception as e:
    logger.error(f"初始化配置时出错: {str(e)}")
    _SETTINGS = create_default_settings()

if __name__ == "__main__":
    # 测试函数
    print("=== 配置加载器测试 ===")
    
    settings = get_settings()
    print("已加载配置:")
    for key, value in settings.items():
        print(f"  {key}: {value}")
    
    # 测试获取特定配置项
    print(f"\n数据库路径: {get_setting('database.path', 'default.db')}")
    print(f"API基础URL: {get_setting('api.base_url', 'http://localhost')}")
    
    # 测试更新配置项
    print(f"\n更新配置项...")
    update_setting('test.new_key', 'test_value')
    print(f"新配置项: {get_setting('test.new_key')}")
    
    # 测试重新加载
    settings = get_settings(reload=True)
    print(f"\n重新加载配置完成，配置项数量: {len(settings)}")
