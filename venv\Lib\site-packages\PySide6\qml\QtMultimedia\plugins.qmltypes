import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QAudioDevice"
        accessSemantics: "value"
        exports: ["QtMultimedia/audioDevice 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "Mode"
            values: ["Null", "Input", "Output"]
        }
        Property {
            name: "id"
            type: "QByteArray"
            read: "id"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "description"
            type: "QString"
            read: "description"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "isDefault"
            type: "bool"
            read: "isDefault"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "mode"
            type: "Mode"
            read: "mode"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QAudioDeviceDerived"
        accessSemantics: "none"
        prototype: "QAudioDevice"
        exports: ["QtMultimedia/AudioDevice 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QAudioInput"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtMultimedia/AudioInput 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "device"
            type: "QAudioDevice"
            read: "device"
            write: "setDevice"
            notify: "deviceChanged"
            index: 0
        }
        Property {
            name: "volume"
            type: "float"
            read: "volume"
            write: "setVolume"
            notify: "volumeChanged"
            index: 1
        }
        Property {
            name: "muted"
            type: "bool"
            read: "isMuted"
            write: "setMuted"
            notify: "mutedChanged"
            index: 2
        }
        Signal { name: "deviceChanged" }
        Signal {
            name: "volumeChanged"
            Parameter { name: "volume"; type: "float" }
        }
        Signal {
            name: "mutedChanged"
            Parameter { name: "muted"; type: "bool" }
        }
        Method {
            name: "setDevice"
            Parameter { name: "device"; type: "QAudioDevice" }
        }
        Method {
            name: "setVolume"
            Parameter { name: "volume"; type: "float" }
        }
        Method {
            name: "setMuted"
            Parameter { name: "muted"; type: "bool" }
        }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QAudioOutput"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtMultimedia/AudioOutput 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "device"
            type: "QAudioDevice"
            read: "device"
            write: "setDevice"
            notify: "deviceChanged"
            index: 0
        }
        Property {
            name: "volume"
            type: "float"
            read: "volume"
            write: "setVolume"
            notify: "volumeChanged"
            index: 1
        }
        Property {
            name: "muted"
            type: "bool"
            read: "isMuted"
            write: "setMuted"
            notify: "mutedChanged"
            index: 2
        }
        Signal { name: "deviceChanged" }
        Signal {
            name: "volumeChanged"
            Parameter { name: "volume"; type: "float" }
        }
        Signal {
            name: "mutedChanged"
            Parameter { name: "muted"; type: "bool" }
        }
        Method {
            name: "setDevice"
            Parameter { name: "device"; type: "QAudioDevice" }
        }
        Method {
            name: "setVolume"
            Parameter { name: "volume"; type: "float" }
        }
        Method {
            name: "setMuted"
            Parameter { name: "muted"; type: "bool" }
        }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QCameraDevice"
        accessSemantics: "value"
        exports: ["QtMultimedia/cameraDevice 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "Position"
            values: ["UnspecifiedPosition", "BackFace", "FrontFace"]
        }
        Property {
            name: "id"
            type: "QByteArray"
            read: "id"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "description"
            type: "QString"
            read: "description"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "isDefault"
            type: "bool"
            read: "isDefault"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "position"
            type: "Position"
            read: "position"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "videoFormats"
            type: "QCameraFormat"
            isList: true
            read: "videoFormats"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "correctionAngle"
            type: "QtVideo::Rotation"
            read: "correctionAngle"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QCameraDeviceDerived"
        accessSemantics: "none"
        prototype: "QCameraDevice"
        exports: ["QtMultimedia/CameraDevice 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QCamera"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtMultimedia/Camera 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "Error"
            values: ["NoError", "CameraError"]
        }
        Enum {
            name: "FocusMode"
            values: [
                "FocusModeAuto",
                "FocusModeAutoNear",
                "FocusModeAutoFar",
                "FocusModeHyperfocal",
                "FocusModeInfinity",
                "FocusModeManual"
            ]
        }
        Enum {
            name: "FlashMode"
            values: ["FlashOff", "FlashOn", "FlashAuto"]
        }
        Enum {
            name: "TorchMode"
            values: ["TorchOff", "TorchOn", "TorchAuto"]
        }
        Enum {
            name: "ExposureMode"
            values: [
                "ExposureAuto",
                "ExposureManual",
                "ExposurePortrait",
                "ExposureNight",
                "ExposureSports",
                "ExposureSnow",
                "ExposureBeach",
                "ExposureAction",
                "ExposureLandscape",
                "ExposureNightPortrait",
                "ExposureTheatre",
                "ExposureSunset",
                "ExposureSteadyPhoto",
                "ExposureFireworks",
                "ExposureParty",
                "ExposureCandlelight",
                "ExposureBarcode"
            ]
        }
        Enum {
            name: "WhiteBalanceMode"
            values: [
                "WhiteBalanceAuto",
                "WhiteBalanceManual",
                "WhiteBalanceSunlight",
                "WhiteBalanceCloudy",
                "WhiteBalanceShade",
                "WhiteBalanceTungsten",
                "WhiteBalanceFluorescent",
                "WhiteBalanceFlash",
                "WhiteBalanceSunset"
            ]
        }
        Enum {
            name: "Features"
            alias: "Feature"
            isFlag: true
            isScoped: true
            values: [
                "ColorTemperature",
                "ExposureCompensation",
                "IsoSensitivity",
                "ManualExposureTime",
                "CustomFocusPoint",
                "FocusDistance"
            ]
        }
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            write: "setActive"
            notify: "activeChanged"
            index: 0
        }
        Property {
            name: "cameraDevice"
            type: "QCameraDevice"
            read: "cameraDevice"
            write: "setCameraDevice"
            notify: "cameraDeviceChanged"
            index: 1
        }
        Property {
            name: "error"
            type: "Error"
            read: "error"
            notify: "errorChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "errorString"
            type: "QString"
            read: "errorString"
            notify: "errorChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "cameraFormat"
            type: "QCameraFormat"
            read: "cameraFormat"
            write: "setCameraFormat"
            notify: "cameraFormatChanged"
            index: 4
        }
        Property {
            name: "focusMode"
            type: "FocusMode"
            read: "focusMode"
            write: "setFocusMode"
            notify: "focusModeChanged"
            index: 5
        }
        Property {
            name: "focusPoint"
            type: "QPointF"
            read: "focusPoint"
            notify: "focusPointChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "customFocusPoint"
            type: "QPointF"
            read: "customFocusPoint"
            write: "setCustomFocusPoint"
            notify: "customFocusPointChanged"
            index: 7
        }
        Property {
            name: "focusDistance"
            type: "float"
            read: "focusDistance"
            write: "setFocusDistance"
            notify: "focusDistanceChanged"
            index: 8
        }
        Property {
            name: "minimumZoomFactor"
            type: "float"
            read: "minimumZoomFactor"
            notify: "minimumZoomFactorChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "maximumZoomFactor"
            type: "float"
            read: "maximumZoomFactor"
            notify: "maximumZoomFactorChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "zoomFactor"
            type: "float"
            read: "zoomFactor"
            write: "setZoomFactor"
            notify: "zoomFactorChanged"
            index: 11
        }
        Property {
            name: "exposureTime"
            type: "float"
            read: "exposureTime"
            notify: "exposureTimeChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "manualExposureTime"
            type: "float"
            read: "manualExposureTime"
            write: "setManualExposureTime"
            notify: "manualExposureTimeChanged"
            index: 13
        }
        Property {
            name: "isoSensitivity"
            type: "int"
            read: "isoSensitivity"
            notify: "isoSensitivityChanged"
            index: 14
            isReadonly: true
        }
        Property {
            name: "manualIsoSensitivity"
            type: "int"
            read: "manualIsoSensitivity"
            write: "setManualIsoSensitivity"
            notify: "manualIsoSensitivityChanged"
            index: 15
        }
        Property {
            name: "exposureCompensation"
            type: "float"
            read: "exposureCompensation"
            write: "setExposureCompensation"
            notify: "exposureCompensationChanged"
            index: 16
        }
        Property {
            name: "exposureMode"
            type: "QCamera::ExposureMode"
            read: "exposureMode"
            write: "setExposureMode"
            notify: "exposureModeChanged"
            index: 17
        }
        Property {
            name: "flashReady"
            type: "bool"
            read: "isFlashReady"
            notify: "flashReady"
            index: 18
            isReadonly: true
        }
        Property {
            name: "flashMode"
            type: "QCamera::FlashMode"
            read: "flashMode"
            write: "setFlashMode"
            notify: "flashModeChanged"
            index: 19
        }
        Property {
            name: "torchMode"
            type: "QCamera::TorchMode"
            read: "torchMode"
            write: "setTorchMode"
            notify: "torchModeChanged"
            index: 20
        }
        Property {
            name: "whiteBalanceMode"
            type: "WhiteBalanceMode"
            read: "whiteBalanceMode"
            write: "setWhiteBalanceMode"
            notify: "whiteBalanceModeChanged"
            index: 21
        }
        Property {
            name: "colorTemperature"
            type: "int"
            read: "colorTemperature"
            write: "setColorTemperature"
            notify: "colorTemperatureChanged"
            index: 22
        }
        Property {
            name: "supportedFeatures"
            type: "Features"
            read: "supportedFeatures"
            notify: "supportedFeaturesChanged"
            index: 23
            isReadonly: true
        }
        Signal {
            name: "activeChanged"
            Parameter { type: "bool" }
        }
        Signal { name: "errorChanged" }
        Signal {
            name: "errorOccurred"
            Parameter { name: "error"; type: "QCamera::Error" }
            Parameter { name: "errorString"; type: "QString" }
        }
        Signal { name: "cameraDeviceChanged" }
        Signal { name: "cameraFormatChanged" }
        Signal { name: "supportedFeaturesChanged" }
        Signal { name: "focusModeChanged" }
        Signal {
            name: "zoomFactorChanged"
            Parameter { type: "float" }
        }
        Signal {
            name: "minimumZoomFactorChanged"
            Parameter { type: "float" }
        }
        Signal {
            name: "maximumZoomFactorChanged"
            Parameter { type: "float" }
        }
        Signal {
            name: "focusDistanceChanged"
            Parameter { type: "float" }
        }
        Signal { name: "focusPointChanged" }
        Signal { name: "customFocusPointChanged" }
        Signal {
            name: "flashReady"
            Parameter { type: "bool" }
        }
        Signal { name: "flashModeChanged" }
        Signal { name: "torchModeChanged" }
        Signal {
            name: "exposureTimeChanged"
            Parameter { name: "speed"; type: "float" }
        }
        Signal {
            name: "manualExposureTimeChanged"
            Parameter { name: "speed"; type: "float" }
        }
        Signal {
            name: "isoSensitivityChanged"
            Parameter { type: "int" }
        }
        Signal {
            name: "manualIsoSensitivityChanged"
            Parameter { type: "int" }
        }
        Signal {
            name: "exposureCompensationChanged"
            Parameter { type: "float" }
        }
        Signal { name: "exposureModeChanged" }
        Signal { name: "whiteBalanceModeChanged"; isMethodConstant: true }
        Signal { name: "colorTemperatureChanged"; isMethodConstant: true }
        Signal { name: "brightnessChanged" }
        Signal { name: "contrastChanged" }
        Signal { name: "saturationChanged" }
        Signal { name: "hueChanged" }
        Method {
            name: "setActive"
            Parameter { name: "active"; type: "bool" }
        }
        Method { name: "start" }
        Method { name: "stop" }
        Method {
            name: "zoomTo"
            Parameter { name: "zoom"; type: "float" }
            Parameter { name: "rate"; type: "float" }
        }
        Method {
            name: "setFlashMode"
            Parameter { name: "mode"; type: "FlashMode" }
        }
        Method {
            name: "setTorchMode"
            Parameter { name: "mode"; type: "TorchMode" }
        }
        Method {
            name: "setExposureMode"
            Parameter { name: "mode"; type: "ExposureMode" }
        }
        Method {
            name: "setExposureCompensation"
            Parameter { name: "ev"; type: "float" }
        }
        Method {
            name: "setManualIsoSensitivity"
            Parameter { name: "iso"; type: "int" }
        }
        Method { name: "setAutoIsoSensitivity" }
        Method {
            name: "setManualExposureTime"
            Parameter { name: "seconds"; type: "float" }
        }
        Method { name: "setAutoExposureTime" }
        Method {
            name: "setWhiteBalanceMode"
            Parameter { name: "mode"; type: "WhiteBalanceMode" }
        }
        Method {
            name: "setColorTemperature"
            Parameter { name: "colorTemperature"; type: "int" }
        }
        Method {
            name: "isFocusModeSupported"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "mode"; type: "FocusMode" }
        }
        Method {
            name: "isFlashModeSupported"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "mode"; type: "FlashMode" }
        }
        Method { name: "isFlashReady"; type: "bool"; isMethodConstant: true }
        Method {
            name: "isTorchModeSupported"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "mode"; type: "TorchMode" }
        }
        Method {
            name: "isExposureModeSupported"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "mode"; type: "ExposureMode" }
        }
        Method {
            name: "isWhiteBalanceModeSupported"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "mode"; type: "WhiteBalanceMode" }
        }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QCameraFormat"
        accessSemantics: "value"
        exports: ["QtMultimedia/cameraFormat 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property {
            name: "resolution"
            type: "QSize"
            read: "resolution"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "pixelFormat"
            type: "QVideoFrameFormat::PixelFormat"
            read: "pixelFormat"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "minFrameRate"
            type: "float"
            read: "minFrameRate"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maxFrameRate"
            type: "float"
            read: "maxFrameRate"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QCapturableWindow"
        accessSemantics: "value"
        exports: ["QtMultimedia/capturableWindow 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property {
            name: "description"
            type: "QString"
            read: "description"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "isValid"
            type: "bool"
            read: "isValid"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QImageCapture"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "Error"
            values: [
                "NoError",
                "NotReadyError",
                "ResourceError",
                "OutOfSpaceError",
                "NotSupportedFeatureError",
                "FormatError"
            ]
        }
        Enum {
            name: "Quality"
            values: [
                "VeryLowQuality",
                "LowQuality",
                "NormalQuality",
                "HighQuality",
                "VeryHighQuality"
            ]
        }
        Enum {
            name: "FileFormat"
            values: [
                "UnspecifiedFormat",
                "JPEG",
                "PNG",
                "WebP",
                "Tiff",
                "LastFileFormat"
            ]
        }
        Property {
            name: "readyForCapture"
            type: "bool"
            read: "isReadyForCapture"
            notify: "readyForCaptureChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "metaData"
            type: "QMediaMetaData"
            read: "metaData"
            write: "setMetaData"
            notify: "metaDataChanged"
            index: 1
        }
        Property {
            name: "error"
            type: "Error"
            read: "error"
            notify: "errorChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "errorString"
            type: "QString"
            read: "errorString"
            notify: "errorChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "fileFormat"
            type: "FileFormat"
            read: "fileFormat"
            write: "setFileFormat"
            notify: "fileFormatChanged"
            index: 4
        }
        Property {
            name: "quality"
            type: "Quality"
            read: "quality"
            write: "setQuality"
            notify: "qualityChanged"
            index: 5
        }
        Signal { name: "errorChanged" }
        Signal {
            name: "errorOccurred"
            Parameter { name: "id"; type: "int" }
            Parameter { name: "error"; type: "QImageCapture::Error" }
            Parameter { name: "errorString"; type: "QString" }
        }
        Signal {
            name: "readyForCaptureChanged"
            Parameter { name: "ready"; type: "bool" }
        }
        Signal { name: "metaDataChanged" }
        Signal { name: "fileFormatChanged" }
        Signal { name: "qualityChanged" }
        Signal { name: "resolutionChanged" }
        Signal {
            name: "imageExposed"
            Parameter { name: "id"; type: "int" }
        }
        Signal {
            name: "imageCaptured"
            Parameter { name: "id"; type: "int" }
            Parameter { name: "preview"; type: "QImage" }
        }
        Signal {
            name: "imageMetadataAvailable"
            Parameter { name: "id"; type: "int" }
            Parameter { name: "metaData"; type: "QMediaMetaData" }
        }
        Signal {
            name: "imageAvailable"
            Parameter { name: "id"; type: "int" }
            Parameter { name: "frame"; type: "QVideoFrame" }
        }
        Signal {
            name: "imageSaved"
            Parameter { name: "id"; type: "int" }
            Parameter { name: "fileName"; type: "QString" }
        }
        Method {
            name: "captureToFile"
            type: "int"
            Parameter { name: "location"; type: "QString" }
        }
        Method { name: "captureToFile"; type: "int"; isCloned: true }
        Method { name: "capture"; type: "int" }
        Method {
            name: "_q_error"
            Parameter { type: "int" }
            Parameter { type: "int" }
            Parameter { type: "QString" }
        }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QMediaCaptureSession"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtMultimedia/CaptureSession 6.0",
            "QtMultimedia/CaptureSession 6.8"
        ]
        exportMetaObjectRevisions: [1536, 1544]
        Property {
            name: "audioInput"
            type: "QAudioInput"
            isPointer: true
            read: "audioInput"
            write: "setAudioInput"
            notify: "audioInputChanged"
            index: 0
        }
        Property {
            name: "audioBufferInput"
            revision: 1544
            type: "QAudioBufferInput"
            isPointer: true
            read: "audioBufferInput"
            write: "setAudioBufferInput"
            notify: "audioBufferInputChanged"
            index: 1
        }
        Property {
            name: "audioOutput"
            type: "QAudioOutput"
            isPointer: true
            read: "audioOutput"
            write: "setAudioOutput"
            notify: "audioOutputChanged"
            index: 2
        }
        Property {
            name: "camera"
            type: "QCamera"
            isPointer: true
            read: "camera"
            write: "setCamera"
            notify: "cameraChanged"
            index: 3
        }
        Property {
            name: "screenCapture"
            type: "QScreenCapture"
            isPointer: true
            read: "screenCapture"
            write: "setScreenCapture"
            notify: "screenCaptureChanged"
            index: 4
        }
        Property {
            name: "windowCapture"
            type: "QWindowCapture"
            isPointer: true
            read: "windowCapture"
            write: "setWindowCapture"
            notify: "windowCaptureChanged"
            index: 5
        }
        Property {
            name: "videoFrameInput"
            revision: 1544
            type: "QVideoFrameInput"
            isPointer: true
            read: "videoFrameInput"
            write: "setVideoFrameInput"
            notify: "videoFrameInputChanged"
            index: 6
        }
        Property {
            name: "imageCapture"
            type: "QImageCapture"
            isPointer: true
            read: "imageCapture"
            write: "setImageCapture"
            notify: "imageCaptureChanged"
            index: 7
        }
        Property {
            name: "recorder"
            type: "QMediaRecorder"
            isPointer: true
            read: "recorder"
            write: "setRecorder"
            notify: "recorderChanged"
            index: 8
        }
        Property {
            name: "videoOutput"
            type: "QObject"
            isPointer: true
            read: "videoOutput"
            write: "setVideoOutput"
            notify: "videoOutputChanged"
            index: 9
        }
        Signal { name: "audioInputChanged" }
        Signal { name: "audioBufferInputChanged"; revision: 1544 }
        Signal { name: "cameraChanged" }
        Signal { name: "screenCaptureChanged" }
        Signal { name: "windowCaptureChanged" }
        Signal { name: "videoFrameInputChanged"; revision: 1544 }
        Signal { name: "imageCaptureChanged" }
        Signal { name: "recorderChanged" }
        Signal { name: "videoOutputChanged" }
        Signal { name: "audioOutputChanged" }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QMediaDevices"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtMultimedia/MediaDevices 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "audioInputs"
            type: "QAudioDevice"
            isList: true
            read: "audioInputs"
            notify: "audioInputsChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "audioOutputs"
            type: "QAudioDevice"
            isList: true
            read: "audioOutputs"
            notify: "audioOutputsChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "videoInputs"
            type: "QCameraDevice"
            isList: true
            read: "videoInputs"
            notify: "videoInputsChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "defaultAudioInput"
            type: "QAudioDevice"
            read: "defaultAudioInput"
            notify: "audioInputsChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "defaultAudioOutput"
            type: "QAudioDevice"
            read: "defaultAudioOutput"
            notify: "audioOutputsChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "defaultVideoInput"
            type: "QCameraDevice"
            read: "defaultVideoInput"
            notify: "videoInputsChanged"
            index: 5
            isReadonly: true
        }
        Signal { name: "audioInputsChanged" }
        Signal { name: "audioOutputsChanged" }
        Signal { name: "videoInputsChanged" }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QMediaFormat"
        accessSemantics: "value"
        exports: ["QtMultimedia/mediaFormat 6.0"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "FileFormat"
            values: [
                "UnspecifiedFormat",
                "WMV",
                "AVI",
                "Matroska",
                "MPEG4",
                "Ogg",
                "QuickTime",
                "WebM",
                "Mpeg4Audio",
                "AAC",
                "WMA",
                "MP3",
                "FLAC",
                "Wave",
                "LastFileFormat"
            ]
        }
        Enum {
            name: "AudioCodec"
            isScoped: true
            values: [
                "Unspecified",
                "MP3",
                "AAC",
                "AC3",
                "EAC3",
                "FLAC",
                "DolbyTrueHD",
                "Opus",
                "Vorbis",
                "Wave",
                "WMA",
                "ALAC",
                "LastAudioCodec"
            ]
        }
        Enum {
            name: "VideoCodec"
            isScoped: true
            values: [
                "Unspecified",
                "MPEG1",
                "MPEG2",
                "MPEG4",
                "H264",
                "H265",
                "VP8",
                "VP9",
                "AV1",
                "Theora",
                "WMV",
                "MotionJPEG",
                "LastVideoCodec"
            ]
        }
        Enum {
            name: "ConversionMode"
            values: ["Encode", "Decode"]
        }
        Property {
            name: "fileFormat"
            type: "FileFormat"
            read: "fileFormat"
            write: "setFileFormat"
            index: 0
        }
        Property {
            name: "audioCodec"
            type: "AudioCodec"
            read: "audioCodec"
            write: "setAudioCodec"
            index: 1
        }
        Property {
            name: "videoCodec"
            type: "VideoCodec"
            read: "videoCodec"
            write: "setVideoCodec"
            index: 2
        }
        Method {
            name: "isSupported"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "mode"; type: "ConversionMode" }
        }
        Method {
            name: "supportedFileFormats"
            type: "FileFormat"
            isList: true
            Parameter { name: "m"; type: "ConversionMode" }
        }
        Method {
            name: "supportedVideoCodecs"
            type: "VideoCodec"
            isList: true
            Parameter { name: "m"; type: "ConversionMode" }
        }
        Method {
            name: "supportedAudioCodecs"
            type: "AudioCodec"
            isList: true
            Parameter { name: "m"; type: "ConversionMode" }
        }
        Method {
            name: "fileFormatName"
            type: "QString"
            Parameter { name: "fileFormat"; type: "FileFormat" }
        }
        Method {
            name: "audioCodecName"
            type: "QString"
            Parameter { name: "codec"; type: "AudioCodec" }
        }
        Method {
            name: "videoCodecName"
            type: "QString"
            Parameter { name: "codec"; type: "VideoCodec" }
        }
        Method {
            name: "fileFormatDescription"
            type: "QString"
            Parameter { name: "fileFormat"; type: "QMediaFormat::FileFormat" }
        }
        Method {
            name: "audioCodecDescription"
            type: "QString"
            Parameter { name: "codec"; type: "QMediaFormat::AudioCodec" }
        }
        Method {
            name: "videoCodecDescription"
            type: "QString"
            Parameter { name: "codec"; type: "QMediaFormat::VideoCodec" }
        }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QMediaFormatDerived"
        accessSemantics: "none"
        prototype: "QMediaFormat"
        exports: ["QtMultimedia/MediaFormat 6.0"]
        isCreatable: false
        enforcesScopedEnums: true
        exportMetaObjectRevisions: [1536]
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QMediaMetaData"
        accessSemantics: "value"
        exports: ["QtMultimedia/mediaMetaData 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "Key"
            values: [
                "Title",
                "Author",
                "Comment",
                "Description",
                "Genre",
                "Date",
                "Language",
                "Publisher",
                "Copyright",
                "Url",
                "Duration",
                "MediaType",
                "FileFormat",
                "AudioBitRate",
                "AudioCodec",
                "VideoBitRate",
                "VideoCodec",
                "VideoFrameRate",
                "AlbumTitle",
                "AlbumArtist",
                "ContributingArtist",
                "TrackNumber",
                "Composer",
                "LeadPerformer",
                "ThumbnailImage",
                "CoverArtImage",
                "Orientation",
                "Resolution",
                "HasHdrContent"
            ]
        }
        Method {
            name: "value"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "k"; type: "Key" }
        }
        Method {
            name: "insert"
            Parameter { name: "k"; type: "Key" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "remove"
            Parameter { name: "k"; type: "Key" }
        }
        Method { name: "keys"; type: "Key"; isList: true; isMethodConstant: true }
        Method { name: "clear" }
        Method { name: "isEmpty"; type: "bool"; isMethodConstant: true }
        Method {
            name: "stringValue"
            type: "QString"
            isMethodConstant: true
            Parameter { name: "k"; type: "Key" }
        }
        Method {
            name: "metaDataKeyToString"
            type: "QString"
            Parameter { name: "k"; type: "Key" }
        }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QMediaMetaDataDerived"
        accessSemantics: "none"
        prototype: "QMediaMetaData"
        exports: ["QtMultimedia/MediaMetaData 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
    }
    Component {
        file: "qmediaplayer.h"
        name: "QMediaPlayer"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "PlaybackState"
            values: ["StoppedState", "PlayingState", "PausedState"]
        }
        Enum {
            name: "MediaStatus"
            values: [
                "NoMedia",
                "LoadingMedia",
                "LoadedMedia",
                "StalledMedia",
                "BufferingMedia",
                "BufferedMedia",
                "EndOfMedia",
                "InvalidMedia"
            ]
        }
        Enum {
            name: "Error"
            values: [
                "NoError",
                "ResourceError",
                "FormatError",
                "NetworkError",
                "AccessDeniedError"
            ]
        }
        Enum {
            name: "Loops"
            values: ["Infinite", "Once"]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "duration"
            type: "qlonglong"
            read: "duration"
            notify: "durationChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "position"
            type: "qlonglong"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 2
        }
        Property {
            name: "bufferProgress"
            type: "float"
            read: "bufferProgress"
            notify: "bufferProgressChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "hasAudio"
            type: "bool"
            read: "hasAudio"
            notify: "hasAudioChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "hasVideo"
            type: "bool"
            read: "hasVideo"
            notify: "hasVideoChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "seekable"
            type: "bool"
            read: "isSeekable"
            notify: "seekableChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "playing"
            type: "bool"
            read: "isPlaying"
            notify: "playingChanged"
            index: 7
            isReadonly: true
        }
        Property {
            name: "playbackRate"
            type: "double"
            read: "playbackRate"
            write: "setPlaybackRate"
            notify: "playbackRateChanged"
            index: 8
        }
        Property {
            name: "loops"
            type: "int"
            read: "loops"
            write: "setLoops"
            notify: "loopsChanged"
            index: 9
        }
        Property {
            name: "playbackState"
            type: "PlaybackState"
            read: "playbackState"
            notify: "playbackStateChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "mediaStatus"
            type: "MediaStatus"
            read: "mediaStatus"
            notify: "mediaStatusChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "metaData"
            type: "QMediaMetaData"
            read: "metaData"
            notify: "metaDataChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "error"
            type: "Error"
            read: "error"
            notify: "errorChanged"
            index: 13
            isReadonly: true
        }
        Property {
            name: "errorString"
            type: "QString"
            read: "errorString"
            notify: "errorChanged"
            index: 14
            isReadonly: true
        }
        Property {
            name: "videoOutput"
            type: "QObject"
            isPointer: true
            read: "videoOutput"
            write: "setVideoOutput"
            notify: "videoOutputChanged"
            index: 15
        }
        Property {
            name: "audioOutput"
            type: "QAudioOutput"
            isPointer: true
            read: "audioOutput"
            write: "setAudioOutput"
            notify: "audioOutputChanged"
            index: 16
        }
        Property {
            name: "audioBufferOutput"
            type: "QAudioBufferOutput"
            isPointer: true
            read: "audioBufferOutput"
            write: "setAudioBufferOutput"
            notify: "audioBufferOutputChanged"
            index: 17
        }
        Property {
            name: "audioTracks"
            type: "QMediaMetaData"
            isList: true
            read: "audioTracks"
            notify: "tracksChanged"
            index: 18
            isReadonly: true
        }
        Property {
            name: "videoTracks"
            type: "QMediaMetaData"
            isList: true
            read: "videoTracks"
            notify: "tracksChanged"
            index: 19
            isReadonly: true
        }
        Property {
            name: "subtitleTracks"
            type: "QMediaMetaData"
            isList: true
            read: "subtitleTracks"
            notify: "tracksChanged"
            index: 20
            isReadonly: true
        }
        Property {
            name: "activeAudioTrack"
            type: "int"
            read: "activeAudioTrack"
            write: "setActiveAudioTrack"
            notify: "activeTracksChanged"
            index: 21
        }
        Property {
            name: "activeVideoTrack"
            type: "int"
            read: "activeVideoTrack"
            write: "setActiveVideoTrack"
            notify: "activeTracksChanged"
            index: 22
        }
        Property {
            name: "activeSubtitleTrack"
            type: "int"
            read: "activeSubtitleTrack"
            write: "setActiveSubtitleTrack"
            notify: "activeTracksChanged"
            index: 23
        }
        Signal {
            name: "sourceChanged"
            Parameter { name: "media"; type: "QUrl" }
        }
        Signal {
            name: "playbackStateChanged"
            Parameter { name: "newState"; type: "QMediaPlayer::PlaybackState" }
        }
        Signal {
            name: "mediaStatusChanged"
            Parameter { name: "status"; type: "QMediaPlayer::MediaStatus" }
        }
        Signal {
            name: "durationChanged"
            Parameter { name: "duration"; type: "qlonglong" }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "qlonglong" }
        }
        Signal {
            name: "hasAudioChanged"
            Parameter { name: "available"; type: "bool" }
        }
        Signal {
            name: "hasVideoChanged"
            Parameter { name: "videoAvailable"; type: "bool" }
        }
        Signal {
            name: "bufferProgressChanged"
            Parameter { name: "progress"; type: "float" }
        }
        Signal {
            name: "seekableChanged"
            Parameter { name: "seekable"; type: "bool" }
        }
        Signal {
            name: "playingChanged"
            Parameter { name: "playing"; type: "bool" }
        }
        Signal {
            name: "playbackRateChanged"
            Parameter { name: "rate"; type: "double" }
        }
        Signal { name: "loopsChanged" }
        Signal { name: "metaDataChanged" }
        Signal { name: "videoOutputChanged" }
        Signal { name: "audioOutputChanged" }
        Signal { name: "audioBufferOutputChanged"; revision: 1544 }
        Signal { name: "tracksChanged" }
        Signal { name: "activeTracksChanged" }
        Signal { name: "errorChanged" }
        Signal {
            name: "errorOccurred"
            Parameter { name: "error"; type: "QMediaPlayer::Error" }
            Parameter { name: "errorString"; type: "QString" }
        }
        Method { name: "play" }
        Method { name: "pause" }
        Method { name: "stop" }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "qlonglong" }
        }
        Method {
            name: "setPlaybackRate"
            Parameter { name: "rate"; type: "double" }
        }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "setSourceDevice"
            Parameter { name: "device"; type: "QIODevice"; isPointer: true }
            Parameter { name: "sourceUrl"; type: "QUrl" }
        }
        Method {
            name: "setSourceDevice"
            isCloned: true
            Parameter { name: "device"; type: "QIODevice"; isPointer: true }
        }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QMediaRecorder"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtMultimedia/MediaRecorder 6.0",
            "QtMultimedia/MediaRecorder 6.8"
        ]
        exportMetaObjectRevisions: [1536, 1544]
        Enum {
            name: "Quality"
            values: [
                "VeryLowQuality",
                "LowQuality",
                "NormalQuality",
                "HighQuality",
                "VeryHighQuality"
            ]
        }
        Enum {
            name: "EncodingMode"
            values: [
                "ConstantQualityEncoding",
                "ConstantBitRateEncoding",
                "AverageBitRateEncoding",
                "TwoPassEncoding"
            ]
        }
        Enum {
            name: "RecorderState"
            values: ["StoppedState", "RecordingState", "PausedState"]
        }
        Enum {
            name: "Error"
            values: [
                "NoError",
                "ResourceError",
                "FormatError",
                "OutOfSpaceError",
                "LocationNotWritable"
            ]
        }
        Property {
            name: "recorderState"
            type: "QMediaRecorder::RecorderState"
            read: "recorderState"
            notify: "recorderStateChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "duration"
            type: "qlonglong"
            read: "duration"
            notify: "durationChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "outputLocation"
            type: "QUrl"
            read: "outputLocation"
            write: "setOutputLocation"
            index: 2
        }
        Property {
            name: "actualLocation"
            type: "QUrl"
            read: "actualLocation"
            notify: "actualLocationChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "metaData"
            type: "QMediaMetaData"
            read: "metaData"
            write: "setMetaData"
            notify: "metaDataChanged"
            index: 4
        }
        Property {
            name: "error"
            type: "QMediaRecorder::Error"
            read: "error"
            notify: "errorChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "errorString"
            type: "QString"
            read: "errorString"
            notify: "errorChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "mediaFormat"
            type: "QMediaFormat"
            read: "mediaFormat"
            write: "setMediaFormat"
            notify: "mediaFormatChanged"
            index: 7
        }
        Property {
            name: "quality"
            type: "Quality"
            read: "quality"
            write: "setQuality"
            notify: "qualityChanged"
            index: 8
        }
        Property {
            name: "encodingMode"
            type: "QMediaRecorder::EncodingMode"
            read: "encodingMode"
            write: "setEncodingMode"
            notify: "encodingModeChanged"
            index: 9
        }
        Property {
            name: "videoResolution"
            type: "QSize"
            read: "videoResolution"
            write: "setVideoResolution"
            notify: "videoResolutionChanged"
            index: 10
        }
        Property {
            name: "videoFrameRate"
            type: "double"
            read: "videoFrameRate"
            write: "setVideoFrameRate"
            notify: "videoFrameRateChanged"
            index: 11
        }
        Property {
            name: "videoBitRate"
            type: "int"
            read: "videoBitRate"
            write: "setVideoBitRate"
            notify: "videoBitRateChanged"
            index: 12
        }
        Property {
            name: "audioBitRate"
            type: "int"
            read: "audioBitRate"
            write: "setAudioBitRate"
            notify: "audioBitRateChanged"
            index: 13
        }
        Property {
            name: "audioChannelCount"
            type: "int"
            read: "audioChannelCount"
            write: "setAudioChannelCount"
            notify: "audioChannelCountChanged"
            index: 14
        }
        Property {
            name: "audioSampleRate"
            type: "int"
            read: "audioSampleRate"
            write: "setAudioSampleRate"
            notify: "audioSampleRateChanged"
            index: 15
        }
        Property {
            name: "autoStop"
            revision: 1544
            type: "bool"
            read: "autoStop"
            write: "setAutoStop"
            notify: "autoStopChanged"
            index: 16
        }
        Signal {
            name: "recorderStateChanged"
            Parameter { name: "state"; type: "RecorderState" }
        }
        Signal {
            name: "durationChanged"
            Parameter { name: "duration"; type: "qlonglong" }
        }
        Signal {
            name: "actualLocationChanged"
            Parameter { name: "location"; type: "QUrl" }
        }
        Signal { name: "encoderSettingsChanged" }
        Signal {
            name: "errorOccurred"
            Parameter { name: "error"; type: "Error" }
            Parameter { name: "errorString"; type: "QString" }
        }
        Signal { name: "errorChanged" }
        Signal { name: "metaDataChanged" }
        Signal { name: "mediaFormatChanged" }
        Signal { name: "encodingModeChanged" }
        Signal { name: "qualityChanged" }
        Signal { name: "videoResolutionChanged" }
        Signal { name: "videoFrameRateChanged" }
        Signal { name: "videoBitRateChanged" }
        Signal { name: "audioBitRateChanged" }
        Signal { name: "audioChannelCountChanged" }
        Signal { name: "audioSampleRateChanged" }
        Signal { name: "autoStopChanged"; revision: 1544 }
        Method { name: "record" }
        Method { name: "pause" }
        Method { name: "stop" }
    }
    Component {
        file: "private/qquickimagecapture_p.h"
        name: "QQuickImageCapture"
        accessSemantics: "reference"
        prototype: "QImageCapture"
        exports: ["QtMultimedia/ImageCapture 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "preview"
            type: "QString"
            read: "preview"
            notify: "previewChanged"
            index: 0
            isReadonly: true
        }
        Signal { name: "previewChanged" }
        Method {
            name: "saveToFile"
            isMethodConstant: true
            Parameter { name: "location"; type: "QUrl" }
        }
        Method {
            name: "_q_imageCaptured"
            Parameter { type: "int" }
            Parameter { type: "QImage" }
        }
    }
    Component {
        file: "private/qquickmediaplayer_p.h"
        name: "QQuickMediaPlayer"
        accessSemantics: "reference"
        prototype: "QMediaPlayer"
        exports: [
            "QtMultimedia/MediaPlayer 6.0",
            "QtMultimedia/MediaPlayer 6.8"
        ]
        exportMetaObjectRevisions: [1536, 1544]
        Property {
            name: "source"
            type: "QUrl"
            read: "qmlSource"
            write: "qmlSetSource"
            notify: "qmlSourceChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "duration"
            type: "int"
            read: "qmlDuration"
            notify: "qmlDurationChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "position"
            type: "int"
            read: "qmlPosition"
            write: "setQmlPosition"
            notify: "qmlPositionChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "autoPlay"
            type: "bool"
            read: "autoPlay"
            write: "setAutoPlay"
            notify: "autoPlayChanged"
            index: 3
            isFinal: true
        }
        Signal {
            name: "qmlSourceChanged"
            Parameter { name: "source"; type: "QUrl" }
        }
        Signal {
            name: "qmlPositionChanged"
            Parameter { name: "position"; type: "int" }
        }
        Signal {
            name: "qmlDurationChanged"
            Parameter { name: "duration"; type: "int" }
        }
        Signal {
            name: "autoPlayChanged"
            Parameter { name: "autoPlay"; type: "bool" }
        }
    }
    Component {
        file: "private/qquickscreencapture_p.h"
        name: "QQuickScreenCatpure"
        accessSemantics: "reference"
        prototype: "QScreenCapture"
        exports: ["QtMultimedia/ScreenCapture 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "screen"
            type: "QQuickScreenInfo"
            isPointer: true
            read: "qmlScreen"
            write: "qmlSetScreen"
            notify: "screenChanged"
            index: 0
        }
        Signal {
            name: "screenChanged"
            Parameter { type: "QQuickScreenInfo"; isPointer: true }
        }
    }
    Component {
        file: "private/qquicksoundeffect_p.h"
        name: "QQuickSoundEffect"
        accessSemantics: "reference"
        prototype: "QSoundEffect"
        exports: ["QtMultimedia/SoundEffect 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "source"
            type: "QUrl"
            read: "qmlSource"
            write: "qmlSetSource"
            notify: "sourceChanged"
            index: 0
        }
        Signal {
            name: "sourceChanged"
            Parameter { name: "source"; type: "QUrl" }
        }
    }
    Component {
        file: "private/qquickvideooutput_p.h"
        name: "QQuickVideoOutput"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtMultimedia/VideoOutput 6.0",
            "QtMultimedia/VideoOutput 6.3",
            "QtMultimedia/VideoOutput 6.7",
            "QtMultimedia/VideoOutput 6.9"
        ]
        exportMetaObjectRevisions: [1536, 1539, 1543, 1545]
        Enum {
            name: "FillMode"
            values: ["Stretch", "PreserveAspectFit", "PreserveAspectCrop"]
        }
        Enum {
            name: "EndOfStreamPolicy"
            values: ["ClearOutput", "KeepLastFrame"]
        }
        Property {
            name: "fillMode"
            type: "FillMode"
            read: "fillMode"
            write: "setFillMode"
            notify: "fillModeChanged"
            index: 0
        }
        Property {
            name: "endOfStreamPolicy"
            revision: 1545
            type: "EndOfStreamPolicy"
            read: "endOfStreamPolicy"
            write: "setEndOfStreamPolicy"
            notify: "endOfStreamPolicyChanged"
            index: 1
        }
        Property {
            name: "orientation"
            type: "int"
            read: "orientation"
            write: "setOrientation"
            notify: "orientationChanged"
            index: 2
        }
        Property {
            name: "mirrored"
            revision: 1545
            type: "bool"
            read: "mirrored"
            write: "setMirrored"
            notify: "mirroredChanged"
            index: 3
        }
        Property {
            name: "sourceRect"
            type: "QRectF"
            read: "sourceRect"
            notify: "sourceRectChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "contentRect"
            type: "QRectF"
            read: "contentRect"
            notify: "contentRectChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "videoSink"
            type: "QVideoSink"
            isPointer: true
            read: "videoSink"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Signal { name: "sourceChanged" }
        Signal {
            name: "fillModeChanged"
            Parameter { type: "QQuickVideoOutput::FillMode" }
        }
        Signal { name: "orientationChanged" }
        Signal { name: "mirroredChanged" }
        Signal { name: "sourceRectChanged" }
        Signal { name: "contentRectChanged" }
        Signal {
            name: "endOfStreamPolicyChanged"
            Parameter { type: "QQuickVideoOutput::EndOfStreamPolicy" }
        }
        Method {
            name: "_q_newFrame"
            Parameter { type: "QSize" }
        }
        Method { name: "_q_updateGeometry" }
        Method { name: "_q_invalidateSceneGraph" }
        Method { name: "_q_sceneGraphInitialized" }
        Method { name: "_q_afterFrameEnd" }
        Method { name: "videoSink"; type: "QVideoSink"; isPointer: true; isMethodConstant: true }
        Method { name: "clearOutput"; revision: 1545 }
    }
    Component {
        file: "private/qquickvideooutput_p.h"
        name: "QQuickVideoSink"
        accessSemantics: "reference"
        prototype: "QVideoSink"
        exports: ["QtMultimedia/VideoSink 6.0"]
        exportMetaObjectRevisions: [1536]
        Signal { name: "videoFrameChanged" }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QScreenCapture"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "Error"
            values: [
                "NoError",
                "InternalError",
                "CapturingNotSupported",
                "CaptureFailed",
                "NotFound"
            ]
        }
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            write: "setActive"
            notify: "activeChanged"
            index: 0
        }
        Property {
            name: "screen"
            type: "QScreen"
            isPointer: true
            read: "screen"
            write: "setScreen"
            notify: "screenChanged"
            index: 1
        }
        Property {
            name: "error"
            type: "Error"
            read: "error"
            notify: "errorChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "errorString"
            type: "QString"
            read: "errorString"
            notify: "errorChanged"
            index: 3
            isReadonly: true
        }
        Signal {
            name: "activeChanged"
            Parameter { type: "bool" }
        }
        Signal { name: "errorChanged" }
        Signal {
            name: "screenChanged"
            Parameter { type: "QScreen"; isPointer: true }
        }
        Signal {
            name: "errorOccurred"
            Parameter { name: "error"; type: "QScreenCapture::Error" }
            Parameter { name: "errorString"; type: "QString" }
        }
        Method {
            name: "setActive"
            Parameter { name: "active"; type: "bool" }
        }
        Method { name: "start" }
        Method { name: "stop" }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QScreen"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "name"
            type: "QString"
            read: "name"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "manufacturer"
            type: "QString"
            read: "manufacturer"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "model"
            type: "QString"
            read: "model"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "serialNumber"
            type: "QString"
            read: "serialNumber"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "depth"
            type: "int"
            read: "depth"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "size"
            type: "QSize"
            read: "size"
            notify: "geometryChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "availableSize"
            type: "QSize"
            read: "availableSize"
            notify: "availableGeometryChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "virtualSize"
            type: "QSize"
            read: "virtualSize"
            notify: "virtualGeometryChanged"
            index: 7
            isReadonly: true
        }
        Property {
            name: "availableVirtualSize"
            type: "QSize"
            read: "availableVirtualSize"
            notify: "virtualGeometryChanged"
            index: 8
            isReadonly: true
        }
        Property {
            name: "geometry"
            type: "QRect"
            read: "geometry"
            notify: "geometryChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "availableGeometry"
            type: "QRect"
            read: "availableGeometry"
            notify: "availableGeometryChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "virtualGeometry"
            type: "QRect"
            read: "virtualGeometry"
            notify: "virtualGeometryChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "availableVirtualGeometry"
            type: "QRect"
            read: "availableVirtualGeometry"
            notify: "virtualGeometryChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "physicalSize"
            type: "QSizeF"
            read: "physicalSize"
            notify: "physicalSizeChanged"
            index: 13
            isReadonly: true
        }
        Property {
            name: "physicalDotsPerInchX"
            type: "double"
            read: "physicalDotsPerInchX"
            notify: "physicalDotsPerInchChanged"
            index: 14
            isReadonly: true
        }
        Property {
            name: "physicalDotsPerInchY"
            type: "double"
            read: "physicalDotsPerInchY"
            notify: "physicalDotsPerInchChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "physicalDotsPerInch"
            type: "double"
            read: "physicalDotsPerInch"
            notify: "physicalDotsPerInchChanged"
            index: 16
            isReadonly: true
        }
        Property {
            name: "logicalDotsPerInchX"
            type: "double"
            read: "logicalDotsPerInchX"
            notify: "logicalDotsPerInchChanged"
            index: 17
            isReadonly: true
        }
        Property {
            name: "logicalDotsPerInchY"
            type: "double"
            read: "logicalDotsPerInchY"
            notify: "logicalDotsPerInchChanged"
            index: 18
            isReadonly: true
        }
        Property {
            name: "logicalDotsPerInch"
            type: "double"
            read: "logicalDotsPerInch"
            notify: "logicalDotsPerInchChanged"
            index: 19
            isReadonly: true
        }
        Property {
            name: "devicePixelRatio"
            type: "double"
            read: "devicePixelRatio"
            notify: "physicalDotsPerInchChanged"
            index: 20
            isReadonly: true
        }
        Property {
            name: "primaryOrientation"
            type: "Qt::ScreenOrientation"
            read: "primaryOrientation"
            notify: "primaryOrientationChanged"
            index: 21
            isReadonly: true
        }
        Property {
            name: "orientation"
            type: "Qt::ScreenOrientation"
            read: "orientation"
            notify: "orientationChanged"
            index: 22
            isReadonly: true
        }
        Property {
            name: "nativeOrientation"
            type: "Qt::ScreenOrientation"
            read: "nativeOrientation"
            index: 23
            isReadonly: true
        }
        Property {
            name: "refreshRate"
            type: "double"
            read: "refreshRate"
            notify: "refreshRateChanged"
            index: 24
            isReadonly: true
        }
        Signal {
            name: "geometryChanged"
            Parameter { name: "geometry"; type: "QRect" }
        }
        Signal {
            name: "availableGeometryChanged"
            Parameter { name: "geometry"; type: "QRect" }
        }
        Signal {
            name: "physicalSizeChanged"
            Parameter { name: "size"; type: "QSizeF" }
        }
        Signal {
            name: "physicalDotsPerInchChanged"
            Parameter { name: "dpi"; type: "double" }
        }
        Signal {
            name: "logicalDotsPerInchChanged"
            Parameter { name: "dpi"; type: "double" }
        }
        Signal {
            name: "virtualGeometryChanged"
            Parameter { name: "rect"; type: "QRect" }
        }
        Signal {
            name: "primaryOrientationChanged"
            Parameter { name: "orientation"; type: "Qt::ScreenOrientation" }
        }
        Signal {
            name: "orientationChanged"
            Parameter { name: "orientation"; type: "Qt::ScreenOrientation" }
        }
        Signal {
            name: "refreshRateChanged"
            Parameter { name: "refreshRate"; type: "double" }
        }
    }
    Component {
        file: "qsoundeffect.h"
        name: "QSoundEffect"
        accessSemantics: "reference"
        prototype: "QObject"
        Enum {
            name: "Loop"
            values: ["Infinite"]
        }
        Enum {
            name: "Status"
            values: ["Null", "Loading", "Ready", "Error"]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "loops"
            type: "int"
            read: "loopCount"
            write: "setLoopCount"
            notify: "loopCountChanged"
            index: 1
        }
        Property {
            name: "loopsRemaining"
            type: "int"
            read: "loopsRemaining"
            notify: "loopsRemainingChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "volume"
            type: "float"
            read: "volume"
            write: "setVolume"
            notify: "volumeChanged"
            index: 3
        }
        Property {
            name: "muted"
            type: "bool"
            read: "isMuted"
            write: "setMuted"
            notify: "mutedChanged"
            index: 4
        }
        Property {
            name: "playing"
            type: "bool"
            read: "isPlaying"
            notify: "playingChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 6
            isReadonly: true
        }
        Property {
            name: "audioDevice"
            type: "QAudioDevice"
            read: "audioDevice"
            write: "setAudioDevice"
            notify: "audioDeviceChanged"
            index: 7
        }
        Signal { name: "sourceChanged" }
        Signal { name: "loopCountChanged" }
        Signal { name: "loopsRemainingChanged" }
        Signal { name: "volumeChanged" }
        Signal { name: "mutedChanged" }
        Signal { name: "loadedChanged" }
        Signal { name: "playingChanged" }
        Signal { name: "statusChanged" }
        Signal { name: "audioDeviceChanged" }
        Method { name: "play" }
        Method { name: "stop" }
    }
    Component {
        file: "qvideosink.h"
        name: "QVideoSink"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "subtitleText"
            type: "QString"
            read: "subtitleText"
            write: "setSubtitleText"
            notify: "subtitleTextChanged"
            index: 0
        }
        Property {
            name: "videoSize"
            type: "QSize"
            read: "videoSize"
            notify: "videoSizeChanged"
            index: 1
            isReadonly: true
        }
        Signal {
            name: "videoFrameChanged"
            isMethodConstant: true
            Parameter { name: "frame"; type: "QVideoFrame" }
        }
        Signal {
            name: "subtitleTextChanged"
            isMethodConstant: true
            Parameter { name: "subtitleText"; type: "QString" }
        }
        Signal { name: "videoSizeChanged" }
    }
    Component {
        file: "private/qtmultimediaquicktypes_p.h"
        name: "QWindowCapture"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtMultimedia/WindowCapture 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "Error"
            values: [
                "NoError",
                "InternalError",
                "CapturingNotSupported",
                "CaptureFailed",
                "NotFound"
            ]
        }
        Property {
            name: "active"
            type: "bool"
            read: "isActive"
            write: "setActive"
            notify: "activeChanged"
            index: 0
        }
        Property {
            name: "window"
            type: "QCapturableWindow"
            read: "window"
            write: "setWindow"
            notify: "windowChanged"
            index: 1
        }
        Property {
            name: "error"
            type: "Error"
            read: "error"
            notify: "errorChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "errorString"
            type: "QString"
            read: "errorString"
            notify: "errorChanged"
            index: 3
            isReadonly: true
        }
        Signal {
            name: "activeChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "windowChanged"
            Parameter { name: "window"; type: "QCapturableWindow" }
        }
        Signal { name: "errorChanged" }
        Signal {
            name: "errorOccurred"
            Parameter { name: "error"; type: "QWindowCapture::Error" }
            Parameter { name: "errorString"; type: "QString" }
        }
        Method {
            name: "setActive"
            Parameter { name: "active"; type: "bool" }
        }
        Method { name: "start" }
        Method { name: "stop" }
        Method { name: "capturableWindows"; type: "QCapturableWindow"; isList: true }
    }
}
