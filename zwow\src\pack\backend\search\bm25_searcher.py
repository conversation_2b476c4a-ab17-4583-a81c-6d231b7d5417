"""
BM25搜索器
基于BM25算法的关键词搜索功能
"""

import os
import json
import logging
import string
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Set
from collections import Counter
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.settings_loader import get_setting, get_absolute_path
except ImportError as e:
    print(f"导入配置模块失败: {e}")
    def get_setting(key, default=None): return default
    def get_absolute_path(path): return path

# 配置日志
logger = logging.getLogger(__name__)

class BM25Searcher:
    """BM25搜索器类"""
    
    def __init__(self):
        """初始化BM25搜索器"""
        # BM25参数
        self.k1 = get_setting("search.bm25_k1", 1.2)
        self.b = get_setting("search.bm25_b", 0.75)
        self.max_results = get_setting("search.max_results", 10)
        self.min_score_threshold = get_setting("search.min_score_threshold", 0.1)
        
        # 文档数据
        self.documents = []
        self.tokenized_docs = []
        self.doc_metadata = []
        self.doc_freqs = []
        self.idf = {}
        self.avgdl = 0
        
        # 停用词
        self.stopwords = self._load_stopwords()
        
        logger.info("BM25搜索器已初始化")
    
    def _load_stopwords(self) -> Set[str]:
        """加载停用词"""
        try:
            # 尝试加载停用词文件
            stopwords_path = get_absolute_path("data/stopwords.txt")
            if os.path.exists(stopwords_path):
                with open(stopwords_path, 'r', encoding='utf-8') as f:
                    stopwords = set(line.strip() for line in f if line.strip())
                logger.info(f"加载了 {len(stopwords)} 个停用词")
                return stopwords
            else:
                # 使用默认停用词
                default_stopwords = {
                    '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
                    '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
                    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of',
                    'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had'
                }
                logger.info(f"使用默认停用词，共 {len(default_stopwords)} 个")
                return default_stopwords
        except Exception as e:
            logger.error(f"加载停用词时出错: {str(e)}")
            return set()
    
    def preprocess_text(self, text: str) -> List[str]:
        """
        文本预处理：分词、去停用词、标准化处理
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 处理后的词语列表
        """
        try:
            # 导入jieba分词
            import jieba
        except ImportError:
            logger.warning("jieba未安装，使用简单分词")
            # 简单分词替代
            words = text.split()
        else:
            # 将英文转为小写
            text = text.lower()
            
            # 保留产品型号格式
            import re
            text = re.sub(r'([a-zA-Z]+)[-]?(\d+)', r'\1\2', text)
            
            # 使用jieba进行分词
            words = list(jieba.cut(text))
        
        # 去除停用词和空白词
        words = [w for w in words if w not in self.stopwords and len(w.strip()) > 0]
        
        # 去除纯标点符号的词
        punctuation = set(string.punctuation + '，。！？、；：""''（）【】《》')
        words = [w for w in words if not all(char in punctuation for char in w)]
        
        # 合并相邻的英文字母和数字
        result = []
        i = 0
        while i < len(words):
            current = words[i]
            if i == len(words) - 1 or not (current.isalpha() or current.isdigit()):
                result.append(current)
                i += 1
                continue
            
            next_word = words[i+1]
            if (current.isalpha() and next_word.isdigit()) or (current.isdigit() and next_word.isalpha()):
                result.append(current + next_word)
                i += 2
            else:
                result.append(current)
                i += 1
        
        return result
    
    def build_index(self, documents: List[str], metadata: List[Dict] = None) -> bool:
        """
        构建BM25索引
        
        Args:
            documents: 文档内容列表
            metadata: 文档元数据列表
            
        Returns:
            bool: 构建是否成功
        """
        try:
            if not documents:
                logger.warning("文档列表为空")
                return False
            
            self.documents = documents
            self.doc_metadata = metadata or [{} for _ in documents]
            
            # 对文档进行分词处理
            self.tokenized_docs = [self.preprocess_text(doc) for doc in documents]
            
            # 计算文档频率
            self._calculate_doc_frequencies()
            
            # 计算IDF
            self._calculate_idf()
            
            # 计算平均文档长度
            self.avgdl = sum(len(doc) for doc in self.tokenized_docs) / len(self.tokenized_docs)
            
            logger.info(f"BM25索引构建完成，包含 {len(documents)} 个文档，平均长度: {self.avgdl:.2f}")
            return True
            
        except Exception as e:
            logger.error(f"构建BM25索引时出错: {str(e)}")
            return False
    
    def _calculate_doc_frequencies(self):
        """计算文档频率"""
        self.doc_freqs = []
        for doc in self.tokenized_docs:
            freq = Counter(doc)
            self.doc_freqs.append(freq)
    
    def _calculate_idf(self):
        """计算逆文档频率"""
        import math
        
        # 统计每个词在多少个文档中出现
        df = Counter()
        for doc in self.tokenized_docs:
            for word in set(doc):
                df[word] += 1
        
        # 计算IDF
        N = len(self.tokenized_docs)
        for word, doc_freq in df.items():
            self.idf[word] = math.log((N - doc_freq + 0.5) / (doc_freq + 0.5))
    
    def search(self, query: str, top_k: int = None) -> List[Dict]:
        """
        使用BM25算法搜索文档
        
        Args:
            query: 查询文本
            top_k: 返回的最大结果数量
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        if top_k is None:
            top_k = self.max_results
        
        try:
            # 对查询进行分词处理
            query_tokens = self.preprocess_text(query)
            
            if not query_tokens:
                logger.warning(f"查询分词结果为空: '{query}'")
                return []
            
            logger.info(f"查询 '{query}' 分词结果: {query_tokens}")
            
            # 计算每个文档的BM25得分
            scores = []
            for i, doc in enumerate(self.tokenized_docs):
                score = self._calculate_bm25_score(query_tokens, doc, self.doc_freqs[i])
                scores.append((i, score))
            
            # 按得分降序排序
            scores.sort(key=lambda x: x[1], reverse=True)
            
            # 构建结果
            results = []
            for i, (doc_idx, score) in enumerate(scores[:top_k]):
                if score < self.min_score_threshold:
                    continue
                
                # 获取文档内容片段
                content = self.documents[doc_idx]
                content_snippet = content[:200] + "..." if len(content) > 200 else content
                
                result = {
                    'content': content,
                    'content_snippet': content_snippet,
                    'score': float(score),
                    'rank': i + 1,
                    'metadata': self.doc_metadata[doc_idx],
                    'search_method': 'bm25'
                }
                results.append(result)
            
            logger.info(f"BM25搜索完成，找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"BM25搜索时出错: {str(e)}")
            return []
    
    def _calculate_bm25_score(self, query_tokens: List[str], doc_tokens: List[str], doc_freqs: Counter) -> float:
        """
        计算BM25得分
        
        Args:
            query_tokens: 查询词列表
            doc_tokens: 文档词列表
            doc_freqs: 文档词频统计
            
        Returns:
            float: BM25得分
        """
        score = 0.0
        doc_len = len(doc_tokens)
        
        for token in query_tokens:
            if token in doc_freqs:
                # 词频
                tf = doc_freqs[token]
                
                # IDF
                idf = self.idf.get(token, 0)
                
                # BM25公式
                numerator = tf * (self.k1 + 1)
                denominator = tf + self.k1 * (1 - self.b + self.b * (doc_len / self.avgdl))
                score += idf * (numerator / denominator)
        
        return score
    
    def load_from_vector_db(self, vector_path: str) -> bool:
        """
        从向量数据库加载文档
        
        Args:
            vector_path: 向量数据库路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            vector_path = get_absolute_path(vector_path)
            metadata_path = os.path.join(vector_path, "metadata.json")
            
            if not os.path.exists(metadata_path):
                logger.warning(f"元数据文件不存在: {metadata_path}")
                return False
            
            # 加载元数据
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # 提取文档内容和元数据
            documents = []
            doc_metadata = []
            
            for item in metadata:
                content = item.get('content', '')
                if content:
                    documents.append(content)
                    doc_metadata.append(item.get('metadata', {}))
            
            if not documents:
                logger.warning("没有找到有效的文档内容")
                return False
            
            # 构建索引
            success = self.build_index(documents, doc_metadata)
            if success:
                logger.info(f"从向量数据库加载了 {len(documents)} 个文档")
            
            return success
            
        except Exception as e:
            logger.error(f"从向量数据库加载文档时出错: {str(e)}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取搜索器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            'document_count': len(self.documents),
            'vocabulary_size': len(self.idf),
            'average_doc_length': self.avgdl,
            'stopwords_count': len(self.stopwords),
            'bm25_parameters': {
                'k1': self.k1,
                'b': self.b
            }
        }
