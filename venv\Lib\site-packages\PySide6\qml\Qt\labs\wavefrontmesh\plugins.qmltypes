import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qwavefrontmesh_p.h"
        name: "QWavefrontMesh"
        accessSemantics: "reference"
        prototype: "QQuickShaderEffectMesh"
        exports: [
            "Qt.labs.wavefrontmesh/WavefrontMesh 1.0",
            "Qt.labs.wavefrontmesh/WavefrontMesh 2.0",
            "Qt.labs.wavefrontmesh/WavefrontMesh 6.0"
        ]
        exportMetaObjectRevisions: [256, 512, 1536]
        Enum {
            name: "Error"
            values: [
                "NoError",
                "InvalidSourceError",
                "UnsupportedFaceShapeError",
                "UnsupportedIndexSizeError",
                "FileNotFoundError",
                "NoAttributesError",
                "MissingPositionAttributeError",
                "MissingTextureCoordinateAttributeError",
                "MissingPositionAndTextureCoordinateAttributesError",
                "TooManyAttributesError",
                "InvalidPlaneDefinitionError"
            ]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "lastError"
            type: "Error"
            read: "lastError"
            notify: "lastErrorChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "projectionPlaneV"
            type: "QVector3D"
            read: "projectionPlaneV"
            write: "setProjectionPlaneV"
            notify: "projectionPlaneVChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "projectionPlaneW"
            type: "QVector3D"
            read: "projectionPlaneW"
            write: "setProjectionPlaneW"
            notify: "projectionPlaneWChanged"
            index: 3
            isFinal: true
        }
        Signal { name: "sourceChanged" }
        Signal { name: "lastErrorChanged" }
        Signal { name: "projectionPlaneVChanged" }
        Signal { name: "projectionPlaneWChanged" }
        Method { name: "readData" }
    }
}
