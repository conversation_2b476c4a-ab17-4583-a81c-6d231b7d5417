# ZWOW 部署指南

本文档描述了如何构建和部署ZWOW应用程序。

## 环境要求

### 开发环境
- Python 3.8+
- PySide6
- 所有项目依赖包（见requirements.txt）

### 构建工具
- PyInstaller (自动安装)
- NSIS (可选，用于创建Windows安装程序)

## 快速部署

### 1. 准备环境
```bash
# 安装依赖
pip install -r requirements.txt

# 安装构建工具
pip install pyinstaller
```

### 2. 运行构建脚本
```bash
python deploy/build.py
```

这将自动完成以下步骤：
- 清理之前的构建文件
- 创建PyInstaller规格文件
- 构建可执行文件
- 复制必要的资源文件
- 创建安装程序（如果NSIS可用）

### 3. 输出文件
构建完成后，输出文件位于：
- `dist/ZWOW/` - 可执行文件目录
- `dist/ZWOW_Setup_1.0.0.exe` - Windows安装程序（如果创建）

## 手动构建

### 1. 使用PyInstaller
```bash
# 创建可执行文件
pyinstaller --clean --noconfirm ZWOW.spec
```

### 2. 使用setup.py
```bash
# 安装为Python包
python deploy/setup.py install

# 创建分发包
python deploy/setup.py sdist bdist_wheel
```

## 部署选项

### 选项1：便携版
直接分发 `dist/ZWOW/` 目录，用户可以直接运行 `ZWOW.exe`。

优点：
- 无需安装
- 可以放在任意位置
- 适合企业内部分发

缺点：
- 文件较大
- 无开始菜单快捷方式

### 选项2：安装程序
使用NSIS创建的安装程序 `ZWOW_Setup_1.0.0.exe`。

优点：
- 标准的Windows安装体验
- 自动创建快捷方式
- 支持卸载
- 注册表集成

缺点：
- 需要管理员权限
- 安装到固定位置

### 选项3：Python包
通过pip安装的Python包。

优点：
- 适合开发者
- 易于更新
- 依赖管理

缺点：
- 需要Python环境
- 不适合普通用户

## 配置文件

### 应用程序配置
应用程序的配置文件位于：
- 开发环境：`src/pack/config/`
- 部署环境：`config/`

### 数据目录
应用程序数据存储在：
- 开发环境：`data/`
- 部署环境：`data/`

### 日志目录
日志文件存储在：
- 开发环境：`logs/`
- 部署环境：`logs/`

## 依赖管理

### Python依赖
所有Python依赖都列在 `requirements.txt` 中，PyInstaller会自动打包。

### 系统依赖
- Windows 10/11
- Visual C++ Redistributable (通常已预装)

### 可选依赖
- Node.js (用于WhatsApp集成)
- CUDA (用于GPU加速的向量搜索)

## 测试部署

### 1. 功能测试
```bash
# 运行测试套件
python run_tests.py
```

### 2. 安装测试
1. 在干净的Windows系统上安装
2. 验证所有功能正常工作
3. 检查快捷方式和卸载功能

### 3. 性能测试
- 启动时间
- 内存使用
- 响应速度

## 故障排除

### 构建失败
1. 检查Python版本和依赖
2. 清理构建缓存：`python deploy/build.py`
3. 查看PyInstaller日志

### 运行时错误
1. 检查缺失的DLL文件
2. 验证资源文件路径
3. 查看应用程序日志

### 安装问题
1. 以管理员身份运行安装程序
2. 检查防病毒软件设置
3. 验证系统兼容性

## 版本管理

### 版本号
版本号在以下位置定义：
- `src/pack/config/settings.json`
- `deploy/setup.py`
- `deploy/installer.nsi`

### 发布流程
1. 更新版本号
2. 更新CHANGELOG
3. 运行测试
4. 构建发布包
5. 创建Git标签
6. 发布到分发平台

## 分发渠道

### 内部分发
- 企业文件服务器
- 内部软件仓库
- 邮件分发

### 公开分发
- GitHub Releases
- 官方网站下载
- 软件分发平台

## 更新机制

### 手动更新
用户下载新版本并重新安装。

### 自动更新（计划中）
- 检查更新服务
- 增量更新
- 静默安装

## 许可证和法律

### 开源许可证
项目使用MIT许可证，允许商业使用。

### 第三方组件
确保所有第三方组件的许可证兼容。

### 代码签名（推荐）
为了避免Windows安全警告，建议对可执行文件进行代码签名。

## 支持和维护

### 用户支持
- 用户手册
- 在线帮助
- 技术支持邮箱

### 维护计划
- 定期安全更新
- 功能增强
- 错误修复

---

如有问题，请联系开发团队或查看项目文档。
