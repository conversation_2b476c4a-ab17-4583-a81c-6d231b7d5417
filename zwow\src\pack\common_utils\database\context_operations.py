"""
上下文数据操作模块
提供contexts表的CRUD操作
"""
import logging
from typing import Dict, List, Tuple, Optional, Any
from .db_manager import DatabaseManager

logger = logging.getLogger(__name__)

class ContextOperations:
    """上下文数据操作类"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """
        初始化上下文数据操作
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager or DatabaseManager()
    
    def add_context(self, user_name: str, store_name: str, chat_id: str, context: str, context_trans: str = "") -> int:
        """
        添加或更新上下文
        
        Args:
            user_name: 用户名
            store_name: 店铺名称
            chat_id: 聊天ID
            context: 上下文内容
            context_trans: 翻译后的上下文内容
            
        Returns:
            int: 新上下文ID，失败返回-1
        """
        # 检查是否已存在相同的上下文记录
        existing_context = self.get_context(user_name, store_name, chat_id)
        
        if existing_context:
            # 如果存在，就更新
            query = """
            UPDATE contexts
            SET context = ?, update_time = CURRENT_TIMESTAMP
            WHERE user_name = ? AND store_name = ? AND chat_id = ?
            """
            
            if not self.db_manager.connect():
                return -1
            
            try:
                self.db_manager.cursor.execute(query, (context, user_name, store_name, chat_id))
                self.db_manager.conn.commit()
                return existing_context['id']
            except Exception as e:
                logger.error(f"更新上下文时出错: {str(e)}")
                self.db_manager.conn.rollback()
                return -1
            finally:
                self.db_manager.close()
        else:
            # 如果不存在，就添加新记录
            query = """
            INSERT INTO contexts (user_name, store_name, chat_id, context)
            VALUES (?, ?, ?, ?)
            """
            
            if not self.db_manager.connect():
                return -1
            
            try:
                self.db_manager.cursor.execute(query, (user_name, store_name, chat_id, context))
                self.db_manager.conn.commit()
                return self.db_manager.get_last_insert_id()
            except Exception as e:
                logger.error(f"添加上下文时出错: {str(e)}")
                self.db_manager.conn.rollback()
                return -1
            finally:
                self.db_manager.close()
    
    def get_context(self, user_name: str, store_name: str, chat_id: str) -> Optional[Dict]:
        """
        获取特定的上下文
        
        Args:
            user_name: 用户名
            store_name: 店铺名称
            chat_id: 聊天ID
            
        Returns:
            Optional[Dict]: 上下文信息字典
        """
        query = """
        SELECT id, user_name, store_name, chat_id, context, update_time
        FROM contexts
        WHERE user_name = ? AND store_name = ? AND chat_id = ?
        """
        
        results = self.db_manager.execute_query(query, (user_name, store_name, chat_id))
        if not results:
            return None
        
        return {
            'id': results[0][0],
            'user_name': results[0][1],
            'store_name': results[0][2],
            'chat_id': results[0][3],
            'context': results[0][4],
            'update_time': results[0][5]
        }
    
    def update_context(self, user_name: str, store_name: str, chat_id: str, context: str) -> bool:
        """
        更新上下文内容
        
        Args:
            user_name: 用户名
            store_name: 店铺名称
            chat_id: 聊天ID
            context: 新的上下文内容
            
        Returns:
            bool: 操作是否成功
        """
        query = """
        UPDATE contexts
        SET context = ?, update_time = CURRENT_TIMESTAMP
        WHERE user_name = ? AND store_name = ? AND chat_id = ?
        """
        
        return self.db_manager.execute_update(query, (context, user_name, store_name, chat_id))
    
    def get_contexts_by_user(self, user_name: str) -> List[Dict]:
        """
        获取用户的所有上下文
        
        Args:
            user_name: 用户名
            
        Returns:
            List[Dict]: 上下文信息列表
        """
        query = """
        SELECT id, user_name, store_name, chat_id, context, update_time
        FROM contexts
        WHERE user_name = ?
        """
        
        results = self.db_manager.execute_query(query, (user_name,))
        contexts = []
        
        for row in results:
            contexts.append({
                'id': row[0],
                'user_name': row[1],
                'store_name': row[2],
                'chat_id': row[3],
                'context': row[4],
                'update_time': row[5]
            })
        
        return contexts
    
    def get_contexts_by_store(self, store_name: str) -> List[Dict]:
        """
        获取店铺的所有上下文
        
        Args:
            store_name: 店铺名称
            
        Returns:
            List[Dict]: 上下文信息列表
        """
        query = """
        SELECT id, user_name, store_name, chat_id, context, update_time
        FROM contexts
        WHERE store_name = ?
        """
        
        results = self.db_manager.execute_query(query, (store_name,))
        contexts = []
        
        for row in results:
            contexts.append({
                'id': row[0],
                'user_name': row[1],
                'store_name': row[2],
                'chat_id': row[3],
                'context': row[4],
                'update_time': row[5]
            })
        
        return contexts
    
    def delete_context(self, user_name: str, store_name: str, chat_id: str) -> bool:
        """
        删除特定的上下文
        
        Args:
            user_name: 用户名
            store_name: 店铺名称
            chat_id: 聊天ID
            
        Returns:
            bool: 操作是否成功
        """
        query = """
        DELETE FROM contexts
        WHERE user_name = ? AND store_name = ? AND chat_id = ?
        """
        
        return self.db_manager.execute_update(query, (user_name, store_name, chat_id))
    
    def delete_contexts_by_user(self, user_name: str) -> bool:
        """
        删除用户的所有上下文
        
        Args:
            user_name: 用户名
            
        Returns:
            bool: 操作是否成功
        """
        query = "DELETE FROM contexts WHERE user_name = ?"
        return self.db_manager.execute_update(query, (user_name,))
    
    def delete_contexts_by_store(self, store_name: str) -> bool:
        """
        删除店铺的所有上下文
        
        Args:
            store_name: 店铺名称
            
        Returns:
            bool: 操作是否成功
        """
        query = "DELETE FROM contexts WHERE store_name = ?"
        return self.db_manager.execute_update(query, (store_name,))
    
    def get_all_contexts(self) -> List[Dict]:
        """
        获取所有上下文
        
        Returns:
            List[Dict]: 上下文信息列表
        """
        query = """
        SELECT id, user_name, store_name, chat_id, context, update_time
        FROM contexts
        ORDER BY update_time DESC
        """
        
        results = self.db_manager.execute_query(query)
        contexts = []
        
        for row in results:
            contexts.append({
                'id': row[0],
                'user_name': row[1],
                'store_name': row[2],
                'chat_id': row[3],
                'context': row[4],
                'update_time': row[5]
            })
        
        return contexts
    
    def clear_old_contexts(self, days: int = 30) -> bool:
        """
        清理指定天数之前的上下文记录
        
        Args:
            days: 保留天数
            
        Returns:
            bool: 操作是否成功
        """
        query = """
        DELETE FROM contexts
        WHERE update_time < datetime('now', ?)
        """
        
        days_param = f"-{days} days"
        return self.db_manager.execute_update(query, (days_param,))
    
    def get_context_count(self) -> int:
        """
        获取上下文总数
        
        Returns:
            int: 上下文总数
        """
        query = "SELECT COUNT(*) FROM contexts"
        results = self.db_manager.execute_query(query)
        return results[0][0] if results else 0
    
    def context_exists(self, user_name: str, store_name: str, chat_id: str) -> bool:
        """
        检查上下文是否存在
        
        Args:
            user_name: 用户名
            store_name: 店铺名称
            chat_id: 聊天ID
            
        Returns:
            bool: 上下文是否存在
        """
        context = self.get_context(user_name, store_name, chat_id)
        return context is not None
