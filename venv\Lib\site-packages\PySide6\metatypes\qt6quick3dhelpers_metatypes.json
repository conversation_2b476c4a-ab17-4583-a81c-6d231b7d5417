[{"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}, {"name": "QML.AddedInVersion", "value": "1545"}], "className": "ConeGeometry", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}], "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "topRadius", "notify": "topRadiusChanged", "read": "topRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTopRadius"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "bottomRadius", "notify": "bottomRadiusChanged", "read": "bottomRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setBottomRadius"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "segments", "notify": "segmentsChanged", "read": "segments", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSegments"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "asynchronous", "notify": "asynchronousChanged", "read": "asynchronous", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAsynchronous"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "ConeGeometry", "signals": [{"access": "public", "index": 0, "name": "topRadiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "bottomRadiusChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "lengthChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "segmentsChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "asynchronousChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "statusChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 7, "name": "doUpdateGeometry", "returnType": "void"}, {"access": "private", "index": 8, "name": "requestFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DGeometry"}]}], "inputFile": "conegeometry_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}, {"name": "QML.AddedInVersion", "value": "1545"}], "className": "CuboidGeometry", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}], "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "xExtent", "notify": "xExtentChanged", "read": "xExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setXExtent"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "yExtent", "notify": "yExtentChanged", "read": "yExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setYExtent"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "zExtent", "notify": "zExtentChanged", "read": "zExtent", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setZExtent"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "yzMeshResolution", "notify": "yzMeshResolutionChanged", "read": "yzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setYzMeshResolution"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "xzMeshResolution", "notify": "xzMeshResolutionChanged", "read": "xzMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXzMeshResolution"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "xyMeshResolution", "notify": "xyMeshResolutionChanged", "read": "xyMeshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setXyMeshResolution"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "asynchronous", "notify": "asynchronousChanged", "read": "asynchronous", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAsynchronous"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "CuboidGeometry", "signals": [{"access": "public", "index": 0, "name": "xExtentChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "yExtentChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "zExtentChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "yzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "xzMeshResolutionChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "xyMeshResolutionChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "asynchronousChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "statusChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 8, "name": "doUpdateGeometry", "returnType": "void"}, {"access": "private", "index": 9, "name": "requestFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DGeometry"}]}], "inputFile": "cuboidgeometry_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}, {"name": "QML.AddedInVersion", "value": "1545"}], "className": "CylinderGeometry", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}], "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "length", "notify": "lengthChanged", "read": "length", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "segments", "notify": "segmentsChanged", "read": "segments", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSegments"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "asynchronous", "notify": "asynchronousChanged", "read": "asynchronous", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAsynchronous"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "CylinderGeometry", "signals": [{"access": "public", "index": 0, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "lengthChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "segmentsChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "asynchronousChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "statusChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 6, "name": "doUpdateGeometry", "returnType": "void"}, {"access": "private", "index": 7, "name": "requestFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DGeometry"}]}], "inputFile": "cylindergeometry_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}, {"name": "QML.AddedInVersion", "value": "1545"}], "className": "ExtrudedTextGeometry", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}], "lineNumber": 30, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "text", "notify": "textChanged", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "font", "notify": "fontChanged", "read": "font", "required": false, "scriptable": true, "stored": true, "type": "QFont", "user": false, "write": "setFont"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "depth", "notify": "depthChanged", "read": "depth", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "scale", "notify": "scaleChanged", "read": "scale", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setScale"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "asynchronous", "notify": "asynchronousChanged", "read": "asynchronous", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAsynchronous"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "ExtrudedTextGeometry", "signals": [{"access": "public", "index": 0, "name": "textChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "fontChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "depthChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "scaleChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "asynchronousChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "statusChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 6, "name": "doUpdateGeometry", "returnType": "void"}, {"access": "private", "index": 7, "name": "requestFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DGeometry"}]}], "inputFile": "extrudedtextgeometry_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "GridGeometry"}], "className": "GridGeometry", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "horizontalLines", "notify": "horizontalLinesChanged", "read": "horizontalLines", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHorizontalLines"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "verticalLines", "notify": "verticalLinesChanged", "read": "verticalLines", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setVerticalLines"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "horizontalStep", "notify": "horizontalStepChanged", "read": "horizontalStep", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setHorizontalStep"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "verticalStep", "notify": "verticalStepChanged", "read": "verticalStep", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setVerticalStep"}], "qualifiedClassName": "GridGeometry", "signals": [{"access": "public", "index": 0, "name": "horizontalLinesChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "verticalLinesChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "horizontalStepChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "verticalStepChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 4, "name": "setHorizontalLines", "returnType": "void"}, {"access": "public", "arguments": [{"name": "count", "type": "int"}], "index": 5, "name": "setVerticalLines", "returnType": "void"}, {"access": "public", "arguments": [{"name": "step", "type": "float"}], "index": 6, "name": "setHorizontalStep", "returnType": "void"}, {"access": "public", "arguments": [{"name": "step", "type": "float"}], "index": 7, "name": "setVerticalStep", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DGeometry"}]}], "inputFile": "gridgeometry_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "HeightFieldGeometry"}], "className": "HeightFieldGeometry", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "source", "notify": "sourceChanged", "read": "source", "required": false, "revision": 1541, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "smoothShading", "notify": "smoothShadingChanged", "read": "smoothShading", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setSmoothShading"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "extents", "notify": "extentsChanged", "read": "extents", "required": false, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setExtents"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "heightMap", "notify": "sourceChanged", "read": "source", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setSource"}], "qualifiedClassName": "HeightFieldGeometry", "signals": [{"access": "public", "index": 0, "name": "sourceChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "smoothShadingChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "extentsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DGeometry"}]}], "inputFile": "heightfieldgeometry_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "InfiniteGrid"}], "className": "QQuick3DInfiniteGrid", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 24, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "visible", "notify": "visibleChanged", "read": "visible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setVisible"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "gridInterval", "notify": "gridIntervalChanged", "read": "gridInterval", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setGridInterval"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "gridAxes", "notify": "gridAxesChanged", "read": "gridAxes", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setGridAxes"}], "qualifiedClassName": "QQuick3DInfiniteGrid", "signals": [{"access": "public", "index": 0, "name": "visibleChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "gridIntervalChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "gridAxesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "infinitegrid_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "InstanceModel"}, {"name": "QML.AddedInVersion", "value": "1540"}], "className": "InstanceModel", "lineNumber": 27, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "instancingTable", "notify": "instancingChanged", "read": "instancing", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstancing*", "user": false, "write": "setInstancing"}], "qualifiedClassName": "InstanceModel", "signals": [{"access": "public", "index": 0, "name": "instancingChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 1, "name": "reset", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractListModel"}]}, {"classInfos": [{"name": "QML.Element", "value": "In<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "1540"}], "className": "In<PERSON><PERSON><PERSON><PERSON><PERSON>", "lineNumber": 75, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "instancingTable", "notify": "instancingChanged", "read": "instancing", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstancing*", "user": false, "write": "setInstancing"}], "qualifiedClassName": "In<PERSON><PERSON><PERSON><PERSON><PERSON>", "signals": [{"access": "public", "index": 0, "name": "instancingChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DRepeater"}]}], "inputFile": "instancerepeater_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "LookAtNode"}, {"name": "QML.AddedInVersion", "value": "1540"}], "className": "LookAtNode", "lineNumber": 22, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "target", "notify": "targetChanged", "read": "target", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DNode*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}], "qualifiedClassName": "LookAtNode", "signals": [{"access": "public", "index": 0, "name": "targetChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "node", "type": "QQuick3DNode*"}], "index": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "private", "index": 2, "name": "updateLookAt", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DNode"}]}], "inputFile": "lookatnode_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}, {"name": "QML.AddedInVersion", "value": "1545"}], "className": "PlaneGeometry", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}, {"isClass": false, "isFlag": false, "name": "Plane", "values": ["XY", "XZ", "ZY"]}], "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "height", "notify": "heightChanged", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setHeight"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "meshResolution", "notify": "meshResolutionChanged", "read": "meshResolution", "required": false, "scriptable": true, "stored": true, "type": "QSize", "user": false, "write": "setMeshResolution"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "plane", "notify": "planeChanged", "read": "plane", "required": false, "scriptable": true, "stored": true, "type": "Plane", "user": false, "write": "<PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "reversed", "notify": "reversedChanged", "read": "reversed", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setReversed"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "mirrored", "notify": "mirroredChanged", "read": "mirrored", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "set<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "asynchronous", "notify": "asynchronousChanged", "read": "asynchronous", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAsynchronous"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "PlaneGeometry", "signals": [{"access": "public", "index": 0, "name": "widthChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "heightChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "meshResolutionChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "planeChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "mirroredChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "asynchronousChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "statusChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "reversedChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 8, "name": "doUpdateGeometry", "returnType": "void"}, {"access": "private", "index": 9, "name": "requestFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DGeometry"}]}], "inputFile": "planegeometry_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ProceduralMeshSubset"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "ProceduralMeshSubset", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "offset", "notify": "offsetChanged", "read": "offset", "required": false, "scriptable": true, "stored": true, "type": "uint", "user": false, "write": "setOffset"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "uint", "user": false, "write": "setCount"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}], "qualifiedClassName": "ProceduralMeshSubset", "signals": [{"access": "public", "index": 0, "name": "offsetChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "countChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "nameChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "isDirty", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "auto"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "Procedural<PERSON><PERSON>", "enums": [{"isClass": false, "isFlag": false, "name": "PrimitiveMode", "values": ["Points", "LineStrip", "Lines", "TriangleStrip", "TriangleFan", "Triangles"]}], "lineNumber": 54, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "positions", "notify": "positionsChanged", "read": "positions", "required": false, "scriptable": true, "stored": true, "type": "QList<QVector3D>", "user": false, "write": "setPositions"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "normals", "notify": "normalsChanged", "read": "normals", "required": false, "scriptable": true, "stored": true, "type": "QList<QVector3D>", "user": false, "write": "setNormals"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "tangents", "notify": "tangentsChanged", "read": "tangents", "required": false, "scriptable": true, "stored": true, "type": "QList<QVector3D>", "user": false, "write": "setTangents"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "binormals", "notify": "binormalsChanged", "read": "binormals", "required": false, "scriptable": true, "stored": true, "type": "QList<QVector3D>", "user": false, "write": "setBinormals"}, {"constant": false, "designable": true, "final": true, "index": 4, "name": "uv0s", "notify": "uv0sChanged", "read": "uv0s", "required": false, "scriptable": true, "stored": true, "type": "QList<QVector2D>", "user": false, "write": "setUv0s"}, {"constant": false, "designable": true, "final": true, "index": 5, "name": "uv1s", "notify": "uv1sChanged", "read": "uv1s", "required": false, "scriptable": true, "stored": true, "type": "QList<QVector2D>", "user": false, "write": "setUv1s"}, {"constant": false, "designable": true, "final": true, "index": 6, "name": "colors", "notify": "colorsChanged", "read": "colors", "required": false, "scriptable": true, "stored": true, "type": "QList<QVector4D>", "user": false, "write": "setColors"}, {"constant": false, "designable": true, "final": true, "index": 7, "name": "joints", "notify": "jointsChanged", "read": "joints", "required": false, "scriptable": true, "stored": true, "type": "QList<QVector4D>", "user": false, "write": "setJoints"}, {"constant": false, "designable": true, "final": true, "index": 8, "name": "weights", "notify": "weightsChanged", "read": "weights", "required": false, "scriptable": true, "stored": true, "type": "QList<QVector4D>", "user": false, "write": "setWeights"}, {"constant": false, "designable": true, "final": true, "index": 9, "name": "indexes", "notify": "indexesChanged", "read": "indexes", "required": false, "scriptable": true, "stored": true, "type": "QList<uint>", "user": false, "write": "setIndexes"}, {"constant": false, "designable": true, "final": true, "index": 10, "name": "subsets", "read": "subsets", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<ProceduralMeshSubset>", "user": false}, {"constant": false, "designable": true, "final": true, "index": 11, "name": "primitiveMode", "notify": "primitiveModeChanged", "read": "primitiveMode", "required": false, "scriptable": true, "stored": true, "type": "PrimitiveMode", "user": false, "write": "setPrimitiveMode"}], "qualifiedClassName": "Procedural<PERSON><PERSON>", "signals": [{"access": "public", "index": 0, "name": "positionsChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "primitiveModeChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "indexesChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "normalsChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "tangentsChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "binormalsChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "uv0sChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "uv1sChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "colorsChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "jointsChanged", "returnType": "void"}, {"access": "public", "index": 10, "name": "weightsChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 11, "name": "requestUpdate", "returnType": "void"}, {"access": "private", "index": 12, "name": "updateGeometry", "returnType": "void"}, {"access": "private", "arguments": [{"name": "subset", "type": "QObject*"}], "index": 13, "name": "subsetDestroyed", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DGeometry"}]}], "inputFile": "proceduralmesh_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}], "className": "ProceduralSkyTextureData", "enums": [{"isClass": true, "isFlag": false, "name": "SkyTextureQuality", "values": ["SkyTextureQualityLow", "SkyTextureQualityMedium", "SkyTextureQualityHigh", "SkyTextureQualityVeryHigh"]}], "lineNumber": 27, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "skyTopColor", "notify": "skyTopColorChanged", "read": "skyTopColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSkyTopColor"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "skyHorizonColor", "notify": "skyHorizonColorChanged", "read": "skyHorizonColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSkyHorizonColor"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "skyCurve", "notify": "skyCurveChanged", "read": "skyCurve", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSkyCurve"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "skyEnergy", "notify": "skyEnergyChanged", "read": "skyEnergy", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSkyEnergy"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "groundBottomColor", "notify": "groundBottomColorChanged", "read": "groundBottomColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setGroundBottomColor"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "groundHorizonColor", "notify": "groundHorizonColorChanged", "read": "groundHorizonColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setGroundHorizonColor"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "groundCurve", "notify": "groundCurveChanged", "read": "groundCurve", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setGroundCurve"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "groundEnergy", "notify": "groundEnergyChanged", "read": "groundEnergy", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setGroundEnergy"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "sunColor", "notify": "sunColorChanged", "read": "sunColor", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setSunColor"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "sunLatitude", "notify": "sunLatitudeChanged", "read": "sunLatitude", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSunLatitude"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "sunLongitude", "notify": "sunLongitudeChanged", "read": "sunLongitude", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSunLongitude"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "sunAngleMin", "notify": "sunAngleMinChanged", "read": "sunAngleMin", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSunAngleMin"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "sunAngleMax", "notify": "sunAngleMaxChanged", "read": "sunAngleMax", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSunAngleMax"}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "sunCurve", "notify": "sunCurveChanged", "read": "sunCurve", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSunCurve"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "sunEnergy", "notify": "sunEnergyChanged", "read": "sunEnergy", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setSunEnergy"}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "textureQuality", "notify": "textureQualityChanged", "read": "textureQuality", "required": false, "scriptable": true, "stored": true, "type": "SkyTextureQuality", "user": false, "write": "setTextureQuality"}], "qualifiedClassName": "ProceduralSkyTextureData", "signals": [{"access": "public", "arguments": [{"name": "skyTopColor", "type": "QColor"}], "index": 0, "name": "skyTopColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skyHorizonColor", "type": "QColor"}], "index": 1, "name": "skyHorizonColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skyCurve", "type": "float"}], "index": 2, "name": "skyCurveChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skyEnergy", "type": "float"}], "index": 3, "name": "skyEnergyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundBottomColor", "type": "QColor"}], "index": 4, "name": "groundBottomColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundHorizonColor", "type": "QColor"}], "index": 5, "name": "groundHorizonColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundCurve", "type": "float"}], "index": 6, "name": "groundCurveChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundEnergy", "type": "float"}], "index": 7, "name": "groundEnergyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunColor", "type": "QColor"}], "index": 8, "name": "sunColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunLatitude", "type": "float"}], "index": 9, "name": "sunLatitudeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunLongitude", "type": "float"}], "index": 10, "name": "sunLongitudeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunAngleMin", "type": "float"}], "index": 11, "name": "sunAngleMinChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunAngleMax", "type": "float"}], "index": 12, "name": "sunAngleMaxChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunCurve", "type": "float"}], "index": 13, "name": "sunCurveChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunEnergy", "type": "float"}], "index": 14, "name": "sunEnergyChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureQuality", "type": "SkyTextureQuality"}], "index": 15, "name": "textureQualityChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "skyTopColor", "type": "QColor"}], "index": 16, "name": "setSkyTopColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skyHorizonColor", "type": "QColor"}], "index": 17, "name": "setSkyHorizonColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skyCurve", "type": "float"}], "index": 18, "name": "setSkyCurve", "returnType": "void"}, {"access": "public", "arguments": [{"name": "skyEnergy", "type": "float"}], "index": 19, "name": "setSkyEnergy", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundBottomColor", "type": "QColor"}], "index": 20, "name": "setGroundBottomColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundHorizonColor", "type": "QColor"}], "index": 21, "name": "setGroundHorizonColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundCurve", "type": "float"}], "index": 22, "name": "setGroundCurve", "returnType": "void"}, {"access": "public", "arguments": [{"name": "groundEnergy", "type": "float"}], "index": 23, "name": "setGroundEnergy", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunColor", "type": "QColor"}], "index": 24, "name": "setSunColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunLatitude", "type": "float"}], "index": 25, "name": "setSunLatitude", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunLongitude", "type": "float"}], "index": 26, "name": "setSunLongitude", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunAngleMin", "type": "float"}], "index": 27, "name": "setSunAngleMin", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunAngleMax", "type": "float"}], "index": 28, "name": "setSunAngleMax", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunCurve", "type": "float"}], "index": 29, "name": "setSunCurve", "returnType": "void"}, {"access": "public", "arguments": [{"name": "sunEnergy", "type": "float"}], "index": 30, "name": "setSunEnergy", "returnType": "void"}, {"access": "public", "arguments": [{"name": "textureQuality", "type": "SkyTextureQuality"}], "index": 31, "name": "setTextureQuality", "returnType": "void"}, {"access": "public", "index": 32, "name": "generateRGBA16FTexture", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DTextureData"}]}], "inputFile": "proceduralskytexturedata_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ProceduralTextureData"}, {"name": "QML.AddedInVersion", "value": "1542"}], "className": "QQuick3DTextureDataFrontend", "lineNumber": 25, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "format", "notify": "formatChanged", "read": "format", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DTextureData::Format", "user": false, "write": "setFormat"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "height", "notify": "heightChanged", "read": "height", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setHeight"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "depth", "notify": "depthChanged", "read": "depth", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "hasTransparency", "notify": "hasTransparencyChanged", "read": "hasTransparency", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHasTransparency"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "textureData", "notify": "textureDataChanged", "read": "textureData", "required": false, "scriptable": true, "stored": true, "type": "QByteArray", "user": false, "write": "setTextureData"}], "qualifiedClassName": "QQuick3DTextureDataFrontend", "signals": [{"access": "public", "index": 0, "name": "formatChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "depthChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "hasTransparencyChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "textureDataChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "widthChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "heightChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DTextureData"}]}], "inputFile": "qquick3dtexturedatafrontend_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.AddedInVersion", "value": "1538"}, {"name": "QML.Element", "value": "InstanceRange"}], "className": "QQuick3DInstanceRange", "lineNumber": 23, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "from", "notify": "fromChanged", "read": "from", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setFrom"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "to", "notify": "to<PERSON><PERSON><PERSON>", "read": "to", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setTo"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "proportional", "notify": "proportionalChanged", "read": "proportional", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setProportional"}], "qualifiedClassName": "QQuick3DInstanceRange", "signals": [{"access": "public", "index": 0, "name": "fromChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "to<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 2, "name": "proportionalChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "changed", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "from", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 4, "name": "setFrom", "returnType": "void"}, {"access": "public", "arguments": [{"name": "to", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 5, "name": "setTo", "returnType": "void"}, {"access": "public", "arguments": [{"name": "proportional", "type": "bool"}], "index": 6, "name": "setProportional", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "RandomInstancing"}, {"name": "QML.AddedInVersion", "value": "1538"}], "className": "QQuick3DRandomInstancing", "enums": [{"isClass": true, "isFlag": false, "name": "ColorModel", "values": ["RGB", "HSV", "HSL"]}], "lineNumber": 73, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "instanceCount", "notify": "instanceCountChanged", "read": "instanceCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setInstanceCount"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "position", "notify": "positionChanged", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstanceRange*", "user": false, "write": "setPosition"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "scale", "notify": "scaleChanged", "read": "scale", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstanceRange*", "user": false, "write": "setScale"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "rotation", "notify": "rotationChanged", "read": "rotation", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstanceRange*", "user": false, "write": "setRotation"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstanceRange*", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "colorModel", "notify": "colorModelChanged", "read": "colorModel", "required": false, "scriptable": true, "stored": true, "type": "ColorModel", "user": false, "write": "setColorModel"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "customData", "notify": "customDataChanged", "read": "customData", "required": false, "scriptable": true, "stored": true, "type": "QQuick3DInstanceRange*", "user": false, "write": "setCustomData"}, {"constant": false, "designable": true, "final": true, "index": 7, "name": "gridSpacing", "notify": "gridSpacingChanged", "read": "gridSpacing", "required": false, "revision": 1545, "scriptable": true, "stored": true, "type": "QVector3D", "user": false, "write": "setGridSpacing"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "randomSeed", "notify": "randomSeedChanged", "read": "randomSeed", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRandomSeed"}], "qualifiedClassName": "QQuick3DRandomInstancing", "signals": [{"access": "public", "index": 0, "name": "instanceCountChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "randomSeedChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "positionChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "scaleChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "rotationChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "colorChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "customDataChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "colorModelChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "gridSpacingChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "instanceCount", "type": "int"}], "index": 9, "name": "setInstanceCount", "returnType": "void"}, {"access": "public", "arguments": [{"name": "randomSeed", "type": "int"}], "index": 10, "name": "setRandomSeed", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QQuick3DInstanceRange*"}], "index": 11, "name": "setPosition", "returnType": "void"}, {"access": "public", "arguments": [{"name": "scale", "type": "QQuick3DInstanceRange*"}], "index": 12, "name": "setScale", "returnType": "void"}, {"access": "public", "arguments": [{"name": "rotation", "type": "QQuick3DInstanceRange*"}], "index": 13, "name": "setRotation", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QQuick3DInstanceRange*"}], "index": 14, "name": "setColor", "returnType": "void"}, {"access": "public", "arguments": [{"name": "customData", "type": "QQuick3DInstanceRange*"}], "index": 15, "name": "setCustomData", "returnType": "void"}, {"access": "public", "arguments": [{"name": "colorModel", "type": "ColorModel"}], "index": 16, "name": "setColorModel", "returnType": "void"}, {"access": "private", "index": 17, "name": "handleChange", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DInstancing"}]}], "inputFile": "randominstancing_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}, {"name": "QML.AddedInVersion", "value": "1545"}], "className": "SphereGeometry", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}], "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "segments", "notify": "segmentsChanged", "read": "segments", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSegments"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "asynchronous", "notify": "asynchronousChanged", "read": "asynchronous", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAsynchronous"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "SphereGeometry", "signals": [{"access": "public", "index": 0, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "segmentsChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "asynchronousChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "statusChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 5, "name": "doUpdateGeometry", "returnType": "void"}, {"access": "private", "index": 6, "name": "requestFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DGeometry"}]}], "inputFile": "spheregeometry_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "auto"}, {"name": "QML.AddedInVersion", "value": "1545"}], "className": "TorusGeometry", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}], "lineNumber": 29, "object": true, "properties": [{"constant": false, "designable": true, "final": true, "index": 0, "name": "rings", "notify": "ringsChanged", "read": "rings", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setRings"}, {"constant": false, "designable": true, "final": true, "index": 1, "name": "segments", "notify": "segmentsChanged", "read": "segments", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setSegments"}, {"constant": false, "designable": true, "final": true, "index": 2, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": true, "index": 3, "name": "tubeRadius", "notify": "tubeRadiusChanged", "read": "tubeRadius", "required": false, "scriptable": true, "stored": true, "type": "float", "user": false, "write": "setTubeRadius"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "asynchronous", "notify": "asynchronousChanged", "read": "asynchronous", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAsynchronous"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "TorusGeometry", "signals": [{"access": "public", "index": 0, "name": "ringsChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "segmentsChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "tubeRadiusChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "asynchronousChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "statusChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 6, "name": "doUpdateGeometry", "returnType": "void"}, {"access": "private", "index": 7, "name": "requestFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuick3DGeometry"}]}], "inputFile": "torusgeometry_p.h", "outputRevision": 69}]