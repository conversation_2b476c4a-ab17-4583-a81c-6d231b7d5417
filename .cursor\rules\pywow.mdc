---
description: 
globs: *.py
alwaysApply: false
---
---
name: 易于打包与清晰目录结构的 Python/PySide6 应用开发
description: 针对 Python 代码、PySide6 UI 以及打包成独立可执行文件的综合规则，强调易于打包和清晰的项目结构。
glob: "**/*.py"
---

### I. 通用 Python 代码质量指令：

1.  **PEP 8 规范：** 所有生成的 Python 代码必须严格遵守 **PEP 8 风格指南**。这包括但不限于变量、函数、类和模块的命名约定；4 空格缩进；合理的行长度；以及空行和注释的一致使用。
2.  **类型提示：** **始终为函数参数、返回值、变量赋值和类属性使用明确的类型提示**。这增强了可读性、可维护性，并有助于静态分析。
3.  **文档字符串：** 为所有模块、类、函数和方法提供**全面清晰的文档字符串**。为保持文档格式的一致性，优先使用 **Google Style** 或 **Sphinx Style**。
4.  **健壮的错误处理：** 使用 `try-except` 块实现**特定且健壮的错误处理**。**切勿使用裸 `except:` 子句；始终捕获特定的异常类型**，以避免掩盖意外错误。
5.  **日志记录：** 利用 Python 内置的 **`logging` 模块**来记录所有应用程序事件、警告和错误。在生产代码中，**避免使用 `print()` 语句**进行调试或例行信息输出。
6.  **虚拟环境：** 假定并建议项目在**虚拟环境**中运行，以实现独立的依赖项管理。

---

### II. PySide6 UI 开发与良好实践：

1.  **仅限 PySide6：** **所有图形用户界面 (GUI) 组件必须完全使用 PySide6 构建。** 不允许使用其他 UI 框架（例如 PyQt6、Tkinter、Kivy、Web-based frameworks）。
2.  **清晰的用户界面：** 设计直观、易于使用的界面，避免不必要的复杂性。确保用户能够轻松理解和操作应用程序。
3.  **一致性：** 保持应用程序中术语、图标、交互模式和组件行为的严格一致性。
4.  **标准控件优先：** 优先使用标准的 PySide6 小部件（例如 `QPushButton`、`QLabel`、`QLineEdit`、`QTableView`、`QComboBox`）和标准对话框（`QFileDialog`、`QMessageBox`），以确保稳定性和兼容性。
5.  **有效布局：** 有效利用 PySide6 的布局管理器（`QHBoxLayout`、`QVBoxLayout`、`QGridLayout`、`QFormLayout`）来创建响应式、自适应的 UI，并保持一致的间距。
6.  **PySide6 特定编码实践：**
    * **信号与槽：** 始终使用标准的 PySide6 `signals` 和 `slots` 机制进行对象间通信和事件处理。
    * **单个 `QApplication` 实例：** 确保在应用程序的整个生命周期中只创建一个 `QApplication` 实例。
    * **UI 线程安全：** 所有 UI 更新和操作都必须在主 (UI) 线程上执行。对于长时间运行或阻塞操作，实现异步模式或利用 `QThread` 以防止 UI 冻结。

---

### III. 可执行文件打包（.exe / 安装程序）考虑事项：

1.  **打包目标：** 所有生成的代码和建议的实践都必须明确地促进轻松可靠地编译成独立的 `.exe` 可执行文件或用户友好的安装程序（例如，使用 `PyInstaller`）。
2.  **资源管理与访问：**
    * **关键指令：所有内部应用程序资源（图像、图标、QSS 文件、数据文件）必须使用 `importlib.resources`（适用于 Python 3.7+）访问。** 这确保了在编译的可执行文件中正确捆绑和访问资源，独立于文件系统路径。如果生成引用资源的代码，请明确显示如何通过 `importlib.resources.files('your_package.path_to_assets') / 'your_file.png'` 访问它们。
    * **避免为内部资源硬编码绝对或相对文件系统路径。**
3.  **依赖项管理：** 假定 `pyproject.toml` (PEP 621) 是项目元数据和依赖项声明的主要方法。确保所有必需的第三方库（尤其是 `PySide6`）都明确列出。
4.  **C 语言扩展：** **强烈不鼓励和避免生成依赖 C 语言扩展的代码。** 这些会显著增加跨平台打包的复杂性，需要特定的构建环境。如果对于关键性能绝对不可避免，请清楚地注明增加的复杂性。
5.  **运行时文件生成：** **切勿在应用程序的安装目录（.exe 所在的位置）中直接生成或修改文件。** 应用程序创建、修改或保存的任何文件（例如用户数据、日志、配置）都必须写入平台特定的用户应用程序数据目录（例如使用 PySide6 的 `QStandardPaths`）或临时目录。
6.  **动态 `sys.path` 操作：** **避免运行时修改 `sys.path`**。这种做法可能导致不可预测的行为，并阻碍打包工具发现和捆绑依赖项。
7.  **清晰的应用程序入口点：** 假定应用程序将有一个清晰、明确定义的 Python 函数或脚本（例如，在根目录的 `main.py` 中调用包内部的 `run_app()` 函数），作为 PyInstaller 等打包工具的明确入口点。
8.  **GUI 应用程序模式：** 当提供打包建议时，假定编译后的应用程序将在“无控制台”模式下运行（`PyInstaller --noconsole`），防止命令行窗口与 GUI 一起出现。
9.  **严格的测试文件放置：** 所有测试文件，无论其范围（单元测试、集成测试或端到端测试），**都必须仅放置在指定的 `zwow/test/` 目录下。** 在任何情况下，测试文件都不得放置在项目结构中的任何其他位置。这种严格的遵守确保了测试的可发现性、组织的一致性，并防止了在生产构建中意外包含。
10. **自动化文档生成：** 对于生成的或显著修改的每个 Python 文件，**必须同时生成一个具有相同基本文件名的对应 Markdown (.md) 文件。** 此配套 .md 文件将作为全面的使用指南，详细说明 Python 文件中的所有函数、类和关键方法，包括它们的用途、参数、返回值以及任何特定的使用示例或注意事项。这确保了文档与代码紧密结合，促进了清晰度和可维护性。

---

### IV. AI 问题解决方法（工程师思维）：

11. **深度问题解决：** 认识到复杂和重复出现的问题，特别是那些与代码结构、设计原则或微妙的跨模块交互相关的问题，无法通过肤浅的尝试或试错来解决。当面临**持续或反复出现**的问题时，请采取严格的**“工程师思维”**。这意味着：
    * **系统代码分析：** 暂停立即修改。相反，对受影响的代码路径进行彻底的、逐步的分析。追踪数据流，检查函数调用序列，并仔细审查逻辑分支。
    * **根本原因识别：** 专注于查明问题的根本原因，而不是仅仅解决症状。这可能涉及重新审视基本设计决策、模块职责或架构模式。
    * **原则重新评估：** 有意识地重新评估核心软件工程原则（例如，SOLID、关注点分离）在问题区域的应用。确定这些原则的偏差或误解是否导致了问题。
    * **主动解决方案：** 提出不仅能解决眼前问题，还能提高代码清晰度、可维护性并防止未来出现类似问题的解决方案。优先考虑与项目总体愿景和既定指南一致的解决方案，而不是快速修复。

---

**特别注意：** 所有测试文件必须严格放置在 `C:\Users\<USER>\Desktop\small\zwow\test` 目录下。

**AI 生成文件时请务必认真读取整个项目结构和现有代码，以确保生成的代码与项目高度融合。**