"""
模块功能测试脚本
测试迁移后的各个功能模块是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent
sys.path.insert(0, str(project_root))

def test_config_system():
    """测试配置系统"""
    print("=" * 50)
    print("测试配置系统...")
    
    try:
        from src.pack.config.settings_loader import get_setting, update_setting
        from src.pack.config.config_manager import ConfigManager
        
        # 测试基本配置读取
        app_name = get_setting("app_name", "默认应用")
        print(f"✓ 应用名称: {app_name}")
        
        # 测试配置管理器
        config_manager = ConfigManager()
        version = config_manager.get_config("version", "1.0.0")
        print(f"✓ 版本信息: {version}")
        
        print("✓ 配置系统测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置系统测试失败: {str(e)}")
        return False

def test_database_system():
    """测试数据库系统"""
    print("=" * 50)
    print("测试数据库系统...")
    
    try:
        from src.pack.common_utils.database.app_db_manager import AppDBManager
        
        # 初始化数据库管理器
        db_manager = AppDBManager()
        result = db_manager.initialize()
        
        if result:
            print("✓ 数据库初始化成功")
            
            # 测试基本操作
            users = db_manager.get_all_users()
            stores = db_manager.get_all_stores()
            
            print(f"✓ 用户数量: {len(users) if users else 0}")
            print(f"✓ 店铺数量: {len(stores) if stores else 0}")
            
            print("✓ 数据库系统测试通过")
            return True
        else:
            print("✗ 数据库初始化失败")
            return False
            
    except Exception as e:
        print(f"✗ 数据库系统测试失败: {str(e)}")
        return False

def test_search_system():
    """测试搜索系统"""
    print("=" * 50)
    print("测试搜索系统...")
    
    try:
        from src.pack.backend.search.search_service import SearchService
        from src.pack.backend.search.bm25_searcher import BM25Searcher
        
        # 测试搜索服务
        search_service = SearchService()
        print("✓ 搜索服务初始化成功")
        
        # 测试BM25搜索器
        bm25_searcher = BM25Searcher()
        print("✓ BM25搜索器初始化成功")
        
        # 测试基本搜索功能
        test_docs = ["这是一个测试文档", "另一个测试文档", "第三个文档"]
        bm25_searcher.build_index(test_docs)
        results = bm25_searcher.search("测试", top_k=2)
        
        print(f"✓ BM25搜索测试完成，找到 {len(results)} 个结果")
        print("✓ 搜索系统测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 搜索系统测试失败: {str(e)}")
        return False

def test_knowledge_system():
    """测试知识管理系统"""
    print("=" * 50)
    print("测试知识管理系统...")
    
    try:
        from src.pack.backend.knowledge.knowledge_search import KnowledgeSearcher
        from src.pack.backend.knowledge.document_processor import DocumentProcessor
        
        # 测试知识搜索器
        knowledge_searcher = KnowledgeSearcher()
        print("✓ 知识搜索器初始化成功")
        
        # 测试文档处理器
        doc_processor = DocumentProcessor()
        supported_formats = doc_processor.get_supported_formats()
        print(f"✓ 文档处理器支持格式: {supported_formats}")
        
        print("✓ 知识管理系统测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 知识管理系统测试失败: {str(e)}")
        return False

def test_chat_system():
    """测试聊天系统"""
    print("=" * 50)
    print("测试聊天系统...")
    
    try:
        from src.pack.backend.chat.message_handler import MessageHandler
        from src.pack.backend.chat.whatsapp_service import WhatsAppService
        
        # 测试消息处理器
        message_handler = MessageHandler("test_store", "test_user")
        print("✓ 消息处理器初始化成功")
        
        # 测试WhatsApp服务
        whatsapp_service = WhatsAppService("test_store", "test_user")
        status = whatsapp_service.get_client_status()
        print(f"✓ WhatsApp服务状态: {status}")
        
        print("✓ 聊天系统测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 聊天系统测试失败: {str(e)}")
        return False

def test_bulk_acquisition_system():
    """测试群发获客系统"""
    print("=" * 50)
    print("测试群发获客系统...")
    
    try:
        from src.pack.backend.bulk_acquisition.acquisition_service import AcquisitionService
        from src.pack.backend.bulk_acquisition.customer_manager import CustomerManager
        
        # 测试获客服务
        acquisition_service = AcquisitionService()
        stats = acquisition_service.get_statistics()
        print(f"✓ 获客服务统计: {stats}")
        
        # 测试客户管理器
        customer_manager = CustomerManager()
        customer_stats = customer_manager.get_statistics()
        print(f"✓ 客户管理器统计: {customer_stats}")
        
        print("✓ 群发获客系统测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 群发获客系统测试失败: {str(e)}")
        return False

def test_service_management():
    """测试服务管理系统"""
    print("=" * 50)
    print("测试服务管理系统...")
    
    try:
        from src.pack.backend.services.service_manager import ServiceManager
        from src.pack.backend.services.task_scheduler import TaskScheduler
        
        # 测试服务管理器
        service_manager = ServiceManager()
        health = service_manager.health_check()
        print(f"✓ 服务管理器健康检查: {health}")
        
        # 测试任务调度器
        task_scheduler = TaskScheduler()
        stats = task_scheduler.get_statistics()
        print(f"✓ 任务调度器统计: {stats}")
        
        print("✓ 服务管理系统测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 服务管理系统测试失败: {str(e)}")
        return False

def test_frontend_imports():
    """测试前端页面导入"""
    print("=" * 50)
    print("测试前端页面导入...")
    
    try:
        # 测试页面导入
        from src.pack.frontend.gui.uis.pages.page_login import create_login_page
        from src.pack.frontend.gui.uis.pages.page_chatmax import ChatPage
        from src.pack.frontend.gui.uis.pages.page_knowledge import create_knowledge_page
        from src.pack.frontend.gui.uis.pages.page_bulk_acquisition import create_bulk_acquisition_page
        from src.pack.frontend.gui.uis.pages.page_settings import create_settings_page
        
        print("✓ 登录页面导入成功")
        print("✓ 聊天页面导入成功")
        print("✓ 知识管理页面导入成功")
        print("✓ 群发获客页面导入成功")
        print("✓ 设置页面导入成功")
        
        print("✓ 前端页面导入测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 前端页面导入测试失败: {str(e)}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("开始运行模块功能测试...")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("配置系统", test_config_system()))
    test_results.append(("数据库系统", test_database_system()))
    test_results.append(("搜索系统", test_search_system()))
    test_results.append(("知识管理系统", test_knowledge_system()))
    test_results.append(("聊天系统", test_chat_system()))
    test_results.append(("群发获客系统", test_bulk_acquisition_system()))
    test_results.append(("服务管理系统", test_service_management()))
    test_results.append(("前端页面导入", test_frontend_imports()))
    
    # 汇总结果
    print("=" * 50)
    print("测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print("=" * 50)
    print(f"总计: {len(test_results)} 项测试")
    print(f"通过: {passed} 项")
    print(f"失败: {failed} 项")
    print(f"成功率: {(passed/len(test_results)*100):.1f}%")
    
    if failed == 0:
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️  有测试失败，请检查相关模块")
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
