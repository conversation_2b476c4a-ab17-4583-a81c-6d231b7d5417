"""
混合搜索器
结合向量搜索和BM25搜索的混合搜索功能
"""

import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.settings_loader import get_setting
    from .bm25_searcher import BM25Searcher
    from ..knowledge.knowledge_search import KnowledgeSearcher
    from ..knowledge.vector_builder import VectorBuilder
except ImportError as e:
    print(f"导入模块失败: {e}")
    def get_setting(key, default=None): return default
    class BM25Searcher:
        def __init__(self): pass
        def load_from_vector_db(self, path): return False
        def search(self, query, top_k=5): return []
    class KnowledgeSearcher:
        def __init__(self): pass
        def search_vector_only(self, query, kb_type="core"): return []
    class VectorBuilder:
        def __init__(self): pass

# 配置日志
logger = logging.getLogger(__name__)

class HybridSearcher:
    """混合搜索器类"""
    
    def __init__(self):
        """初始化混合搜索器"""
        # 初始化各个搜索器
        self.bm25_searcher = BM25Searcher()
        self.knowledge_searcher = KnowledgeSearcher()
        self.vector_builder = VectorBuilder()
        
        # 搜索配置
        self.vector_weight = get_setting("search.hybrid.vector_weight", 0.6)
        self.bm25_weight = get_setting("search.hybrid.bm25_weight", 0.4)
        self.fusion_method = get_setting("search.hybrid.fusion_method", "weighted_sum")
        
        logger.info("混合搜索器已初始化")
    
    def search(self, query: str, kb_type: str = "core", top_k: int = 5, 
               fusion_method: str = None) -> List[Dict]:
        """
        执行混合搜索
        
        Args:
            query: 查询文本
            kb_type: 知识库类型，"core" 或 "help"
            top_k: 返回结果数量
            fusion_method: 融合方法
            
        Returns:
            List[Dict]: 搜索结果列表
        """
        if fusion_method is None:
            fusion_method = self.fusion_method
        
        try:
            logger.info(f"开始混合搜索，查询: '{query}', 知识库: {kb_type}")
            
            # 1. 向量搜索
            vector_results = self._vector_search(query, kb_type, top_k)
            logger.info(f"向量搜索完成，找到 {len(vector_results)} 个结果")
            
            # 2. BM25搜索
            bm25_results = self._bm25_search(query, kb_type, top_k)
            logger.info(f"BM25搜索完成，找到 {len(bm25_results)} 个结果")
            
            # 3. 融合结果
            fused_results = self._fuse_results(
                vector_results, bm25_results, fusion_method, top_k
            )
            logger.info(f"结果融合完成，最终 {len(fused_results)} 个结果")
            
            return fused_results
            
        except Exception as e:
            logger.error(f"混合搜索时出错: {str(e)}")
            return []
    
    def _vector_search(self, query: str, kb_type: str, top_k: int) -> List[Dict]:
        """
        执行向量搜索
        
        Args:
            query: 查询文本
            kb_type: 知识库类型
            top_k: 返回结果数量
            
        Returns:
            List[Dict]: 向量搜索结果
        """
        try:
            results = self.knowledge_searcher.search_vector_only(query, kb_type)
            
            # 标记搜索方法
            for result in results:
                result['search_method'] = 'vector'
            
            return results[:top_k]
            
        except Exception as e:
            logger.error(f"向量搜索时出错: {str(e)}")
            return []
    
    def _bm25_search(self, query: str, kb_type: str, top_k: int) -> List[Dict]:
        """
        执行BM25搜索
        
        Args:
            query: 查询文本
            kb_type: 知识库类型
            top_k: 返回结果数量
            
        Returns:
            List[Dict]: BM25搜索结果
        """
        try:
            # 根据知识库类型选择向量数据库路径
            if kb_type == "core":
                vector_path = get_setting("vector_path_core", "data/vector/core")
            else:
                vector_path = get_setting("vector_path_help", "data/vector/help")
            
            # 加载文档到BM25搜索器
            if not self.bm25_searcher.load_from_vector_db(vector_path):
                logger.warning(f"无法从 {vector_path} 加载文档到BM25搜索器")
                return []
            
            # 执行搜索
            results = self.bm25_searcher.search(query, top_k)
            
            return results
            
        except Exception as e:
            logger.error(f"BM25搜索时出错: {str(e)}")
            return []
    
    def _fuse_results(self, vector_results: List[Dict], bm25_results: List[Dict], 
                     fusion_method: str, top_k: int) -> List[Dict]:
        """
        融合搜索结果
        
        Args:
            vector_results: 向量搜索结果
            bm25_results: BM25搜索结果
            fusion_method: 融合方法
            top_k: 返回结果数量
            
        Returns:
            List[Dict]: 融合后的结果
        """
        try:
            if fusion_method == "weighted_sum":
                return self._weighted_sum_fusion(vector_results, bm25_results, top_k)
            elif fusion_method == "reciprocal_rank":
                return self._reciprocal_rank_fusion(vector_results, bm25_results, top_k)
            elif fusion_method == "simple_concat":
                return self._simple_concat_fusion(vector_results, bm25_results, top_k)
            else:
                logger.warning(f"未知的融合方法: {fusion_method}，使用加权求和")
                return self._weighted_sum_fusion(vector_results, bm25_results, top_k)
                
        except Exception as e:
            logger.error(f"融合结果时出错: {str(e)}")
            # 出错时返回向量搜索结果
            return vector_results[:top_k]
    
    def _weighted_sum_fusion(self, vector_results: List[Dict], bm25_results: List[Dict], 
                           top_k: int) -> List[Dict]:
        """
        加权求和融合
        
        Args:
            vector_results: 向量搜索结果
            bm25_results: BM25搜索结果
            top_k: 返回结果数量
            
        Returns:
            List[Dict]: 融合后的结果
        """
        # 创建文档ID到结果的映射
        doc_scores = {}
        
        # 处理向量搜索结果
        for result in vector_results:
            doc_id = self._get_doc_id(result)
            normalized_score = self._normalize_score(result.get('score', 0), 'vector')
            doc_scores[doc_id] = {
                'vector_score': normalized_score,
                'bm25_score': 0,
                'result': result
            }
        
        # 处理BM25搜索结果
        for result in bm25_results:
            doc_id = self._get_doc_id(result)
            normalized_score = self._normalize_score(result.get('score', 0), 'bm25')
            
            if doc_id in doc_scores:
                doc_scores[doc_id]['bm25_score'] = normalized_score
            else:
                doc_scores[doc_id] = {
                    'vector_score': 0,
                    'bm25_score': normalized_score,
                    'result': result
                }
        
        # 计算融合得分
        fused_results = []
        for doc_id, scores in doc_scores.items():
            fused_score = (scores['vector_score'] * self.vector_weight + 
                          scores['bm25_score'] * self.bm25_weight)
            
            result = scores['result'].copy()
            result['fused_score'] = fused_score
            result['vector_score'] = scores['vector_score']
            result['bm25_score'] = scores['bm25_score']
            result['search_method'] = 'hybrid'
            
            fused_results.append(result)
        
        # 按融合得分排序
        fused_results.sort(key=lambda x: x['fused_score'], reverse=True)
        
        return fused_results[:top_k]
    
    def _reciprocal_rank_fusion(self, vector_results: List[Dict], bm25_results: List[Dict], 
                              top_k: int) -> List[Dict]:
        """
        倒数排名融合 (Reciprocal Rank Fusion)
        
        Args:
            vector_results: 向量搜索结果
            bm25_results: BM25搜索结果
            top_k: 返回结果数量
            
        Returns:
            List[Dict]: 融合后的结果
        """
        k = 60  # RRF参数
        doc_scores = {}
        
        # 处理向量搜索结果
        for rank, result in enumerate(vector_results):
            doc_id = self._get_doc_id(result)
            rrf_score = 1 / (k + rank + 1)
            doc_scores[doc_id] = {
                'rrf_score': rrf_score,
                'result': result
            }
        
        # 处理BM25搜索结果
        for rank, result in enumerate(bm25_results):
            doc_id = self._get_doc_id(result)
            rrf_score = 1 / (k + rank + 1)
            
            if doc_id in doc_scores:
                doc_scores[doc_id]['rrf_score'] += rrf_score
            else:
                doc_scores[doc_id] = {
                    'rrf_score': rrf_score,
                    'result': result
                }
        
        # 构建结果
        fused_results = []
        for doc_id, scores in doc_scores.items():
            result = scores['result'].copy()
            result['rrf_score'] = scores['rrf_score']
            result['search_method'] = 'hybrid_rrf'
            fused_results.append(result)
        
        # 按RRF得分排序
        fused_results.sort(key=lambda x: x['rrf_score'], reverse=True)
        
        return fused_results[:top_k]
    
    def _simple_concat_fusion(self, vector_results: List[Dict], bm25_results: List[Dict], 
                            top_k: int) -> List[Dict]:
        """
        简单拼接融合
        
        Args:
            vector_results: 向量搜索结果
            bm25_results: BM25搜索结果
            top_k: 返回结果数量
            
        Returns:
            List[Dict]: 融合后的结果
        """
        # 简单地将两个结果列表拼接
        all_results = []
        
        # 添加向量搜索结果
        for result in vector_results:
            result = result.copy()
            result['search_method'] = 'vector'
            all_results.append(result)
        
        # 添加BM25搜索结果（去重）
        seen_docs = set(self._get_doc_id(r) for r in vector_results)
        for result in bm25_results:
            doc_id = self._get_doc_id(result)
            if doc_id not in seen_docs:
                result = result.copy()
                result['search_method'] = 'bm25'
                all_results.append(result)
                seen_docs.add(doc_id)
        
        return all_results[:top_k]
    
    def _get_doc_id(self, result: Dict) -> str:
        """
        获取文档唯一标识
        
        Args:
            result: 搜索结果
            
        Returns:
            str: 文档ID
        """
        # 使用内容的前100个字符作为文档ID
        content = result.get('content', result.get('content_snippet', ''))
        return content[:100] if content else str(hash(str(result)))
    
    def _normalize_score(self, score: float, search_type: str) -> float:
        """
        归一化得分
        
        Args:
            score: 原始得分
            search_type: 搜索类型
            
        Returns:
            float: 归一化后的得分
        """
        if search_type == 'vector':
            # 向量搜索得分通常在0-1之间
            return max(0, min(1, score))
        elif search_type == 'bm25':
            # BM25得分需要归一化到0-1之间
            # 使用sigmoid函数进行归一化
            import math
            return 1 / (1 + math.exp(-score / 10))
        else:
            return score
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取混合搜索器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            'fusion_method': self.fusion_method,
            'weights': {
                'vector': self.vector_weight,
                'bm25': self.bm25_weight
            },
            'bm25_stats': self.bm25_searcher.get_statistics()
        }
