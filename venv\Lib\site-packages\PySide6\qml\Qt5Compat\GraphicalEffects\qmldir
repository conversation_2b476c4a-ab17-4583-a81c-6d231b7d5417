module Qt5Compat.GraphicalEffects
linktarget Qt6::qtgraphicaleffectsplugin
plugin qtgraphicaleffectsplugin
classname QtGraphicalEffectsPlugin
designersupported
typeinfo plugins.qmltypes
depends Qt5Compat.GraphicalEffects.private
depends QtQuick.Window
depends QtQuick
prefer :/qt-project.org/imports/Qt5Compat/GraphicalEffects/
Blend 6.0 Blend.qml
Blend 1.0 Blend.qml
BrightnessContrast 6.0 BrightnessContrast.qml
BrightnessContrast 1.0 BrightnessContrast.qml
ColorOverlay 6.0 ColorOverlay.qml
ColorOverlay 1.0 ColorOverlay.qml
Colorize 6.0 Colorize.qml
Colorize 1.0 Colorize.qml
ConicalGradient 6.0 ConicalGradient.qml
ConicalGradient 1.0 ConicalGradient.qml
Desaturate 6.0 Desaturate.qml
Desaturate 1.0 Desaturate.qml
DirectionalBlur 6.0 DirectionalBlur.qml
DirectionalBlur 1.0 DirectionalBlur.qml
Displace 6.0 Displace.qml
Displace 1.0 Displace.qml
DropShadow 6.0 DropShadow.qml
DropShadow 1.0 DropShadow.qml
FastBlur 6.0 FastBlur.qml
FastBlur 1.0 FastBlur.qml
GammaAdjust 6.0 GammaAdjust.qml
GammaAdjust 1.0 GammaAdjust.qml
GaussianBlur 6.0 GaussianBlur.qml
GaussianBlur 1.0 GaussianBlur.qml
Glow 6.0 Glow.qml
Glow 1.0 Glow.qml
HueSaturation 6.0 HueSaturation.qml
HueSaturation 1.0 HueSaturation.qml
InnerShadow 6.0 InnerShadow.qml
InnerShadow 1.0 InnerShadow.qml
LevelAdjust 6.0 LevelAdjust.qml
LevelAdjust 1.0 LevelAdjust.qml
LinearGradient 6.0 LinearGradient.qml
LinearGradient 1.0 LinearGradient.qml
MaskedBlur 6.0 MaskedBlur.qml
MaskedBlur 1.0 MaskedBlur.qml
OpacityMask 6.0 OpacityMask.qml
OpacityMask 1.0 OpacityMask.qml
RadialBlur 6.0 RadialBlur.qml
RadialBlur 1.0 RadialBlur.qml
RadialGradient 6.0 RadialGradient.qml
RadialGradient 1.0 RadialGradient.qml
RectangularGlow 6.0 RectangularGlow.qml
RectangularGlow 1.0 RectangularGlow.qml
RecursiveBlur 6.0 RecursiveBlur.qml
RecursiveBlur 1.0 RecursiveBlur.qml
ThresholdMask 6.0 ThresholdMask.qml
ThresholdMask 1.0 ThresholdMask.qml
ZoomBlur 6.0 ZoomBlur.qml
ZoomBlur 1.0 ZoomBlur.qml

