"""
ZWOW 应用程序安装脚本
用于创建可执行文件和安装包
"""

from setuptools import setup, find_packages
import os
import sys
from pathlib import Path

# 获取项目根目录
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 读取版本信息
try:
    from src.pack.config.settings_loader import get_setting
    version = get_setting("version", "1.0.0")
except:
    version = "1.0.0"

# 读取README
readme_path = project_root / "README.md"
long_description = ""
if readme_path.exists():
    with open(readme_path, "r", encoding="utf-8") as f:
        long_description = f.read()

# 读取依赖
requirements_path = project_root / "requirements.txt"
requirements = []
if requirements_path.exists():
    with open(requirements_path, "r", encoding="utf-8") as f:
        requirements = [
            line.strip() for line in f 
            if line.strip() and not line.startswith("#")
        ]

setup(
    name="zwow",
    version=version,
    author="WOWCKER Team",
    author_email="<EMAIL>",
    description="智能客服和获客系统",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/wowcker/zwow",
    packages=find_packages(where=str(project_root)),
    package_dir={"": str(project_root)},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: Microsoft :: Windows",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Communications :: Chat",
        "Topic :: Office/Business :: Financial :: Point-Of-Sale",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-qt>=4.2.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
        ],
        "optional": [
            "faiss-cpu>=1.7.4",
            "PyYAML>=6.0",
            "colorlog>=6.7.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "zwow=src.pack.frontend.main:main",
            "zwow-test=run_tests:main",
        ],
    },
    include_package_data=True,
    package_data={
        "src.pack.frontend.gui": ["**/*.qss", "**/*.svg", "**/*.png", "**/*.ico"],
        "src.pack.config": ["*.json"],
    },
    data_files=[
        ("", ["README.md", "requirements.txt"]),
        ("deploy", ["deploy/build.py", "deploy/installer.nsi"]),
    ],
    zip_safe=False,
)
