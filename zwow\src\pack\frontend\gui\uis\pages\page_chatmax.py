# ///////////////////////////////////////////////////////////////
#
# 作者: WANDERSON M.PIMENTA
# 项目基于: Qt Designer 和 PySide6
# 版本: 1.0.0
#
# 本项目可以免费用于任何用途，但必须在Python脚本中保留相应的版权信息。
# 可以自由修改可视化界面(GUI)中的任何信息，不会有任何影响。
#
# 如果您想将产品商业化，Qt许可证有一些限制，
# 建议您阅读官方网站的相关内容：
# https://doc.qt.io/qtforpython/licenses.html
#
# ///////////////////////////////////////////////////////////////

# 导入包和模块
# ///////////////////////////////////////////////////////////////
from qt_core import *
from gui.widgets import *
from gui.core.json_themes import Themes
from gui.core.functions import Functions
import datetime
import json
import sys
import threading
from pathlib import Path

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入聊天服务
try:
    from src.pack.backend.chat.whatsapp_service import WhatsAppService
    from src.pack.backend.chat.message_handler import MessageHandler
    from src.pack.common_utils.database.app_db_manager import AppDBManager
except ImportError as e:
    print(f"导入聊天服务失败: {e}")
    # 创建模拟类
    class WhatsAppService:
        def __init__(self, store_name, username=None):
            self.is_running = False
        def start(self): return True
        def stop(self): return True
        def get_client_status(self): return {"is_running": False}
    class MessageHandler:
        def __init__(self, store_name, username=None): pass
    class AppDBManager:
        def __init__(self): pass
        def initialize(self): return True

# 聊天消息类
# ///////////////////////////////////////////////////////////////
class ChatMessage:
    def __init__(self, text: str, is_self: bool = True, timestamp: str = None):
        self.text = text
        self.is_self = is_self
        self.timestamp = timestamp or datetime.datetime.now().strftime("%H:%M")
        self.message_id = f"msg_{datetime.datetime.now().timestamp()}"

# 聊天消息气泡控件
# ///////////////////////////////////////////////////////////////
class ChatBubble(QWidget):
    def __init__(self, message: ChatMessage, parent=None):
        super().__init__(parent)
        self.message = message
        self.setup_ui()
        
    def setup_ui(self):
        # 加载主题
        themes = Themes()
        self.themes = themes.items
        
        # 主布局
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(10, 5, 10, 5)
        self.main_layout.setSpacing(8)
        
        # 消息容器
        self.message_container = QFrame()
        self.message_container.setMaximumWidth(400)
        self.message_container.setMinimumWidth(100)
        
        # 消息布局
        self.message_layout = QVBoxLayout(self.message_container)
        self.message_layout.setContentsMargins(12, 8, 12, 8)
        self.message_layout.setSpacing(4)
        
        # 消息文本
        self.message_label = QLabel(self.message.text)
        self.message_label.setWordWrap(True)
        self.message_label.setTextInteractionFlags(Qt.TextSelectableByMouse)
        
        # 时间标签
        self.time_label = QLabel(self.message.timestamp)
        self.time_label.setAlignment(Qt.AlignRight)
        
        # 添加到布局
        self.message_layout.addWidget(self.message_label)
        self.message_layout.addWidget(self.time_label)
        
        # 设置样式
        if self.message.is_self:
            # 自己的消息 - 右对齐，绿色背景
            self.main_layout.addStretch()
            self.main_layout.addWidget(self.message_container)
            self.message_container.setStyleSheet(f"""
                QFrame {{
                    background-color: {self.themes['app_color']['green']};
                    border-radius: 12px;
                    border-top-right-radius: 4px;
                }}
            """)
            self.message_label.setStyleSheet(f"""
                QLabel {{
                    color: {self.themes['app_color']['dark_one']};
                    font-size: 14px;
                    font-weight: 500;
                }}
            """)
            self.time_label.setStyleSheet(f"""
                QLabel {{
                    color: {self.themes['app_color']['dark_one']};
                    font-size: 11px;
                    opacity: 0.7;
                }}
            """)
        else:
            # 对方的消息 - 左对齐，灰色背景
            self.main_layout.addWidget(self.message_container)
            self.main_layout.addStretch()
            self.message_container.setStyleSheet(f"""
                QFrame {{
                    background-color: {self.themes['app_color']['bg_three']};
                    border-radius: 12px;
                    border-top-left-radius: 4px;
                }}
            """)
            self.message_label.setStyleSheet(f"""
                QLabel {{
                    color: {self.themes['app_color']['text_foreground']};
                    font-size: 14px;
                    font-weight: 500;
                }}
            """)
            self.time_label.setStyleSheet(f"""
                QLabel {{
                    color: {self.themes['app_color']['text_description']};
                    font-size: 11px;
                    opacity: 0.7;
                }}
            """)

# 联系人类
# ///////////////////////////////////////////////////////////////
class Contact:
    def __init__(self, name: str, avatar: str = None, status: str = "离线", last_message: str = "", unread_count: int = 0):
        self.name = name
        self.avatar = avatar
        self.status = status
        self.last_message = last_message
        self.unread_count = unread_count
        self.messages = []  # 存储与该联系人的聊天记录

# 联系人卡片控件
# ///////////////////////////////////////////////////////////////
class ContactCard(QFrame):
    clicked = Signal(Contact)  # 定义点击信号
    
    def __init__(self, contact: Contact, parent=None):
        super().__init__(parent)
        self.contact = contact
        self.setup_ui()
        
    def setup_ui(self):
        # 加载主题
        themes = Themes()
        self.themes = themes.items
        
        # 设置固定高度
        self.setFixedHeight(70)
        
        # 设置鼠标样式
        self.setCursor(Qt.PointingHandCursor)
        
        # 主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(10)
        
        # 头像
        self.avatar_label = QLabel()
        self.avatar_label.setFixedSize(50, 50)
        self.avatar_label.setStyleSheet(f"""
            QLabel {{
                background-color: {self.themes['app_color']['context_color']};
                border-radius: 25px;
                border: 2px solid {self.themes['app_color']['bg_three']};
            }}
        """)
        
        # 信息容器
        info_container = QVBoxLayout()
        info_container.setSpacing(2)
        
        # 名称和未读消息数
        name_layout = QHBoxLayout()
        self.name_label = QLabel(self.contact.name)
        self.name_label.setStyleSheet(f"""
            QLabel {{
                color: {self.themes['app_color']['text_foreground']};
                font-size: 14px;
                font-weight: bold;
            }}
        """)
        
        self.unread_label = QLabel(str(self.contact.unread_count) if self.contact.unread_count > 0 else "")
        self.unread_label.setAlignment(Qt.AlignCenter)
        self.unread_label.setStyleSheet(f"""
            QLabel {{
                background-color: {self.themes['app_color']['context_color']};
                color: white;
                border-radius: 10px;
                min-width: 20px;
                max-width: 20px;
                min-height: 20px;
                max-height: 20px;
                font-size: 11px;
            }}
        """)
        self.unread_label.setVisible(self.contact.unread_count > 0)
        
        name_layout.addWidget(self.name_label)
        name_layout.addStretch()
        name_layout.addWidget(self.unread_label)
        
        # 最后一条消息和状态
        self.last_message_label = QLabel(self.contact.last_message)
        self.last_message_label.setStyleSheet(f"""
            QLabel {{
                color: {self.themes['app_color']['text_description']};
                font-size: 12px;
            }}
        """)
        
        info_container.addLayout(name_layout)
        info_container.addWidget(self.last_message_label)
        
        layout.addWidget(self.avatar_label)
        layout.addLayout(info_container)
        
        # 设置样式
        self.setStyleSheet(f"""
            ContactCard {{
                background-color: {self.themes['app_color']['bg_two']};
                border-radius: 10px;
                margin: 2px 5px;
            }}
            ContactCard:hover {{
                background-color: {self.themes['app_color']['bg_three']};
            }}
        """)
        
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.clicked.emit(self.contact)
        super().mousePressEvent(event)

# 聊天页面主类
# ///////////////////////////////////////////////////////////////
class ChatPage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.contacts = []  # 存储联系人列表
        self.current_contact = None  # 当前选中的联系人

        # WhatsApp服务相关
        self.whatsapp_service = None
        self.current_store = "default_store"
        self.current_user = "default_user"
        self.service_status_timer = QTimer()
        self.service_status_timer.timeout.connect(self.update_service_status)

        self.setup_ui()
        self.load_sample_contacts()  # 加载示例联系人
        self.setup_whatsapp_service()  # 设置WhatsApp服务
        
    def setup_ui(self):
        # 加载主题
        themes = Themes()
        self.themes = themes.items
        
        # 主布局 - 水平分割
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 设置左侧联系人列表
        self.setup_contact_list()
        
        # 设置右侧聊天区域
        self.setup_chat_widget()
        
        # 设置整体样式
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {self.themes['app_color']['bg_one']};
                color: {self.themes['app_color']['text_foreground']};
            }}
        """)
        
    def setup_contact_list(self):
        """设置联系人列表区域"""
        # 联系人列表容器
        self.contact_container = QFrame()
        self.contact_container.setFixedWidth(300)
        self.contact_container.setStyleSheet(f"""
            QFrame {{
                background-color: {self.themes['app_color']['bg_two']};
                border-right: 1px solid {self.themes['app_color']['dark_three']};
            }}
        """)
        
        contact_layout = QVBoxLayout(self.contact_container)
        contact_layout.setContentsMargins(0, 0, 0, 0)
        contact_layout.setSpacing(0)
        
        # 搜索框
        self.search_input = PyLineEdit(
            place_holder_text="搜索联系人...",
            radius=8,
            border_size=2,
            color=self.themes['app_color']['text_foreground'],
            selection_color=self.themes['app_color']['text_foreground'],
            bg_color=self.themes['app_color']['bg_one'],
            bg_color_active=self.themes['app_color']['bg_three'],
            context_color=self.themes['app_color']['context_color']
        )
        self.search_input.setFixedHeight(40)
        self.search_input.textChanged.connect(self.filter_contacts)
        
        search_container = QFrame()
        search_container.setFixedHeight(60)
        search_layout = QVBoxLayout(search_container)
        search_layout.setContentsMargins(15, 10, 15, 10)
        search_layout.addWidget(self.search_input)
        
        # 联系人列表滚动区域
        self.contact_scroll = QScrollArea()
        self.contact_scroll.setWidgetResizable(True)
        self.contact_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.contact_scroll.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollBar:vertical {{
                background-color: {self.themes['app_color']['bg_two']};
                width: 8px;
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {self.themes['app_color']['bg_three']};
                border-radius: 4px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {self.themes['app_color']['context_color']};
            }}
        """)
        
        # 联系人列表内容
        self.contact_list_widget = QWidget()
        self.contact_list_layout = QVBoxLayout(self.contact_list_widget)
        self.contact_list_layout.setContentsMargins(5, 5, 5, 5)
        self.contact_list_layout.setSpacing(5)
        self.contact_list_layout.addStretch()
        
        self.contact_scroll.setWidget(self.contact_list_widget)
        
        contact_layout.addWidget(search_container)
        contact_layout.addWidget(self.contact_scroll)
        
        self.main_layout.addWidget(self.contact_container)
        
    def setup_chat_widget(self):
        """设置右侧聊天区域"""
        self.chat_widget = QWidget()
        self.chat_layout = QVBoxLayout(self.chat_widget)
        self.chat_layout.setContentsMargins(0, 0, 0, 0)
        self.chat_layout.setSpacing(0)
        
        # 聊天标题栏
        self.setup_title_bar()
        
        # 聊天记录区域
        self.setup_chat_area()
        
        # 输入区域
        self.setup_input_area()
        
        self.main_layout.addWidget(self.chat_widget)
        
    def setup_title_bar(self):
        """设置聊天标题栏"""
        self.title_bar = QFrame()
        self.title_bar.setFixedHeight(60)
        self.title_bar.setStyleSheet(f"""
            QFrame {{
                background-color: {self.themes['app_color']['bg_two']};
                border-bottom: 1px solid {self.themes['app_color']['dark_three']};
            }}
        """)
        
        title_layout = QHBoxLayout(self.title_bar)
        title_layout.setContentsMargins(20, 0, 20, 0)
        
        # 头像
        self.avatar_label = QLabel()
        self.avatar_label.setFixedSize(40, 40)
        self.avatar_label.setStyleSheet(f"""
            QLabel {{
                background-color: {self.themes['app_color']['context_color']};
                border-radius: 20px;
                border: 2px solid {self.themes['app_color']['bg_three']};
            }}
        """)
        
        # 用户信息
        user_info_layout = QVBoxLayout()
        user_info_layout.setSpacing(2)
        
        self.username_label = QLabel("聊天助手")
        self.username_label.setStyleSheet(f"""
            QLabel {{
                color: {self.themes['app_color']['text_foreground']};
                font-size: 16px;
                font-weight: bold;
            }}
        """)
        
        self.status_label = QLabel("在线")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {self.themes['app_color']['green']};
                font-size: 12px;
            }}
        """)
        
        user_info_layout.addWidget(self.username_label)
        user_info_layout.addWidget(self.status_label)
        
        title_layout.addWidget(self.avatar_label)
        title_layout.addLayout(user_info_layout)
        title_layout.addStretch()

        # WhatsApp服务控制按钮
        self.start_service_btn = PyPushButton(
            text="启动服务",
            radius=6,
            color=self.themes['app_color']['white'],
            bg_color=self.themes['app_color']['green'],
            bg_color_hover=self.themes['app_color']['context_hover'],
            bg_color_pressed=self.themes['app_color']['context_pressed'],
            parent=self.title_bar
        )
        self.start_service_btn.setFixedSize(80, 30)
        self.start_service_btn.clicked.connect(self.start_whatsapp_service)

        self.stop_service_btn = PyPushButton(
            text="停止服务",
            radius=6,
            color=self.themes['app_color']['white'],
            bg_color=self.themes['app_color']['red'],
            bg_color_hover=self.themes['app_color']['context_hover'],
            bg_color_pressed=self.themes['app_color']['context_pressed'],
            parent=self.title_bar
        )
        self.stop_service_btn.setFixedSize(80, 30)
        self.stop_service_btn.clicked.connect(self.stop_whatsapp_service)

        title_layout.addWidget(self.start_service_btn)
        title_layout.addWidget(self.stop_service_btn)

        # 功能按钮
        self.video_call_btn = PyIconButton(
            icon_path=Functions.set_svg_icon("icon_video_call.svg"),
            icon_color=self.themes['app_color']['icon_color'],
            icon_color_hover=self.themes['app_color']['icon_hover'],
            icon_color_pressed=self.themes['app_color']['icon_pressed'],
            bg_color="transparent",
            bg_color_hover=self.themes['app_color']['bg_three'],
            bg_color_pressed=self.themes['app_color']['bg_one'],
            parent=self.title_bar
        )
        self.video_call_btn.setFixedSize(40, 40)
        
        self.voice_call_btn = PyIconButton(
            icon_path=Functions.set_svg_icon("icon_voice_call.svg"),
            icon_color=self.themes['app_color']['icon_color'],
            icon_color_hover=self.themes['app_color']['icon_hover'],
            icon_color_pressed=self.themes['app_color']['icon_pressed'],
            bg_color="transparent",
            bg_color_hover=self.themes['app_color']['bg_three'],
            bg_color_pressed=self.themes['app_color']['bg_one'],
            parent=self.title_bar
        )
        self.voice_call_btn.setFixedSize(40, 40)
        
        self.more_btn = PyIconButton(
            icon_path=Functions.set_svg_icon("icon_more.svg"),
            icon_color=self.themes['app_color']['icon_color'],
            icon_color_hover=self.themes['app_color']['icon_hover'],
            icon_color_pressed=self.themes['app_color']['icon_pressed'],
            bg_color="transparent",
            bg_color_hover=self.themes['app_color']['bg_three'],
            bg_color_pressed=self.themes['app_color']['bg_one'],
            parent=self.title_bar
        )
        self.more_btn.setFixedSize(40, 40)
        
        title_layout.addWidget(self.video_call_btn)
        title_layout.addWidget(self.voice_call_btn)
        title_layout.addWidget(self.more_btn)
        
        self.chat_layout.addWidget(self.title_bar)
        
    def setup_chat_area(self):
        """设置聊天记录显示区域"""
        # 聊天区域容器
        self.chat_container = QFrame()
        self.chat_container.setStyleSheet(f"""
            QFrame {{
                background-color: {self.themes['app_color']['bg_one']};
                border: none;
            }}
        """)
        
        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollBar:vertical {{
                background-color: {self.themes['app_color']['bg_two']};
                width: 8px;
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical {{
                background-color: {self.themes['app_color']['bg_three']};
                border-radius: 4px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {self.themes['app_color']['context_color']};
            }}
        """)
        
        # 聊天内容容器
        self.chat_content = QWidget()
        self.chat_content.setStyleSheet(f"""
            QWidget {{
                background-color: {self.themes['app_color']['bg_one']};
            }}
        """)
        
        # 聊天消息布局
        self.chat_layout_messages = QVBoxLayout(self.chat_content)
        self.chat_layout_messages.setContentsMargins(10, 10, 10, 10)
        self.chat_layout_messages.setSpacing(8)
        self.chat_layout_messages.addStretch()  # 添加弹性空间，让消息从顶部开始
        
        self.scroll_area.setWidget(self.chat_content)
        
        # 添加到主布局
        chat_area_layout = QVBoxLayout(self.chat_container)
        chat_area_layout.setContentsMargins(0, 0, 0, 0)
        chat_area_layout.addWidget(self.scroll_area)
        
        self.chat_layout.addWidget(self.chat_container)
        
    def setup_input_area(self):
        """设置输入区域"""
        self.input_container = QFrame()
        self.input_container.setFixedHeight(120)
        self.input_container.setStyleSheet(f"""
            QFrame {{
                background-color: {self.themes['app_color']['bg_two']};
                border-top: 1px solid {self.themes['app_color']['dark_three']};
            }}
        """)
        
        input_layout = QVBoxLayout(self.input_container)
        input_layout.setContentsMargins(15, 10, 15, 10)
        input_layout.setSpacing(8)
        
        # 输入框
        self.message_input = PyLineEdit(
            place_holder_text="输入消息...",
            radius=8,
            border_size=2,
            color=self.themes['app_color']['text_foreground'],
            selection_color=self.themes['app_color']['text_foreground'],
            bg_color=self.themes['app_color']['bg_one'],
            bg_color_active=self.themes['app_color']['bg_three'],
            context_color=self.themes['app_color']['context_color']
        )
        self.message_input.setFixedHeight(40)
        self.message_input.returnPressed.connect(self.send_message)
        
        # 底部按钮区域
        bottom_layout = QHBoxLayout()
        bottom_layout.setContentsMargins(0, 0, 0, 0)
        bottom_layout.setSpacing(10)
        
        # 功能按钮
        self.emoji_btn = PyIconButton(
            icon_path=Functions.set_svg_icon("icon_emoji.svg"),
            icon_color=self.themes['app_color']['icon_color'],
            icon_color_hover=self.themes['app_color']['icon_hover'],
            icon_color_pressed=self.themes['app_color']['icon_pressed'],
            bg_color="transparent",
            bg_color_hover=self.themes['app_color']['bg_three'],
            bg_color_pressed=self.themes['app_color']['bg_one'],
            parent=self.input_container
        )
        self.emoji_btn.setFixedSize(32, 32)
        self.emoji_btn.clicked.connect(self.show_emoji_panel)
        
        self.file_btn = PyIconButton(
            icon_path=Functions.set_svg_icon("icon_file.svg"),
            icon_color=self.themes['app_color']['icon_color'],
            icon_color_hover=self.themes['app_color']['icon_hover'],
            icon_color_pressed=self.themes['app_color']['icon_pressed'],
            bg_color="transparent",
            bg_color_hover=self.themes['app_color']['bg_three'],
            bg_color_pressed=self.themes['app_color']['bg_one'],
            parent=self.input_container
        )
        self.file_btn.setFixedSize(32, 32)
        self.file_btn.clicked.connect(self.send_file)
        
        self.image_btn = PyIconButton(
            icon_path=Functions.set_svg_icon("icon_image.svg"),
            icon_color=self.themes['app_color']['icon_color'],
            icon_color_hover=self.themes['app_color']['icon_hover'],
            icon_color_pressed=self.themes['app_color']['icon_pressed'],
            bg_color="transparent",
            bg_color_hover=self.themes['app_color']['bg_three'],
            bg_color_pressed=self.themes['app_color']['bg_one'],
            parent=self.input_container
        )
        self.image_btn.setFixedSize(32, 32)
        self.image_btn.clicked.connect(self.send_image)
        
        bottom_layout.addWidget(self.emoji_btn)
        bottom_layout.addWidget(self.file_btn)
        bottom_layout.addWidget(self.image_btn)
        bottom_layout.addStretch()
        
        # 发送按钮
        self.send_btn = PyPushButton(
            text="发送",
            radius=8,
            color=self.themes['app_color']['white'],
            bg_color=self.themes['app_color']['context_color'],
            bg_color_hover=self.themes['app_color']['context_hover'],
            bg_color_pressed=self.themes['app_color']['context_pressed'],
            parent=self.input_container
        )
        self.send_btn.setFixedSize(80, 32)
        self.send_btn.clicked.connect(self.send_message)
        
        bottom_layout.addWidget(self.send_btn)
        
        input_layout.addWidget(self.message_input)
        input_layout.addLayout(bottom_layout)
        
        self.chat_layout.addWidget(self.input_container)
        
    def load_sample_contacts(self):
        """加载示例联系人"""
        sample_contacts = [
            Contact("张三", status="在线", last_message="今天天气真好", unread_count=2),
            Contact("李四", status="离线", last_message="晚上一起吃饭吗？", unread_count=0),
            Contact("王五", status="在线", last_message="项目进展如何？", unread_count=5),
            Contact("赵六", status="忙碌", last_message="稍后回复你", unread_count=0),
        ]
        
        for contact in sample_contacts:
            self.add_contact(contact)
            
        # 默认选中第一个联系人
        if sample_contacts:
            self.select_contact(sample_contacts[0])
            
    def add_contact(self, contact: Contact):
        """添加联系人到列表"""
        self.contacts.append(contact)
        
        # 创建联系人卡片
        card = ContactCard(contact)
        card.clicked.connect(self.select_contact)
        
        # 添加到布局
        self.contact_list_layout.insertWidget(self.contact_list_layout.count() - 1, card)
        
    def select_contact(self, contact: Contact):
        """选择联系人"""
        self.current_contact = contact
        
        # 更新标题栏
        self.username_label.setText(contact.name)
        self.status_label.setText(contact.status)
        
        # 清空并加载聊天记录
        self.clear_chat_area()
        self.load_chat_history(contact)
        
    def clear_chat_area(self):
        """清空聊天区域"""
        while self.chat_layout_messages.count() > 1:  # 保留最后的弹性空间
            item = self.chat_layout_messages.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
                
    def load_chat_history(self, contact: Contact):
        """加载与联系人的聊天记录"""
        if not contact.messages:  # 如果没有聊天记录，加载一些示例消息
            contact.messages = [
                ChatMessage(f"你好，{contact.name}！", False, "09:30"),
                ChatMessage("你好！很高兴和你聊天", True, "09:31"),
            ]
        
        for message in contact.messages:
            self.add_message(message)
            
    def filter_contacts(self, text: str):
        """根据搜索文本过滤联系人"""
        text = text.lower()
        for i in range(self.contact_list_layout.count() - 1):  # -1 排除最后的弹性空间
            item = self.contact_list_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if isinstance(widget, ContactCard):
                    widget.setVisible(text in widget.contact.name.lower())
                    
    def load_sample_messages(self):
        """这个方法已不再使用，由load_chat_history替代"""
        pass
            
    def add_message(self, message: ChatMessage):
        """添加消息到聊天区域"""
        if hasattr(self, "messages"):  # 兼容旧代码
            self.messages.append(message)
        
        # 创建消息气泡
        bubble = ChatBubble(message)
        
        # 添加到布局
        self.chat_layout_messages.insertWidget(self.chat_layout_messages.count() - 1, bubble)
        
        # 滚动到底部
        QTimer.singleShot(100, self.scroll_to_bottom)
        
    def update_contact_card(self, contact: Contact):
        """更新联系人卡片显示"""
        for i in range(self.contact_list_layout.count() - 1):
            item = self.contact_list_layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                if isinstance(widget, ContactCard) and widget.contact == contact:
                    widget.last_message_label.setText(contact.last_message)
                    widget.unread_label.setText(str(contact.unread_count))
                    widget.unread_label.setVisible(contact.unread_count > 0)
                    break
                    
    def send_message(self):
        """发送消息"""
        if not self.current_contact:
            return
            
        text = self.message_input.text().strip()
        if text:
            # 创建新消息
            message = ChatMessage(text, True)
            self.add_message(message)
            
            # 添加到联系人的消息历史
            self.current_contact.messages.append(message)
            
            # 更新联系人卡片的最后一条消息
            self.current_contact.last_message = text
            self.update_contact_card(self.current_contact)
            
            # 清空输入框
            self.message_input.clear()
            
            # 模拟对方回复
            QTimer.singleShot(1000, self.simulate_reply)
            
    def simulate_reply(self):
        """模拟对方回复"""
        if not self.current_contact:
            return
            
        replies = [
            f"好的，{self.current_contact.name}明白了",
            "这个想法不错",
            "我们详细讨论一下",
            "稍后回复你",
            "好的，没问题",
            "我考虑一下"
        ]
        
        import random
        reply_text = random.choice(replies)
        reply_message = ChatMessage(reply_text, False)
        
        # 添加到消息历史
        self.current_contact.messages.append(reply_message)
        self.add_message(reply_message)
        
        # 更新联系人卡片
        self.current_contact.last_message = reply_text
        self.update_contact_card(self.current_contact)
        
    def scroll_to_bottom(self):
        """滚动到底部"""
        self.scroll_area.verticalScrollBar().setValue(
            self.scroll_area.verticalScrollBar().maximum()
        )
        
    def show_emoji_panel(self):
        """显示表情面板"""
        # 这里可以实现表情选择面板
        print("显示表情面板")
        
    def send_file(self):
        """发送文件"""
        # 这里可以实现文件选择功能
        print("发送文件")
        
    def send_image(self):
        """发送图片"""
        # 这里可以实现图片选择功能
        print("发送图片")

    def setup_whatsapp_service(self):
        """设置WhatsApp服务"""
        try:
            # 初始化WhatsApp服务
            self.whatsapp_service = WhatsAppService(
                store_name=self.current_store,
                username=self.current_user
            )

            # 启动服务状态监控
            self.service_status_timer.start(5000)  # 每5秒检查一次状态

            print(f"WhatsApp服务已初始化: {self.current_store}")

        except Exception as e:
            print(f"初始化WhatsApp服务失败: {e}")

    def start_whatsapp_service(self):
        """启动WhatsApp服务"""
        if self.whatsapp_service and not self.whatsapp_service.is_running:
            try:
                success = self.whatsapp_service.start()
                if success:
                    self.status_label.setText("WhatsApp服务已启动")
                    self.status_label.setStyleSheet(f"""
                        QLabel {{
                            color: {self.themes['app_color']['green']};
                            font-size: 12px;
                        }}
                    """)
                    print("WhatsApp服务启动成功")
                else:
                    self.status_label.setText("WhatsApp服务启动失败")
                    self.status_label.setStyleSheet(f"""
                        QLabel {{
                            color: {self.themes['app_color']['red']};
                            font-size: 12px;
                        }}
                    """)
                    print("WhatsApp服务启动失败")
            except Exception as e:
                print(f"启动WhatsApp服务时出错: {e}")

    def stop_whatsapp_service(self):
        """停止WhatsApp服务"""
        if self.whatsapp_service and self.whatsapp_service.is_running:
            try:
                success = self.whatsapp_service.stop()
                if success:
                    self.status_label.setText("WhatsApp服务已停止")
                    self.status_label.setStyleSheet(f"""
                        QLabel {{
                            color: {self.themes['app_color']['text_description']};
                            font-size: 12px;
                        }}
                    """)
                    print("WhatsApp服务已停止")
            except Exception as e:
                print(f"停止WhatsApp服务时出错: {e}")

    def update_service_status(self):
        """更新服务状态显示"""
        if self.whatsapp_service:
            try:
                status = self.whatsapp_service.get_client_status()
                is_running = status.get("is_running", False)

                if is_running:
                    self.status_label.setText("WhatsApp在线")
                    self.status_label.setStyleSheet(f"""
                        QLabel {{
                            color: {self.themes['app_color']['green']};
                            font-size: 12px;
                        }}
                    """)
                else:
                    self.status_label.setText("WhatsApp离线")
                    self.status_label.setStyleSheet(f"""
                        QLabel {{
                            color: {self.themes['app_color']['text_description']};
                            font-size: 12px;
                        }}
                    """)
            except Exception as e:
                print(f"更新服务状态时出错: {e}")

    def set_store_and_user(self, store_name: str, username: str):
        """设置店铺和用户信息"""
        self.current_store = store_name
        self.current_user = username

        # 重新初始化WhatsApp服务
        if self.whatsapp_service:
            self.stop_whatsapp_service()

        self.setup_whatsapp_service()
        print(f"已切换到店铺: {store_name}, 用户: {username}")

    def get_service_status(self) -> dict:
        """获取服务状态"""
        if self.whatsapp_service:
            return self.whatsapp_service.get_client_status()
        return {"is_running": False, "error": "服务未初始化"}

# 创建聊天页面实例的函数
# ///////////////////////////////////////////////////////////////
def create_chat_page():
    """创建聊天页面实例"""
    return ChatPage()
