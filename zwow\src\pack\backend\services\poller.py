"""
店铺状态轮询器
定期轮询店铺状态并更新数据库
"""

import time
import threading
import logging
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.settings_loader import get_setting
    from src.pack.common_utils.database.app_db_manager import AppDBManager
except ImportError as e:
    print(f"导入模块失败: {e}")
    def get_setting(key, default=None): return default
    class AppDBManager:
        def __init__(self): pass
        def initialize(self): return True

# 配置日志
logger = logging.getLogger(__name__)

class StorePoller:
    """店铺状态轮询器类"""
    
    def __init__(self, polling_interval: int = None, api_base_url: str = None):
        """
        初始化轮询器
        
        Args:
            polling_interval: 轮询间隔（秒），默认从配置读取
            api_base_url: API基础URL，默认从配置读取
        """
        # 从配置获取参数
        self.polling_interval = polling_interval or get_setting("poller.polling_interval", 30)
        self.api_base_url = api_base_url or get_setting("poller.api_base_url", "http://47.120.74.30:9090/api")
        
        # 数据库管理器
        self.db_manager = AppDBManager()
        self.db_manager.initialize()
        
        # 运行状态
        self.running = False
        self.poll_thread = None
        
        # 统计信息
        self.poll_count = 0
        self.success_count = 0
        self.error_count = 0
        self.last_poll_time = None
        self.last_error = None
        
        logger.info(f"轮询器已初始化，间隔: {self.polling_interval}秒")
    
    def start(self) -> bool:
        """
        启动轮询线程
        
        Returns:
            bool: 启动是否成功
        """
        if self.running:
            logger.warning("轮询器已经在运行")
            return False
        
        try:
            self.running = True
            self.poll_thread = threading.Thread(target=self._polling_task, daemon=True)
            self.poll_thread.start()
            logger.info(f"轮询器已启动，间隔: {self.polling_interval}秒")
            return True
        except Exception as e:
            logger.error(f"启动轮询器时出错: {str(e)}")
            self.running = False
            return False
    
    def stop(self) -> bool:
        """
        停止轮询线程
        
        Returns:
            bool: 停止是否成功
        """
        if not self.running:
            logger.warning("轮询器未在运行")
            return True
        
        try:
            self.running = False
            if self.poll_thread and self.poll_thread.is_alive():
                self.poll_thread.join(timeout=5)
            logger.info("轮询器已停止")
            return True
        except Exception as e:
            logger.error(f"停止轮询器时出错: {str(e)}")
            return False
    
    def _polling_task(self):
        """轮询任务主循环"""
        logger.info("轮询任务已启动")
        last_execution_time = 0
        
        while self.running:
            try:
                current_time = time.time()
                
                # 记录执行间隔
                if last_execution_time > 0:
                    elapsed = current_time - last_execution_time
                    logger.debug(f"距离上次轮询已过去 {elapsed:.1f} 秒")
                
                last_execution_time = current_time
                
                # 执行轮询
                success = self._poll_once()
                self.poll_count += 1
                
                if success:
                    self.success_count += 1
                    logger.info("轮询执行成功")
                else:
                    self.error_count += 1
                    logger.warning("轮询执行失败，将在下一周期重试")
                
                self.last_poll_time = current_time
                
                # 验证积分重置状态
                self._verify_points_reset()
                
            except Exception as e:
                self.error_count += 1
                self.last_error = str(e)
                logger.error(f"轮询过程中发生错误: {str(e)}")
            
            # 等待下一次轮询
            self._wait_for_next_poll()
        
        logger.info("轮询任务已结束")
    
    def _wait_for_next_poll(self):
        """等待下一次轮询"""
        logger.debug(f"等待 {self.polling_interval} 秒后进行下一次轮询")
        
        for i in range(self.polling_interval):
            if not self.running:
                break
            
            # 每10秒打印一次等待状态
            if i > 0 and i % 10 == 0:
                remaining = self.polling_interval - i
                logger.debug(f"轮询器等待中... 还剩 {remaining} 秒")
            
            time.sleep(1)
    
    def _poll_once(self) -> bool:
        """
        执行一次轮询操作
        
        Returns:
            bool: 轮询是否成功
        """
        logger.debug("执行轮询操作...")
        
        try:
            # 获取当前用户信息
            user = self._get_current_user()
            if not user:
                logger.error("找不到当前用户，轮询操作取消")
                return False
            
            # 获取所有店铺信息
            stores = self._get_all_stores()
            if not stores:
                logger.warning("没有找到任何店铺信息")
                return False
            
            # 准备请求数据
            request_data = {
                "plg_usn": user["plg_usn"],
                "stores": [
                    {
                        "plg_shopname": store["plg_shopname"],
                        "plg_points": store["plg_points"]
                    } for store in stores
                ]
            }
            
            logger.debug(f"准备轮询 {len(stores)} 个店铺")
            
            # 发送API请求
            response = self._send_api_request(request_data)
            if not response:
                return False
            
            # 处理响应
            return self._process_api_response(response, stores)
            
        except Exception as e:
            logger.error(f"轮询操作时出错: {str(e)}")
            return False
    
    def _get_current_user(self) -> Optional[Dict]:
        """获取当前用户信息"""
        try:
            users = self.db_manager.get_all_users()
            if users:
                return users[0]  # 假设只有一个用户
            return None
        except Exception as e:
            logger.error(f"获取用户信息时出错: {str(e)}")
            return None
    
    def _get_all_stores(self) -> List[Dict]:
        """获取所有店铺信息"""
        try:
            return self.db_manager.get_all_stores()
        except Exception as e:
            logger.error(f"获取店铺信息时出错: {str(e)}")
            return []
    
    def _send_api_request(self, data: Dict) -> Optional[Dict]:
        """
        发送API请求
        
        Args:
            data: 请求数据
            
        Returns:
            Optional[Dict]: 响应数据
        """
        try:
            url = f"{self.api_base_url}/poll"
            timeout = get_setting("poller.request_timeout", 10)
            
            logger.debug(f"发送轮询请求到: {url}")
            
            response = requests.post(
                url,
                json=data,
                timeout=timeout,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"API请求失败，状态码: {response.status_code}")
                return None
                
        except requests.exceptions.Timeout:
            logger.error("API请求超时")
            return None
        except requests.exceptions.ConnectionError:
            logger.error("API连接失败")
            return None
        except Exception as e:
            logger.error(f"发送API请求时出错: {str(e)}")
            return None
    
    def _process_api_response(self, response: Dict, stores: List[Dict]) -> bool:
        """
        处理API响应
        
        Args:
            response: API响应数据
            stores: 店铺列表
            
        Returns:
            bool: 处理是否成功
        """
        try:
            if response.get("success"):
                # 更新店铺状态
                updated_stores = response.get("stores", [])
                return self._update_store_status(updated_stores)
            else:
                error_msg = response.get("message", "未知错误")
                logger.error(f"API返回错误: {error_msg}")
                return False
                
        except Exception as e:
            logger.error(f"处理API响应时出错: {str(e)}")
            return False
    
    def _update_store_status(self, updated_stores: List[Dict]) -> bool:
        """
        更新店铺状态
        
        Args:
            updated_stores: 更新的店铺数据
            
        Returns:
            bool: 更新是否成功
        """
        try:
            updated_count = 0
            
            for store_data in updated_stores:
                store_name = store_data.get("plg_shopname")
                new_points = store_data.get("plg_points")
                new_status = store_data.get("plg_status")
                
                if store_name:
                    # 更新店铺信息
                    success = self.db_manager.update_store_status(
                        store_name, new_points, new_status
                    )
                    if success:
                        updated_count += 1
            
            logger.info(f"成功更新 {updated_count} 个店铺状态")
            return updated_count > 0
            
        except Exception as e:
            logger.error(f"更新店铺状态时出错: {str(e)}")
            return False
    
    def _verify_points_reset(self):
        """验证积分重置状态"""
        try:
            # 检查是否需要重置积分
            # 这里可以实现具体的积分重置逻辑
            pass
        except Exception as e:
            logger.error(f"验证积分重置时出错: {str(e)}")
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取轮询器状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            "running": self.running,
            "polling_interval": self.polling_interval,
            "poll_count": self.poll_count,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "last_poll_time": self.last_poll_time,
            "last_error": self.last_error,
            "success_rate": self.success_count / max(self.poll_count, 1)
        }
    
    def force_poll(self) -> bool:
        """
        强制执行一次轮询
        
        Returns:
            bool: 轮询是否成功
        """
        if not self.running:
            logger.warning("轮询器未运行，无法强制轮询")
            return False
        
        logger.info("执行强制轮询")
        return self._poll_once()
    
    def update_config(self, polling_interval: int = None, api_base_url: str = None):
        """
        更新配置
        
        Args:
            polling_interval: 新的轮询间隔
            api_base_url: 新的API基础URL
        """
        if polling_interval is not None:
            self.polling_interval = polling_interval
            logger.info(f"轮询间隔已更新为: {polling_interval}秒")
        
        if api_base_url is not None:
            self.api_base_url = api_base_url
            logger.info(f"API基础URL已更新为: {api_base_url}")

# 启动函数
def run_poller():
    """启动轮询程序"""
    poller = StorePoller()
    success = poller.start()
    
    if success:
        logger.info("轮询器已成功启动")
    else:
        logger.error("轮询器启动失败")
        return False
    
    try:
        logger.info("按Ctrl+C终止轮询...")
        while poller.running:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("接收到终止信号，停止轮询...")
        poller.stop()
    
    return True
