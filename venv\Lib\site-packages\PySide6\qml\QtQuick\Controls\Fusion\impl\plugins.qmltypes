import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquickfusionbusyindicator_p.h"
        name: "QQuickFusionBusyIndicator"
        accessSemantics: "reference"
        prototype: "QQuickPaintedItem"
        exports: [
            "QtQuick.Controls.Fusion.impl/BusyIndicatorImpl 2.3",
            "QtQuick.Controls.Fusion.impl/BusyIndicatorImpl 2.4",
            "QtQuick.Controls.Fusion.impl/BusyIndicatorImpl 2.7",
            "QtQuick.Controls.Fusion.impl/BusyIndicatorImpl 2.11",
            "QtQuick.Controls.Fusion.impl/BusyIndicatorImpl 6.0",
            "QtQuick.Controls.Fusion.impl/BusyIndicatorImpl 6.3",
            "QtQuick.Controls.Fusion.impl/BusyIndicatorImpl 6.7"
        ]
        exportMetaObjectRevisions: [515, 516, 519, 523, 1536, 1539, 1543]
        Property { name: "color"; type: "QColor"; read: "color"; write: "setColor"; index: 0; isFinal: true }
        Property { name: "running"; type: "bool"; read: "isRunning"; write: "setRunning"; index: 1 }
    }
    Component {
        file: "private/qquickfusiondial_p.h"
        name: "QQuickFusionDial"
        accessSemantics: "reference"
        prototype: "QQuickPaintedItem"
        exports: [
            "QtQuick.Controls.Fusion.impl/DialImpl 2.3",
            "QtQuick.Controls.Fusion.impl/DialImpl 2.4",
            "QtQuick.Controls.Fusion.impl/DialImpl 2.7",
            "QtQuick.Controls.Fusion.impl/DialImpl 2.11",
            "QtQuick.Controls.Fusion.impl/DialImpl 6.0",
            "QtQuick.Controls.Fusion.impl/DialImpl 6.3",
            "QtQuick.Controls.Fusion.impl/DialImpl 6.7"
        ]
        exportMetaObjectRevisions: [515, 516, 519, 523, 1536, 1539, 1543]
        Property {
            name: "highlight"
            type: "bool"
            read: "highlight"
            write: "setHighlight"
            index: 0
            isFinal: true
        }
    }
    Component {
        file: "private/qquickfusionknob_p.h"
        name: "QQuickFusionKnob"
        accessSemantics: "reference"
        prototype: "QQuickPaintedItem"
        exports: [
            "QtQuick.Controls.Fusion.impl/KnobImpl 2.3",
            "QtQuick.Controls.Fusion.impl/KnobImpl 2.4",
            "QtQuick.Controls.Fusion.impl/KnobImpl 2.7",
            "QtQuick.Controls.Fusion.impl/KnobImpl 2.11",
            "QtQuick.Controls.Fusion.impl/KnobImpl 6.0",
            "QtQuick.Controls.Fusion.impl/KnobImpl 6.3",
            "QtQuick.Controls.Fusion.impl/KnobImpl 6.7"
        ]
        exportMetaObjectRevisions: [515, 516, 519, 523, 1536, 1539, 1543]
    }
}
