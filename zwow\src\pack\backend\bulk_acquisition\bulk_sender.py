"""
批量发送器
负责批量发送消息的核心功能
"""

import logging
import re
import time
import threading
from pathlib import Path
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.settings_loader import get_setting
    from src.pack.common_utils.database.app_db_manager import AppDBManager
except ImportError as e:
    print(f"导入模块失败: {e}")
    def get_setting(key, default=None): return default
    class AppDBManager:
        def __init__(self): pass
        def initialize(self): return True

# 配置日志
logger = logging.getLogger(__name__)

class SendStatus(Enum):
    """发送状态枚举"""
    PENDING = "pending"
    SENDING = "sending"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class SendTask:
    """发送任务数据类"""
    phone_number: str
    message: str
    status: SendStatus = SendStatus.PENDING
    error_message: str = ""
    send_time: Optional[float] = None
    retry_count: int = 0

@dataclass
class SendResult:
    """发送结果数据类"""
    total_count: int
    success_count: int
    failed_count: int
    cancelled_count: int
    tasks: List[SendTask]
    start_time: float
    end_time: float
    
    @property
    def duration(self) -> float:
        """发送总耗时"""
        return self.end_time - self.start_time
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_count == 0:
            return 0.0
        return self.success_count / self.total_count

class BulkSender:
    """批量发送器类"""
    
    def __init__(self):
        """初始化批量发送器"""
        # 发送配置
        self.send_interval = get_setting("bulk_acquisition.send_interval", 1.0)  # 发送间隔（秒）
        self.max_retries = get_setting("bulk_acquisition.max_retries", 3)  # 最大重试次数
        self.timeout = get_setting("bulk_acquisition.timeout", 30)  # 超时时间（秒）
        self.batch_size = get_setting("bulk_acquisition.batch_size", 10)  # 批次大小
        
        # 状态管理
        self.is_sending = False
        self.is_cancelled = False
        self.current_tasks = []
        self.send_thread = None
        
        # 回调函数
        self.progress_callback = None
        self.completion_callback = None
        
        # 数据库管理器
        self.db_manager = AppDBManager()
        self.db_manager.initialize()
        
        logger.info("批量发送器已初始化")
    
    def validate_phone_number(self, phone: str) -> bool:
        """
        验证电话号码格式
        
        Args:
            phone: 电话号码
            
        Returns:
            bool: 是否有效
        """
        if not phone:
            return False
        
        # 清理号码（移除空格和特殊字符）
        cleaned_phone = re.sub(r'[^\d+]', '', phone)
        
        # 验证国际格式 (+86138****8888)
        international_pattern = r'^\+\d{1,3}\d{10,14}$'
        if re.match(international_pattern, cleaned_phone):
            return True
        
        # 验证国内格式 (138****8888)
        domestic_pattern = r'^1[3-9]\d{9}$'
        if re.match(domestic_pattern, cleaned_phone):
            return True
        
        return False
    
    def normalize_phone_number(self, phone: str) -> str:
        """
        标准化电话号码格式
        
        Args:
            phone: 原始电话号码
            
        Returns:
            str: 标准化后的电话号码
        """
        # 清理号码
        cleaned_phone = re.sub(r'[^\d+]', '', phone)
        
        # 如果是国内号码且没有国际前缀，添加+86
        if re.match(r'^1[3-9]\d{9}$', cleaned_phone):
            return f"+86{cleaned_phone}"
        
        return cleaned_phone
    
    def prepare_tasks(self, phone_numbers: List[str], message: str) -> List[SendTask]:
        """
        准备发送任务
        
        Args:
            phone_numbers: 电话号码列表
            message: 消息内容
            
        Returns:
            List[SendTask]: 发送任务列表
        """
        tasks = []
        
        for phone in phone_numbers:
            if self.validate_phone_number(phone):
                normalized_phone = self.normalize_phone_number(phone)
                task = SendTask(
                    phone_number=normalized_phone,
                    message=message
                )
                tasks.append(task)
            else:
                logger.warning(f"无效的电话号码: {phone}")
        
        logger.info(f"准备了 {len(tasks)} 个发送任务")
        return tasks
    
    def send_bulk_messages(self, phone_numbers: List[str], message: str,
                          progress_callback: Callable = None,
                          completion_callback: Callable = None) -> bool:
        """
        批量发送消息
        
        Args:
            phone_numbers: 电话号码列表
            message: 消息内容
            progress_callback: 进度回调函数
            completion_callback: 完成回调函数
            
        Returns:
            bool: 是否成功启动发送任务
        """
        if self.is_sending:
            logger.warning("已有发送任务在进行中")
            return False
        
        if not phone_numbers or not message:
            logger.error("电话号码列表或消息内容为空")
            return False
        
        # 准备发送任务
        self.current_tasks = self.prepare_tasks(phone_numbers, message)
        if not self.current_tasks:
            logger.error("没有有效的发送任务")
            return False
        
        # 设置回调函数
        self.progress_callback = progress_callback
        self.completion_callback = completion_callback
        
        # 重置状态
        self.is_sending = True
        self.is_cancelled = False
        
        # 启动发送线程
        self.send_thread = threading.Thread(target=self._send_worker)
        self.send_thread.daemon = True
        self.send_thread.start()
        
        logger.info(f"开始批量发送，共 {len(self.current_tasks)} 个任务")
        return True
    
    def _send_worker(self):
        """发送工作线程"""
        start_time = time.time()
        
        try:
            total_count = len(self.current_tasks)
            success_count = 0
            failed_count = 0
            cancelled_count = 0
            
            for i, task in enumerate(self.current_tasks):
                if self.is_cancelled:
                    task.status = SendStatus.CANCELLED
                    cancelled_count += 1
                    continue
                
                # 更新任务状态
                task.status = SendStatus.SENDING
                
                # 发送进度回调
                if self.progress_callback:
                    progress = int((i / total_count) * 100)
                    self.progress_callback(progress, f"正在发送到 {task.phone_number}")
                
                # 执行发送
                success = self._send_single_message(task)
                
                if success:
                    task.status = SendStatus.SUCCESS
                    success_count += 1
                else:
                    task.status = SendStatus.FAILED
                    failed_count += 1
                
                # 发送间隔
                if i < total_count - 1 and not self.is_cancelled:
                    time.sleep(self.send_interval)
            
            # 构建结果
            end_time = time.time()
            result = SendResult(
                total_count=total_count,
                success_count=success_count,
                failed_count=failed_count,
                cancelled_count=cancelled_count,
                tasks=self.current_tasks,
                start_time=start_time,
                end_time=end_time
            )
            
            # 完成回调
            if self.completion_callback:
                self.completion_callback(result)
            
            # 保存发送记录
            self._save_send_record(result)
            
            logger.info(f"批量发送完成，成功: {success_count}, 失败: {failed_count}, 取消: {cancelled_count}")
            
        except Exception as e:
            logger.error(f"批量发送过程中出错: {str(e)}")
            if self.completion_callback:
                self.completion_callback(None)
        finally:
            self.is_sending = False
    
    def _send_single_message(self, task: SendTask) -> bool:
        """
        发送单条消息
        
        Args:
            task: 发送任务
            
        Returns:
            bool: 是否发送成功
        """
        try:
            # 这里应该调用实际的消息发送API
            # 目前使用模拟发送
            
            # 模拟发送延迟
            time.sleep(0.5)
            
            # 模拟发送结果（90%成功率）
            import random
            success = random.random() > 0.1
            
            if success:
                task.send_time = time.time()
                logger.debug(f"消息发送成功: {task.phone_number}")
                return True
            else:
                task.error_message = "模拟发送失败"
                logger.debug(f"消息发送失败: {task.phone_number}")
                return False
                
        except Exception as e:
            task.error_message = str(e)
            logger.error(f"发送消息到 {task.phone_number} 时出错: {str(e)}")
            return False
    
    def cancel_sending(self):
        """取消发送"""
        if self.is_sending:
            self.is_cancelled = True
            logger.info("发送任务已取消")
    
    def get_sending_status(self) -> Dict[str, Any]:
        """
        获取发送状态
        
        Returns:
            Dict[str, Any]: 发送状态信息
        """
        if not self.current_tasks:
            return {
                'is_sending': False,
                'total_count': 0,
                'completed_count': 0,
                'success_count': 0,
                'failed_count': 0
            }
        
        total_count = len(self.current_tasks)
        completed_count = sum(1 for task in self.current_tasks 
                            if task.status in [SendStatus.SUCCESS, SendStatus.FAILED, SendStatus.CANCELLED])
        success_count = sum(1 for task in self.current_tasks if task.status == SendStatus.SUCCESS)
        failed_count = sum(1 for task in self.current_tasks if task.status == SendStatus.FAILED)
        
        return {
            'is_sending': self.is_sending,
            'is_cancelled': self.is_cancelled,
            'total_count': total_count,
            'completed_count': completed_count,
            'success_count': success_count,
            'failed_count': failed_count,
            'progress': int((completed_count / total_count) * 100) if total_count > 0 else 0
        }
    
    def _save_send_record(self, result: SendResult):
        """
        保存发送记录
        
        Args:
            result: 发送结果
        """
        try:
            # 这里应该保存到数据库
            # 目前只记录日志
            logger.info(f"发送记录: 总数={result.total_count}, 成功={result.success_count}, "
                       f"失败={result.failed_count}, 耗时={result.duration:.2f}秒")
        except Exception as e:
            logger.error(f"保存发送记录时出错: {str(e)}")
    
    def get_send_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取发送历史记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            List[Dict[str, Any]]: 发送历史记录
        """
        # 这里应该从数据库获取历史记录
        # 目前返回空列表
        return []
