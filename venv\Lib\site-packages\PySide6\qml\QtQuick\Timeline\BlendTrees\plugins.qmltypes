import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qblendanimationnode_p.h"
        name: "QBlendAnimationNode"
        accessSemantics: "reference"
        prototype: "QBlendTreeNode"
        exports: ["QtQuick.Timeline.BlendTrees/BlendAnimationNode 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "source1"
            type: "QBlendTreeNode"
            isPointer: true
            read: "source1"
            write: "setSource1"
            notify: "source1Changed"
            index: 0
            isFinal: true
        }
        Property {
            name: "source2"
            type: "QBlendTreeNode"
            isPointer: true
            read: "source2"
            write: "setSource2"
            notify: "source2Changed"
            index: 1
            isFinal: true
        }
        Property {
            name: "weight"
            type: "double"
            read: "weight"
            write: "setWeight"
            notify: "weightChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "source1Changed" }
        Signal { name: "source2Changed" }
        Signal { name: "weightChanged" }
        Method { name: "handleInputFrameDataChanged" }
    }
    Component {
        file: "private/qblendtreenode_p.h"
        name: "QBlendTreeNode"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick.Timeline.BlendTrees/BlendTreeNode 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property {
            name: "outputEnabled"
            type: "bool"
            read: "outputEnabled"
            write: "setOutputEnabled"
            notify: "outputEnabledChanged"
            index: 0
            isFinal: true
        }
        Signal { name: "frameDataChanged" }
        Signal { name: "outputEnabledChanged" }
        Method { name: "handleFrameDataChanged" }
    }
    Component {
        file: "private/qtimelineanimationnode_p.h"
        name: "QTimelineAnimationNode"
        accessSemantics: "reference"
        prototype: "QBlendTreeNode"
        exports: ["QtQuick.Timeline.BlendTrees/TimelineAnimationNode 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "animation"
            type: "QQuickTimelineAnimation"
            isPointer: true
            read: "animation"
            write: "setAnimation"
            notify: "animationChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "timeline"
            type: "QQuickTimeline"
            isPointer: true
            read: "timeline"
            write: "setTimeline"
            notify: "timelineChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "currentFrame"
            type: "double"
            read: "currentFrame"
            write: "setCurrentFrame"
            notify: "currentFrameChanged"
            index: 2
            isFinal: true
        }
        Signal { name: "animationChanged" }
        Signal { name: "timelineChanged" }
        Signal { name: "currentFrameChanged" }
    }
}
