import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QAbstractAnimationClip"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Animation/AbstractAnimationClip 2.9",
            "Qt3D.Animation/AbstractAnimationClip 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "duration"
            type: "float"
            read: "duration"
            notify: "durationChanged"
            index: 0
            isReadonly: true
        }
        Signal {
            name: "durationChanged"
            Parameter { name: "duration"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QAbstractAnimation"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "Qt3D.Animation/AbstractAnimation 2.9",
            "Qt3D.Animation/AbstractAnimation 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [521, 1536]
        Enum {
            name: "AnimationType"
            values: [
                "KeyframeAnimation",
                "MorphingAnimation",
                "VertexBlendAnimation"
            ]
        }
        Property {
            name: "animationName"
            type: "QString"
            read: "animationName"
            write: "setAnimationName"
            notify: "animationNameChanged"
            index: 0
        }
        Property {
            name: "animationType"
            type: "AnimationType"
            read: "animationType"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "position"
            type: "float"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 2
        }
        Property {
            name: "duration"
            type: "float"
            read: "duration"
            notify: "durationChanged"
            index: 3
            isReadonly: true
        }
        Signal {
            name: "animationNameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "float" }
        }
        Signal {
            name: "durationChanged"
            Parameter { name: "duration"; type: "float" }
        }
        Method {
            name: "setAnimationName"
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QAbstractChannelMapping"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Animation/AbstractChannelMapping 2.10",
            "Qt3D.Animation/AbstractChannelMapping 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [522, 1536]
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QAbstractClipAnimator"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QComponent"
        exports: [
            "Qt3D.Animation/AbstractClipAnimator 2.9",
            "Qt3D.Animation/AbstractClipAnimator 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [521, 1536]
        Enum {
            name: "Loops"
            values: ["Infinite"]
        }
        Property {
            name: "running"
            type: "bool"
            read: "isRunning"
            write: "setRunning"
            notify: "runningChanged"
            index: 0
        }
        Property {
            name: "loops"
            type: "int"
            read: "loopCount"
            write: "setLoopCount"
            notify: "loopCountChanged"
            index: 1
        }
        Property {
            name: "channelMapper"
            type: "Qt3DAnimation::QChannelMapper"
            isPointer: true
            read: "channelMapper"
            write: "setChannelMapper"
            notify: "channelMapperChanged"
            index: 2
        }
        Property {
            name: "clock"
            type: "Qt3DAnimation::QClock"
            isPointer: true
            read: "clock"
            write: "setClock"
            notify: "clockChanged"
            index: 3
        }
        Property {
            name: "normalizedTime"
            type: "float"
            read: "normalizedTime"
            write: "setNormalizedTime"
            notify: "normalizedTimeChanged"
            index: 4
        }
        Signal {
            name: "runningChanged"
            Parameter { name: "running"; type: "bool" }
        }
        Signal {
            name: "channelMapperChanged"
            Parameter { name: "channelMapper"; type: "Qt3DAnimation::QChannelMapper"; isPointer: true }
        }
        Signal {
            name: "loopCountChanged"
            Parameter { name: "loops"; type: "int" }
        }
        Signal {
            name: "clockChanged"
            Parameter { name: "clock"; type: "Qt3DAnimation::QClock"; isPointer: true }
        }
        Signal {
            name: "normalizedTimeChanged"
            Parameter { name: "index"; type: "float" }
        }
        Method {
            name: "setRunning"
            Parameter { name: "running"; type: "bool" }
        }
        Method {
            name: "setChannelMapper"
            Parameter { name: "channelMapper"; type: "Qt3DAnimation::QChannelMapper"; isPointer: true }
        }
        Method {
            name: "setLoopCount"
            Parameter { name: "loops"; type: "int" }
        }
        Method {
            name: "setClock"
            Parameter { name: "clock"; type: "Qt3DAnimation::QClock"; isPointer: true }
        }
        Method {
            name: "setNormalizedTime"
            Parameter { name: "timeFraction"; type: "float" }
        }
        Method { name: "start" }
        Method { name: "stop" }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QAbstractClipBlendNode"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: [
            "Qt3D.Animation/AbstractClipBlendNode 2.9",
            "Qt3D.Animation/AbstractClipBlendNode 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [521, 1536]
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QAdditiveClipBlend"
        accessSemantics: "reference"
        prototype: "Qt3DAnimation::QAbstractClipBlendNode"
        exports: [
            "Qt3D.Animation/AdditiveClipBlend 2.9",
            "Qt3D.Animation/AdditiveClipBlend 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "baseClip"
            type: "Qt3DAnimation::QAbstractClipBlendNode"
            isPointer: true
            read: "baseClip"
            write: "setBaseClip"
            notify: "baseClipChanged"
            index: 0
        }
        Property {
            name: "additiveClip"
            type: "Qt3DAnimation::QAbstractClipBlendNode"
            isPointer: true
            read: "additiveClip"
            write: "setAdditiveClip"
            notify: "additiveClipChanged"
            index: 1
        }
        Property {
            name: "additiveFactor"
            type: "float"
            read: "additiveFactor"
            write: "setAdditiveFactor"
            notify: "additiveFactorChanged"
            index: 2
        }
        Signal {
            name: "additiveFactorChanged"
            Parameter { name: "additiveFactor"; type: "float" }
        }
        Signal {
            name: "baseClipChanged"
            Parameter { name: "baseClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        }
        Signal {
            name: "additiveClipChanged"
            Parameter {
                name: "additiveClip"
                type: "Qt3DAnimation::QAbstractClipBlendNode"
                isPointer: true
            }
        }
        Method {
            name: "setAdditiveFactor"
            Parameter { name: "additiveFactor"; type: "float" }
        }
        Method {
            name: "setBaseClip"
            Parameter { name: "baseClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        }
        Method {
            name: "setAdditiveClip"
            Parameter {
                name: "additiveClip"
                type: "Qt3DAnimation::QAbstractClipBlendNode"
                isPointer: true
            }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QAnimationClip"
        accessSemantics: "reference"
        prototype: "Qt3DAnimation::QAbstractAnimationClip"
        exports: [
            "Qt3D.Animation/AnimationClip 2.9",
            "Qt3D.Animation/AnimationClip 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "clipData"
            type: "Qt3DAnimation::QAnimationClipData"
            read: "clipData"
            write: "setClipData"
            notify: "clipDataChanged"
            index: 0
        }
        Signal {
            name: "clipDataChanged"
            Parameter { name: "clipData"; type: "Qt3DAnimation::QAnimationClipData" }
        }
        Method {
            name: "setClipData"
            Parameter { name: "clipData"; type: "Qt3DAnimation::QAnimationClipData" }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QAnimationClipLoader"
        accessSemantics: "reference"
        prototype: "Qt3DAnimation::QAbstractAnimationClip"
        exports: [
            "Qt3D.Animation/AnimationClipLoader 2.9",
            "Qt3D.Animation/AnimationClipLoader 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Enum {
            name: "Status"
            values: ["NotReady", "Ready", "Error"]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 1
            isReadonly: true
        }
        Signal {
            name: "sourceChanged"
            Parameter { name: "source"; type: "QUrl" }
        }
        Signal {
            name: "statusChanged"
            Parameter { name: "status"; type: "Status" }
        }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QAnimationController"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "Qt3DAnimation::Quick::QQuick3DAnimationController"
        exports: [
            "Qt3D.Animation/AnimationController 2.9",
            "Qt3D.Animation/AnimationController 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "activeAnimationGroup"
            type: "int"
            read: "activeAnimationGroup"
            write: "setActiveAnimationGroup"
            notify: "activeAnimationGroupChanged"
            index: 0
        }
        Property {
            name: "position"
            type: "float"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 1
        }
        Property {
            name: "positionScale"
            type: "float"
            read: "positionScale"
            write: "setPositionScale"
            notify: "positionScaleChanged"
            index: 2
        }
        Property {
            name: "positionOffset"
            type: "float"
            read: "positionOffset"
            write: "setPositionOffset"
            notify: "positionOffsetChanged"
            index: 3
        }
        Property {
            name: "entity"
            type: "Qt3DCore::QEntity"
            isPointer: true
            read: "entity"
            write: "setEntity"
            notify: "entityChanged"
            index: 4
        }
        Property {
            name: "recursive"
            type: "bool"
            read: "recursive"
            write: "setRecursive"
            notify: "recursiveChanged"
            index: 5
        }
        Signal {
            name: "activeAnimationGroupChanged"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "float" }
        }
        Signal {
            name: "positionScaleChanged"
            Parameter { name: "scale"; type: "float" }
        }
        Signal {
            name: "positionOffsetChanged"
            Parameter { name: "offset"; type: "float" }
        }
        Signal {
            name: "entityChanged"
            Parameter { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Signal {
            name: "recursiveChanged"
            Parameter { name: "recursive"; type: "bool" }
        }
        Method {
            name: "setActiveAnimationGroup"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "float" }
        }
        Method {
            name: "setPositionScale"
            Parameter { name: "scale"; type: "float" }
        }
        Method {
            name: "setPositionOffset"
            Parameter { name: "offset"; type: "float" }
        }
        Method {
            name: "setEntity"
            Parameter { name: "entity"; type: "Qt3DCore::QEntity"; isPointer: true }
        }
        Method {
            name: "setRecursive"
            Parameter { name: "recursive"; type: "bool" }
        }
        Method {
            name: "getAnimationIndex"
            type: "int"
            isMethodConstant: true
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "getGroup"
            type: "Qt3DAnimation::QAnimationGroup"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QAnimationGroup"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "Qt3DAnimation::Quick::QQuick3DAnimationGroup"
        exports: [
            "Qt3D.Animation/AnimationGroup 2.9",
            "Qt3D.Animation/AnimationGroup 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 0
        }
        Property {
            name: "position"
            type: "float"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 1
        }
        Property {
            name: "duration"
            type: "float"
            read: "duration"
            notify: "durationChanged"
            index: 2
            isReadonly: true
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "float" }
        }
        Signal {
            name: "durationChanged"
            Parameter { name: "duration"; type: "float" }
        }
        Method {
            name: "setName"
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "float" }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QBlendedClipAnimator"
        accessSemantics: "reference"
        prototype: "Qt3DAnimation::QAbstractClipAnimator"
        exports: [
            "Qt3D.Animation/BlendedClipAnimator 2.9",
            "Qt3D.Animation/BlendedClipAnimator 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "blendTree"
            type: "Qt3DAnimation::QAbstractClipBlendNode"
            isPointer: true
            read: "blendTree"
            write: "setBlendTree"
            notify: "blendTreeChanged"
            index: 0
        }
        Signal {
            name: "blendTreeChanged"
            Parameter { name: "blendTree"; type: "QAbstractClipBlendNode"; isPointer: true }
        }
        Method {
            name: "setBlendTree"
            Parameter { name: "blendTree"; type: "QAbstractClipBlendNode"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QChannelMapper"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        extension: "Qt3DAnimation::Animation::Quick::Quick3DChannelMapper"
        exports: [
            "Qt3D.Animation/ChannelMapper 2.9",
            "Qt3D.Animation/ChannelMapper 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QChannelMapping"
        accessSemantics: "reference"
        prototype: "Qt3DAnimation::QAbstractChannelMapping"
        exports: [
            "Qt3D.Animation/ChannelMapping 2.9",
            "Qt3D.Animation/ChannelMapping 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "channelName"
            type: "QString"
            read: "channelName"
            write: "setChannelName"
            notify: "channelNameChanged"
            index: 0
        }
        Property {
            name: "target"
            type: "Qt3DCore::QNode"
            isPointer: true
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 1
        }
        Property {
            name: "property"
            type: "QString"
            read: "property"
            write: "setProperty"
            notify: "propertyChanged"
            index: 2
        }
        Signal {
            name: "channelNameChanged"
            Parameter { name: "channelName"; type: "QString" }
        }
        Signal {
            name: "targetChanged"
            Parameter { name: "target"; type: "Qt3DCore::QNode"; isPointer: true }
        }
        Signal {
            name: "propertyChanged"
            Parameter { name: "property"; type: "QString" }
        }
        Method {
            name: "setChannelName"
            Parameter { name: "channelName"; type: "QString" }
        }
        Method {
            name: "setTarget"
            Parameter { name: "target"; type: "Qt3DCore::QNode"; isPointer: true }
        }
        Method {
            name: "setProperty"
            Parameter { name: "property"; type: "QString" }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QClipAnimator"
        accessSemantics: "reference"
        prototype: "Qt3DAnimation::QAbstractClipAnimator"
        exports: [
            "Qt3D.Animation/ClipAnimator 2.9",
            "Qt3D.Animation/ClipAnimator 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "clip"
            type: "Qt3DAnimation::QAbstractAnimationClip"
            isPointer: true
            read: "clip"
            write: "setClip"
            notify: "clipChanged"
            index: 0
        }
        Signal {
            name: "clipChanged"
            Parameter { name: "clip"; type: "Qt3DAnimation::QAbstractAnimationClip"; isPointer: true }
        }
        Method {
            name: "setClip"
            Parameter { name: "clip"; type: "Qt3DAnimation::QAbstractAnimationClip"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QClipBlendValue"
        accessSemantics: "reference"
        prototype: "Qt3DAnimation::QAbstractClipBlendNode"
        exports: [
            "Qt3D.Animation/ClipBlendValue 2.9",
            "Qt3D.Animation/ClipBlendValue 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "clip"
            type: "Qt3DAnimation::QAbstractAnimationClip"
            isPointer: true
            read: "clip"
            write: "setClip"
            notify: "clipChanged"
            index: 0
        }
        Signal {
            name: "clipChanged"
            Parameter { name: "clip"; type: "Qt3DAnimation::QAbstractAnimationClip"; isPointer: true }
        }
        Method {
            name: "setClip"
            Parameter { name: "clip"; type: "Qt3DAnimation::QAbstractAnimationClip"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QClock"
        accessSemantics: "reference"
        prototype: "Qt3DCore::QNode"
        exports: ["Qt3D.Animation/Clock 2.9", "Qt3D.Animation/Clock 6.0"]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "playbackRate"
            type: "double"
            read: "playbackRate"
            write: "setPlaybackRate"
            notify: "playbackRateChanged"
            index: 0
        }
        Signal {
            name: "playbackRateChanged"
            Parameter { name: "playbackRate"; type: "double" }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QKeyframeAnimation"
        accessSemantics: "reference"
        prototype: "Qt3DAnimation::QAbstractAnimation"
        extension: "Qt3DAnimation::Quick::QQuick3DKeyframeAnimation"
        exports: [
            "Qt3D.Animation/KeyframeAnimation 2.9",
            "Qt3D.Animation/KeyframeAnimation 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Enum {
            name: "RepeatMode"
            values: ["None", "Constant", "Repeat"]
        }
        Property {
            name: "framePositions"
            type: "float"
            isList: true
            read: "framePositions"
            write: "setFramePositions"
            notify: "framePositionsChanged"
            index: 0
        }
        Property {
            name: "target"
            type: "Qt3DCore::QTransform"
            isPointer: true
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 1
        }
        Property {
            name: "easing"
            type: "QEasingCurve"
            read: "easing"
            write: "setEasing"
            notify: "easingChanged"
            index: 2
        }
        Property {
            name: "targetName"
            type: "QString"
            read: "targetName"
            write: "setTargetName"
            notify: "targetNameChanged"
            index: 3
        }
        Property {
            name: "startMode"
            type: "RepeatMode"
            read: "startMode"
            write: "setStartMode"
            notify: "startModeChanged"
            index: 4
        }
        Property {
            name: "endMode"
            type: "RepeatMode"
            read: "endMode"
            write: "setEndMode"
            notify: "endModeChanged"
            index: 5
        }
        Signal {
            name: "framePositionsChanged"
            Parameter { name: "positions"; type: "float"; isList: true }
        }
        Signal {
            name: "targetChanged"
            Parameter { name: "target"; type: "Qt3DCore::QTransform"; isPointer: true }
        }
        Signal {
            name: "easingChanged"
            Parameter { name: "easing"; type: "QEasingCurve" }
        }
        Signal {
            name: "targetNameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "startModeChanged"
            Parameter { name: "startMode"; type: "QKeyframeAnimation::RepeatMode" }
        }
        Signal {
            name: "endModeChanged"
            Parameter { name: "endMode"; type: "QKeyframeAnimation::RepeatMode" }
        }
        Method {
            name: "setFramePositions"
            Parameter { name: "positions"; type: "float"; isList: true }
        }
        Method {
            name: "setTarget"
            Parameter { name: "target"; type: "Qt3DCore::QTransform"; isPointer: true }
        }
        Method {
            name: "setEasing"
            Parameter { name: "easing"; type: "QEasingCurve" }
        }
        Method {
            name: "setTargetName"
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "setStartMode"
            Parameter { name: "mode"; type: "RepeatMode" }
        }
        Method {
            name: "setEndMode"
            Parameter { name: "mode"; type: "RepeatMode" }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QLerpClipBlend"
        accessSemantics: "reference"
        prototype: "Qt3DAnimation::QAbstractClipBlendNode"
        exports: [
            "Qt3D.Animation/LerpClipBlend 2.9",
            "Qt3D.Animation/LerpClipBlend 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "startClip"
            type: "Qt3DAnimation::QAbstractClipBlendNode"
            isPointer: true
            read: "startClip"
            write: "setStartClip"
            notify: "startClipChanged"
            index: 0
        }
        Property {
            name: "endClip"
            type: "Qt3DAnimation::QAbstractClipBlendNode"
            isPointer: true
            read: "endClip"
            write: "setEndClip"
            notify: "endClipChanged"
            index: 1
        }
        Property {
            name: "blendFactor"
            type: "float"
            read: "blendFactor"
            write: "setBlendFactor"
            notify: "blendFactorChanged"
            index: 2
        }
        Signal {
            name: "blendFactorChanged"
            Parameter { name: "blendFactor"; type: "float" }
        }
        Signal {
            name: "startClipChanged"
            Parameter { name: "startClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        }
        Signal {
            name: "endClipChanged"
            Parameter { name: "endClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        }
        Method {
            name: "setBlendFactor"
            Parameter { name: "blendFactor"; type: "float" }
        }
        Method {
            name: "setStartClip"
            Parameter { name: "startClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        }
        Method {
            name: "setEndClip"
            Parameter { name: "endClip"; type: "Qt3DAnimation::QAbstractClipBlendNode"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QMorphTarget"
        accessSemantics: "reference"
        prototype: "QObject"
        extension: "Qt3DAnimation::Quick::QQuick3DMorphTarget"
        exports: [
            "Qt3D.Animation/MorphTarget 2.9",
            "Qt3D.Animation/MorphTarget 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "attributeNames"
            type: "QStringList"
            read: "attributeNames"
            notify: "attributeNamesChanged"
            index: 0
            isReadonly: true
        }
        Signal {
            name: "attributeNamesChanged"
            Parameter { name: "attributeNames"; type: "QStringList" }
        }
        Method {
            name: "fromGeometry"
            type: "QMorphTarget"
            isPointer: true
            Parameter { name: "geometry"; type: "Qt3DCore::QGeometry"; isPointer: true }
            Parameter { name: "attributes"; type: "QStringList" }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QMorphingAnimation"
        accessSemantics: "reference"
        prototype: "Qt3DAnimation::QAbstractAnimation"
        extension: "Qt3DAnimation::Quick::QQuick3DMorphingAnimation"
        exports: [
            "Qt3D.Animation/MorphingAnimation 2.9",
            "Qt3D.Animation/MorphingAnimation 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Enum {
            name: "Method"
            values: ["Normalized", "Relative"]
        }
        Property {
            name: "targetPositions"
            type: "float"
            isList: true
            read: "targetPositions"
            write: "setTargetPositions"
            notify: "targetPositionsChanged"
            index: 0
        }
        Property {
            name: "interpolator"
            type: "float"
            read: "interpolator"
            notify: "interpolatorChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "target"
            type: "Qt3DRender::QGeometryRenderer"
            isPointer: true
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 2
        }
        Property {
            name: "targetName"
            type: "QString"
            read: "targetName"
            write: "setTargetName"
            notify: "targetNameChanged"
            index: 3
        }
        Property {
            name: "method"
            type: "Method"
            read: "method"
            write: "setMethod"
            notify: "methodChanged"
            index: 4
        }
        Property {
            name: "easing"
            type: "QEasingCurve"
            read: "easing"
            write: "setEasing"
            notify: "easingChanged"
            index: 5
        }
        Signal {
            name: "targetPositionsChanged"
            Parameter { name: "targetPositions"; type: "float"; isList: true }
        }
        Signal {
            name: "interpolatorChanged"
            Parameter { name: "interpolator"; type: "float" }
        }
        Signal {
            name: "targetChanged"
            Parameter { name: "target"; type: "Qt3DRender::QGeometryRenderer"; isPointer: true }
        }
        Signal {
            name: "targetNameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "methodChanged"
            Parameter { name: "method"; type: "QMorphingAnimation::Method" }
        }
        Signal {
            name: "easingChanged"
            Parameter { name: "easing"; type: "QEasingCurve" }
        }
        Method {
            name: "setTargetPositions"
            Parameter { name: "targetPositions"; type: "float"; isList: true }
        }
        Method {
            name: "setTarget"
            Parameter { name: "target"; type: "Qt3DRender::QGeometryRenderer"; isPointer: true }
        }
        Method {
            name: "setTargetName"
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "setMethod"
            Parameter { name: "method"; type: "QMorphingAnimation::Method" }
        }
        Method {
            name: "setEasing"
            Parameter { name: "easing"; type: "QEasingCurve" }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QSkeletonMapping"
        accessSemantics: "reference"
        prototype: "Qt3DAnimation::QAbstractChannelMapping"
        exports: [
            "Qt3D.Animation/SkeletonMapping 2.10",
            "Qt3D.Animation/SkeletonMapping 6.0"
        ]
        exportMetaObjectRevisions: [522, 1536]
        Property {
            name: "skeleton"
            type: "Qt3DCore::QAbstractSkeleton"
            isPointer: true
            read: "skeleton"
            write: "setSkeleton"
            notify: "skeletonChanged"
            index: 0
        }
        Signal {
            name: "skeletonChanged"
            Parameter { name: "skeleton"; type: "Qt3DCore::QAbstractSkeleton"; isPointer: true }
        }
        Method {
            name: "setSkeleton"
            Parameter { name: "skeleton"; type: "Qt3DCore::QAbstractSkeleton"; isPointer: true }
        }
    }
    Component {
        file: "private/qt3dquickanimationforeign_p.h"
        name: "Qt3DAnimation::QVertexBlendAnimation"
        accessSemantics: "reference"
        prototype: "Qt3DAnimation::QAbstractAnimation"
        extension: "Qt3DAnimation::Quick::QQuick3DVertexBlendAnimation"
        exports: [
            "Qt3D.Animation/VertexBlendAnimation 2.9",
            "Qt3D.Animation/VertexBlendAnimation 6.0"
        ]
        exportMetaObjectRevisions: [521, 1536]
        Property {
            name: "targetPositions"
            type: "float"
            isList: true
            read: "targetPositions"
            write: "setTargetPositions"
            notify: "targetPositionsChanged"
            index: 0
        }
        Property {
            name: "interpolator"
            type: "float"
            read: "interpolator"
            notify: "interpolatorChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "target"
            type: "Qt3DRender::QGeometryRenderer"
            isPointer: true
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 2
        }
        Property {
            name: "targetName"
            type: "QString"
            read: "targetName"
            write: "setTargetName"
            notify: "targetNameChanged"
            index: 3
        }
        Signal {
            name: "targetPositionsChanged"
            Parameter { name: "targetPositions"; type: "float"; isList: true }
        }
        Signal {
            name: "interpolatorChanged"
            Parameter { name: "interpolator"; type: "float" }
        }
        Signal {
            name: "targetChanged"
            Parameter { name: "target"; type: "Qt3DRender::QGeometryRenderer"; isPointer: true }
        }
        Signal {
            name: "targetNameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Method {
            name: "setTargetPositions"
            Parameter { name: "targetPositions"; type: "float"; isList: true }
        }
        Method {
            name: "setTarget"
            Parameter { name: "target"; type: "Qt3DRender::QGeometryRenderer"; isPointer: true }
        }
        Method {
            name: "setTargetName"
            Parameter { name: "name"; type: "QString" }
        }
    }
    Component {
        file: "private/quick3dchannelmapper_p.h"
        name: "Qt3DAnimation::Animation::Quick::Quick3DChannelMapper"
        accessSemantics: "reference"
        defaultProperty: "mappings"
        prototype: "QObject"
        Property {
            name: "mappings"
            type: "Qt3DAnimation::QAbstractChannelMapping"
            isList: true
            read: "qmlMappings"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/quick3danimationcontroller_p.h"
        name: "Qt3DAnimation::Quick::QQuick3DAnimationController"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "animationGroups"
            type: "Qt3DAnimation::QAnimationGroup"
            isList: true
            read: "animationGroups"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3danimationgroup_p.h"
        name: "Qt3DAnimation::Quick::QQuick3DAnimationGroup"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "animations"
            type: "Qt3DAnimation::QAbstractAnimation"
            isList: true
            read: "animations"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dkeyframeanimation_p.h"
        name: "Qt3DAnimation::Quick::QQuick3DKeyframeAnimation"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "keyframes"
            type: "Qt3DCore::QTransform"
            isList: true
            read: "keyframes"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dmorphtarget_p.h"
        name: "Qt3DAnimation::Quick::QQuick3DMorphTarget"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "attributes"
            type: "Qt3DCore::QAttribute"
            isList: true
            read: "attributes"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dmorphinganimation_p.h"
        name: "Qt3DAnimation::Quick::QQuick3DMorphingAnimation"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "morphTargets"
            type: "Qt3DAnimation::QMorphTarget"
            isList: true
            read: "morphTargets"
            index: 0
            isReadonly: true
        }
    }
    Component {
        file: "private/quick3dvertexblendanimation_p.h"
        name: "Qt3DAnimation::Quick::QQuick3DVertexBlendAnimation"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "morphTargets"
            type: "Qt3DAnimation::QMorphTarget"
            isList: true
            read: "morphTargets"
            index: 0
            isReadonly: true
        }
    }
}
