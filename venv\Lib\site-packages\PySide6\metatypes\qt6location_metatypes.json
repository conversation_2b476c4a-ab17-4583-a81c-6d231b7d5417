[{"classes": [{"className": "QAbstractGeoTileCache", "lineNumber": 45, "object": true, "qualifiedClassName": "QAbstractGeoTileCache", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qabstractgeotilecache_p.h", "outputRevision": 69}, {"classes": [{"className": "QGeoCodeReply", "lineNumber": 17, "object": true, "qualifiedClassName": "QGeoCodeReply", "signals": [{"access": "public", "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "index": 1, "name": "aborted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "index": 2, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}], "index": 3, "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeocodereply.h", "outputRevision": 69}, {"classes": [{"className": "QGeoCodingManager", "lineNumber": 20, "object": true, "qualifiedClassName": "QGeoCodingManager", "signals": [{"access": "public", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}], "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}, {"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "index": 1, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}, {"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}], "index": 2, "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeocodingmanager.h", "outputRevision": 69}, {"classes": [{"className": "QGeoCodingManagerEngine", "lineNumber": 18, "object": true, "qualifiedClassName": "QGeoCodingManagerEngine", "signals": [{"access": "public", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}], "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}, {"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "index": 1, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}, {"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}], "index": 2, "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeocodingmanagerengine.h", "outputRevision": 69}, {"classes": [{"className": "QGeoFileTileCache", "lineNumber": 56, "object": true, "qualifiedClassName": "QGeoFileTileCache", "superClasses": [{"access": "public", "name": "QAbstractGeoTileCache"}]}], "inputFile": "qgeofiletilecache_p.h", "outputRevision": 69}, {"classes": [{"className": "QGeoManeuverDerived", "gadget": true, "lineNumber": 26, "qualifiedClassName": "QGeoManeuverDerived", "superClasses": [{"access": "public", "name": "QGeoManeuver"}]}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoManeuverDerived"}, {"name": "QML.ForeignIsNamespace", "value": "true"}, {"name": "QML.Element", "value": "RouteManeuver"}], "className": "QGeoManeuverForeignNamespace", "lineNumber": 31, "namespace": true, "qualifiedClassName": "QGeoManeuverForeignNamespace"}], "inputFile": "qgeomaneuverderived_p.h", "outputRevision": 69}, {"classes": [{"className": "QGeoMap", "enums": [{"isClass": false, "isFlag": false, "name": "Capability", "values": ["SupportsNothing", "SupportsVisibleRegion", "SupportsSetBearing", "SupportsAnchoringCoordinate", "SupportsFittingViewportToGeoRectangle", "SupportsVisibleArea"]}, {"alias": "Capability", "isClass": false, "isFlag": true, "name": "Capabilities", "values": ["SupportsNothing", "SupportsVisibleRegion", "SupportsSetBearing", "SupportsAnchoringCoordinate", "SupportsFittingViewportToGeoRectangle", "SupportsVisibleArea"]}], "lineNumber": 38, "object": true, "qualifiedClassName": "QGeoMap", "signals": [{"access": "public", "arguments": [{"name": "cameraData", "type": "QGeoCameraData"}], "index": 0, "name": "cameraDataChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "sgNodeChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "activeMapTypeChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "oldCameraCapabilities", "type": "QGeoCameraCapabilities"}], "index": 3, "name": "cameraCapabilitiesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "copyrightsImage", "type": "QImage"}], "index": 4, "name": "copyrightsImageChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "copyrightsHtml", "type": "QString"}], "index": 5, "name": "copyrightsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "styleSheet", "type": "QString"}], "index": 6, "name": "copyrightsStyleSheetChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "visibleAreaChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeomap_p.h", "outputRevision": 69}, {"classes": [{"className": "QGeoMappingManager", "lineNumber": 34, "object": true, "qualifiedClassName": "QGeoMappingManager", "signals": [{"access": "public", "index": 0, "name": "initialized", "returnType": "void"}, {"access": "public", "index": 1, "name": "supportedMapTypesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeomappingmanager_p.h", "outputRevision": 69}, {"classes": [{"className": "QGeoMappingManagerEngine", "lineNumber": 37, "object": true, "qualifiedClassName": "QGeoMappingManagerEngine", "signals": [{"access": "public", "index": 0, "name": "initialized", "returnType": "void"}, {"access": "public", "index": 1, "name": "supportedMapTypesChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeomappingmanagerengine_p.h", "outputRevision": 69}, {"classes": [{"className": "QGeoRouteParser", "enums": [{"isClass": false, "isFlag": false, "name": "TrafficSide", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}], "lineNumber": 27, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "trafficSide", "notify": "trafficSideChanged", "read": "trafficSide", "required": false, "scriptable": true, "stored": true, "type": "TrafficSide", "user": false, "write": "setTrafficSide"}], "qualifiedClassName": "QGeoRouteParser", "signals": [{"access": "public", "arguments": [{"name": "trafficSide", "type": "TrafficSide"}], "index": 0, "name": "trafficSideChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "trafficSide", "type": "TrafficSide"}], "index": 1, "name": "setTrafficSide", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeorouteparser_p.h", "outputRevision": 69}, {"classes": [{"className": "QGeoRouteParserOsrmV5", "lineNumber": 41, "object": true, "qualifiedClassName": "QGeoRouteParserOsrmV5", "superClasses": [{"access": "public", "name": "QGeoRouteParser"}]}], "inputFile": "qgeorouteparserosrmv5_p.h", "outputRevision": 69}, {"classes": [{"className": "QGeoRouteReply", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "EngineNotSetError", "CommunicationError", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UnsupportedOptionError", "UnknownE<PERSON>r"]}], "lineNumber": 17, "object": true, "qualifiedClassName": "QGeoRouteReply", "signals": [{"access": "public", "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "index": 1, "name": "aborted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "index": 2, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}], "index": 3, "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeoroutereply.h", "outputRevision": 69}, {"classes": [{"className": "QGeoRoutingManager", "lineNumber": 17, "object": true, "qualifiedClassName": "QGeoRoutingManager", "signals": [{"access": "public", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}], "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}, {"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "index": 1, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}, {"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}], "index": 2, "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeoroutingmanager.h", "outputRevision": 69}, {"classes": [{"className": "QGeoRoutingManagerEngine", "lineNumber": 17, "object": true, "qualifiedClassName": "QGeoRoutingManagerEngine", "signals": [{"access": "public", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}], "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}, {"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "index": 1, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}, {"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}], "index": 2, "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeoroutingmanagerengine.h", "outputRevision": 69}, {"classes": [{"className": "QGeoServiceProvider", "enums": [{"isClass": false, "isFlag": false, "name": "Error", "values": ["NoError", "NotSupportedError", "UnknownParameterError", "MissingRequiredParameterError", "ConnectionError", "LoaderError"]}, {"alias": "RoutingFeature", "isClass": false, "isFlag": true, "name": "RoutingFeatures", "values": ["NoRoutingFeatures", "OnlineRoutingFeature", "OfflineRoutingFeature", "LocalizedRoutingFeature", "RouteUpdatesFeature", "AlternativeRoutesFeature", "ExcludeAreasRoutingFeature", "AnyRoutingFeatures"]}, {"alias": "GeocodingFeature", "isClass": false, "isFlag": true, "name": "GeocodingFeatures", "values": ["NoGeocodingFeatures", "OnlineGeocodingFeature", "OfflineGeocodingFeature", "ReverseGeocodingFeature", "LocalizedGeocodingFeature", "AnyGeocodingFeatures"]}, {"alias": "MappingFeature", "isClass": false, "isFlag": true, "name": "MappingFeatures", "values": ["NoMappingFeatures", "OnlineMappingFeature", "OfflineMappingFeature", "LocalizedMappingFeature", "AnyMappingFeatures"]}, {"alias": "PlacesFeature", "isClass": false, "isFlag": true, "name": "PlacesFeatures", "values": ["NoPlacesFeatures", "OnlinePlacesFeature", "OfflinePlacesFeature", "SavePlaceFeature", "RemovePlaceFeature", "SaveCategoryFeature", "RemoveCategoryFeature", "PlaceRecommendationsFeature", "SearchSuggestionsFeature", "LocalizedPlacesFeature", "NotificationsFeature", "PlaceMatchingFeature", "AnyPlacesFeatures"]}, {"alias": "NavigationFeature", "isClass": false, "isFlag": true, "name": "NavigationFeatures", "values": ["NoNavigationFeatures", "OnlineNavigationFeature", "OfflineNavigationFeature", "AnyNavigationFeatures"]}], "lineNumber": 26, "object": true, "qualifiedClassName": "QGeoServiceProvider", "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeoserviceprovider.h", "outputRevision": 69}, {"classes": [{"className": "QGeoTiledMap", "lineNumber": 36, "object": true, "qualifiedClassName": "QGeoTiledMap", "slots": [{"access": "public", "arguments": [{"name": "mapId", "type": "int"}], "index": 0, "name": "clearScene", "returnType": "void"}, {"access": "private", "index": 1, "name": "handleTileVersionChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGeoMap"}]}], "inputFile": "qgeotiledmap_p.h", "outputRevision": 69}, {"classes": [{"className": "QGeoTiledMappingManagerEngine", "lineNumber": 33, "object": true, "qualifiedClassName": "QGeoTiledMappingManagerEngine", "signals": [{"access": "public", "arguments": [{"name": "spec", "type": "QGeoTileSpec"}, {"name": "errorString", "type": "QString"}], "index": 0, "name": "tileError", "returnType": "void"}, {"access": "public", "index": 1, "name": "tileVersionChanged", "returnType": "void"}], "slots": [{"access": "protected", "arguments": [{"name": "spec", "type": "QGeoTileSpec"}, {"name": "bytes", "type": "QByteArray"}, {"name": "format", "type": "QString"}], "index": 2, "name": "engineTileFinished", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "spec", "type": "QGeoTileSpec"}, {"name": "errorString", "type": "QString"}], "index": 3, "name": "engineTileError", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QGeoMappingManagerEngine"}]}], "inputFile": "qgeotiledmappingmanagerengine_p.h", "outputRevision": 69}, {"classes": [{"className": "QGeoTiledMapReply", "lineNumber": 27, "object": true, "qualifiedClassName": "QGeoTiledMapReply", "signals": [{"access": "public", "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "index": 1, "name": "aborted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QGeoTiledMapReply::Error"}, {"name": "errorString", "type": "QString"}], "index": 2, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QGeoTiledMapReply::Error"}], "index": 3, "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeotiledmapreply_p.h", "outputRevision": 69}, {"classes": [{"className": "QGeoTiledMapScene", "lineNumber": 30, "object": true, "qualifiedClassName": "QGeoTiledMapScene", "signals": [{"access": "public", "arguments": [{"name": "newTiles", "type": "QSet<QGeoTileSpec>"}], "index": 0, "name": "newTilesVisible", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeotiledmapscene_p.h", "outputRevision": 69}, {"classes": [{"className": "QGeoTileFetcher", "lineNumber": 30, "object": true, "qualifiedClassName": "QGeoTileFetcher", "signals": [{"access": "public", "arguments": [{"name": "spec", "type": "QGeoTileSpec"}, {"name": "bytes", "type": "QByteArray"}, {"name": "format", "type": "QString"}], "index": 0, "name": "tileFinished", "returnType": "void"}, {"access": "public", "arguments": [{"name": "spec", "type": "QGeoTileSpec"}, {"name": "errorString", "type": "QString"}], "index": 1, "name": "tileError", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "tilesAdded", "type": "QSet<QGeoTileSpec>"}, {"name": "tilesRemoved", "type": "QSet<QGeoTileSpec>"}], "index": 2, "name": "updateTileRequests", "returnType": "void"}, {"access": "private", "arguments": [{"name": "tiles", "type": "QSet<QGeoTileSpec>"}], "index": 3, "name": "cancelTileRequests", "returnType": "void"}, {"access": "private", "index": 4, "name": "requestNextTile", "returnType": "void"}, {"access": "private", "index": 5, "name": "finished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeotilefetcher_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Category"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeCategory", "enums": [{"isClass": false, "isFlag": false, "name": "Visibility", "values": ["UnspecifiedVisibility", "DeviceVisibility", "PrivateVisibility", "PublicVisibility"]}, {"isClass": false, "isFlag": false, "name": "Status", "values": ["Ready", "Saving", "Removing", "Error"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 33, "methods": [{"access": "public", "index": 8, "isConst": true, "name": "errorString", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "parentId", "type": "QString"}], "index": 9, "name": "save", "returnType": "void"}, {"access": "public", "index": 10, "isCloned": true, "name": "save", "returnType": "void"}, {"access": "public", "index": 11, "name": "remove", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "category", "read": "category", "required": false, "scriptable": true, "stored": true, "type": "QPlaceCategory", "user": false, "write": "setCategory"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "categoryId", "notify": "categoryIdChanged", "read": "categoryId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setCategoryId"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "visibility", "notify": "visibilityChanged", "read": "visibility", "required": false, "scriptable": true, "stored": true, "type": "Visibility", "user": false, "write": "setVisibility"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "icon", "notify": "iconChanged", "read": "icon", "required": false, "scriptable": true, "stored": true, "type": "QPlaceIcon", "user": false, "write": "setIcon"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "QDeclarativeCategory", "signals": [{"access": "public", "index": 0, "name": "pluginChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "categoryIdChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "nameChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "visibilityChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "iconChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "statusChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 6, "name": "replyFinished", "returnType": "void"}, {"access": "private", "index": 7, "name": "pluginReady", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativecategory_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ContactDetails"}, {"name": "QML.AddedInVersion", "value": "1280"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "ContactDetails instances cannot be instantiated. Only Place types have ContactDetails and they cannot be re-assigned (but can be modified)."}], "className": "QDeclarativeContactDetails", "lineNumber": 24, "object": true, "qualifiedClassName": "QDeclarativeContactDetails", "superClasses": [{"access": "public", "name": "QQmlPropertyMap"}]}], "inputFile": "qdeclarativecontactdetails_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Place"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativePlace", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["Ready", "Saving", "Fetching", "Removing", "Error"]}, {"isClass": false, "isFlag": false, "name": "Visibility", "values": ["UnspecifiedVisibility", "DeviceVisibility", "PrivateVisibility", "PublicVisibility"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 38, "methods": [{"access": "public", "index": 26, "name": "getDetails", "returnType": "void"}, {"access": "public", "index": 27, "name": "save", "returnType": "void"}, {"access": "public", "index": 28, "name": "remove", "returnType": "void"}, {"access": "public", "index": 29, "isConst": true, "name": "errorString", "returnType": "QString"}, {"access": "public", "arguments": [{"name": "original", "type": "QDeclarativePlace*"}], "index": 30, "name": "copyFrom", "returnType": "void"}, {"access": "public", "arguments": [{"name": "plugin", "type": "QDeclarativeGeoServiceProvider*"}], "index": 31, "name": "initializeFavorite", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "place", "read": "place", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON>lace", "user": false, "write": "setPlace"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "categories", "notify": "categoriesChanged", "read": "categories", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QDeclarativeCategory>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "location", "notify": "locationChanged", "read": "location", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoLocation*", "user": false, "write": "setLocation"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "ratings", "notify": "ratingsChanged", "read": "ratings", "required": false, "scriptable": true, "stored": true, "type": "QPlaceRatings", "user": false, "write": "setRatings"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "supplier", "notify": "supplierChanged", "read": "supplier", "required": false, "scriptable": true, "stored": true, "type": "QPlaceSupplier", "user": false, "write": "setSupplier"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "icon", "notify": "iconChanged", "read": "icon", "required": false, "scriptable": true, "stored": true, "type": "QPlaceIcon", "user": false, "write": "setIcon"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "placeId", "notify": "placeIdChanged", "read": "placeId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setPlaceId"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "attribution", "notify": "attributionChanged", "read": "attribution", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setAttribution"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "reviewModel", "notify": "reviewModelChanged", "read": "reviewModel", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativePlaceReviewModel*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "imageModel", "notify": "imageModelChanged", "read": "imageModel", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativePlaceImageModel*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "editorialModel", "notify": "editorialModelChanged", "read": "editorialModel", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativePlaceEditorialModel*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "extendedAttributes", "notify": "extendedAttributesChanged", "read": "extendedAttributes", "required": false, "scriptable": true, "stored": true, "type": "QObject*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "contactDetails", "notify": "contactDetailsChanged", "read": "contactDetails", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeContactDetails*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "detailsFetched", "notify": "detailsFetchedChanged", "read": "detailsFetched", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "primaryPhone", "notify": "primaryPhoneChanged", "read": "primaryPhone", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "primaryFax", "notify": "primaryFaxChanged", "read": "primaryFax", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "primaryEmail", "notify": "primaryEmailChanged", "read": "primaryEmail", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "primaryWebsite", "notify": "primaryWebsiteChanged", "read": "primaryWebsite", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "visibility", "notify": "visibilityChanged", "read": "visibility", "required": false, "scriptable": true, "stored": true, "type": "Visibility", "user": false, "write": "setVisibility"}, {"constant": false, "designable": true, "final": false, "index": 22, "name": "favorite", "notify": "favoriteChanged", "read": "favorite", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativePlace*", "user": false, "write": "setFavorite"}], "qualifiedClassName": "QDeclarativePlace", "signals": [{"access": "public", "index": 0, "name": "pluginChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "categoriesChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "locationChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "ratingsChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "supplierChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "iconChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "nameChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "placeIdChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "attributionChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "detailsFetchedChanged", "returnType": "void"}, {"access": "public", "index": 10, "name": "reviewModelChanged", "returnType": "void"}, {"access": "public", "index": 11, "name": "imageModelChanged", "returnType": "void"}, {"access": "public", "index": 12, "name": "editorialModelChanged", "returnType": "void"}, {"access": "public", "index": 13, "name": "primaryPhoneChanged", "returnType": "void"}, {"access": "public", "index": 14, "name": "primaryFaxChanged", "returnType": "void"}, {"access": "public", "index": 15, "name": "primaryEmailChanged", "returnType": "void"}, {"access": "public", "index": 16, "name": "primaryWebsiteChanged", "returnType": "void"}, {"access": "public", "index": 17, "name": "extendedAttributesChanged", "returnType": "void"}, {"access": "public", "index": 18, "name": "contactDetailsChanged", "returnType": "void"}, {"access": "public", "index": 19, "name": "statusChanged", "returnType": "void"}, {"access": "public", "index": 20, "name": "visibilityChanged", "returnType": "void"}, {"access": "public", "index": 21, "name": "favoriteChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 22, "name": "finished", "returnType": "void"}, {"access": "private", "arguments": [{"type": "QString"}, {"type": "Q<PERSON><PERSON><PERSON>"}], "index": 23, "name": "contactsModified", "returnType": "void"}, {"access": "private", "index": 24, "name": "pluginReady", "returnType": "void"}, {"access": "private", "index": 25, "name": "cleanupDeletedCategories", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativeplace_p.h", "outputRevision": 69}, {"classes": [{"className": "QDeclarativePlaceContentModel", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 34, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "place", "notify": "placeChanged", "read": "place", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativePlace*", "user": false, "write": "setPlace"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "batchSize", "notify": "batchSizeChanged", "read": "batchSize", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setBatchSize"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "totalCount", "notify": "totalCountChanged", "read": "totalCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}], "qualifiedClassName": "QDeclarativePlaceContentModel", "signals": [{"access": "public", "index": 0, "name": "placeChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "batchSizeChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "totalCountChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 3, "name": "fetchFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractListModel"}, {"access": "public", "name": "QQmlParserStatus"}]}, {"classInfos": [{"name": "QML.Element", "value": "ReviewModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativePlaceReviewModel", "gadget": true, "lineNumber": 115, "qualifiedClassName": "QDeclarativePlaceReviewModel", "superClasses": [{"access": "public", "name": "QDeclarativePlaceContentModel"}]}, {"classInfos": [{"name": "QML.Element", "value": "EditorialModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativePlaceEditorialModel", "gadget": true, "lineNumber": 127, "qualifiedClassName": "QDeclarativePlaceEditorialModel", "superClasses": [{"access": "public", "name": "QDeclarativePlaceContentModel"}]}, {"classInfos": [{"name": "QML.Element", "value": "ImageModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativePlaceImageModel", "gadget": true, "lineNumber": 138, "qualifiedClassName": "QDeclarativePlaceImageModel", "superClasses": [{"access": "public", "name": "QDeclarativePlaceContentModel"}]}], "inputFile": "qdeclarativeplacecontentmodel_p.h", "outputRevision": 69}, {"classes": [{"className": "QDeclarativeSearchModelBase", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 33, "methods": [{"access": "public", "index": 9, "name": "update", "returnType": "void"}, {"access": "public", "index": 10, "name": "cancel", "returnType": "void"}, {"access": "public", "index": 11, "name": "reset", "returnType": "void"}, {"access": "public", "index": 12, "isConst": true, "name": "errorString", "returnType": "QString"}, {"access": "public", "index": 13, "name": "previousPage", "returnType": "void"}, {"access": "public", "index": 14, "name": "nextPage", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "searchArea", "notify": "searchAreaChanged", "read": "searchArea", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setSearchArea"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "limit", "notify": "limitChanged", "read": "limit", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLimit"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "previousPagesAvailable", "notify": "previousPagesAvailableChanged", "read": "previousPagesAvailable", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "nextPagesAvailable", "notify": "nextPagesAvailableChanged", "read": "nextPagesAvailable", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "QDeclarativeSearchModelBase", "signals": [{"access": "public", "index": 0, "name": "pluginChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "searchAreaChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "limitChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "previousPagesAvailableChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "nextPagesAvailableChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "statusChanged", "returnType": "void"}], "slots": [{"access": "protected", "index": 6, "name": "queryFinished", "returnType": "void"}, {"access": "protected", "index": 7, "name": "onContentUpdated", "returnType": "void"}, {"access": "private", "index": 8, "name": "pluginNameChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractListModel"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativesearchmodelbase_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PlaceSearchModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeSearchResultModel", "enums": [{"isClass": false, "isFlag": false, "name": "SearchResultType", "values": ["UnknownSearchResult", "PlaceResult", "ProposedSearchResult"]}, {"isClass": false, "isFlag": false, "name": "RelevanceHint", "values": ["UnspecifiedHint", "DistanceHint", "LexicalPlaceNameHint"]}], "lineNumber": 28, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "<PERSON><PERSON><PERSON>", "type": "QString"}], "index": 16, "isConst": true, "name": "data", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "arguments": [{"name": "proposedSearchIndex", "type": "int"}], "index": 17, "name": "updateWith", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "searchTerm", "notify": "searchTermChanged", "read": "searchTerm", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setSearchTerm"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "categories", "notify": "categoriesChanged", "read": "categories", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QDeclarativeCategory>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "recommendationId", "notify": "recommendationIdChanged", "read": "recommendationId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setRecommendationId"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "relevanceHint", "notify": "relevanceHintChanged", "read": "relevanceHint", "required": false, "scriptable": true, "stored": true, "type": "RelevanceHint", "user": false, "write": "setRelevanceHint"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "visibilityScope", "notify": "visibilityScopeChanged", "read": "visibilityScope", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativePlace::Visibility", "user": false, "write": "setVisibilityScope"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "count", "notify": "rowCountChanged", "read": "rowCount", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "favoritesPlugin", "notify": "favorites<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "favoritesPlugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setFavoritesPlugin"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "favoritesMatchParameters", "notify": "favoritesMatchParametersChanged", "read": "favoritesMatchParameters", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false, "write": "setFavoritesMatchParameters"}, {"constant": false, "designable": true, "final": false, "index": 8, "member": "m_incremental", "name": "incremental", "notify": "incrementalChanged", "required": false, "revision": 1292, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QDeclarativeSearchResultModel", "signals": [{"access": "public", "index": 0, "name": "searchTermChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "categoriesChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "recommendationIdChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "relevanceHintChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "visibilityScopeChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "rowCountChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "favorites<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 7, "name": "favoritesMatchParametersChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "dataChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "incrementalChanged", "returnType": "void"}], "slots": [{"access": "protected", "index": 10, "name": "queryFinished", "returnType": "void"}, {"access": "protected", "index": 11, "name": "onContentUpdated", "returnType": "void"}, {"access": "private", "arguments": [{"name": "favoritePlaces", "type": "QList<QPlace>"}], "index": 12, "name": "updateLayout", "returnType": "void"}, {"access": "private", "index": 13, "isCloned": true, "name": "updateLayout", "returnType": "void"}, {"access": "private", "arguments": [{"name": "placeId", "type": "QString"}], "index": 14, "name": "placeUpdated", "returnType": "void"}, {"access": "private", "arguments": [{"name": "placeId", "type": "QString"}], "index": 15, "name": "placeRemoved", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeSearchModelBase"}]}], "inputFile": "qdeclarativesearchresultmodel_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "PlaceSearchSuggestionModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeSearchSuggestionModel", "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "searchTerm", "notify": "searchTermChanged", "read": "searchTerm", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setSearchTerm"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "suggestions", "notify": "suggestions<PERSON><PERSON><PERSON>", "read": "suggestions", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}], "qualifiedClassName": "QDeclarativeSearchSuggestionModel", "signals": [{"access": "public", "index": 0, "name": "searchTermChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "suggestions<PERSON><PERSON><PERSON>", "returnType": "void"}], "slots": [{"access": "protected", "index": 2, "name": "queryFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeSearchModelBase"}]}], "inputFile": "qdeclarativesearchsuggestionmodel_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "CategoryModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeSupportedCategoriesModel", "enums": [{"isClass": false, "isFlag": false, "name": "Roles", "values": ["CategoryRole", "ParentCategoryRole"]}, {"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 38, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "QModelIndex"}, {"name": "role", "type": "int"}], "index": 10, "isConst": true, "name": "data", "returnType": "Q<PERSON><PERSON><PERSON>"}, {"access": "public", "index": 11, "isConst": true, "name": "errorString", "returnType": "QString"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "hierarchical", "notify": "hierarchicalChanged", "read": "hierarchical", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setHierarchical"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}], "qualifiedClassName": "QDeclarativeSupportedCategoriesModel", "signals": [{"access": "public", "index": 0, "name": "pluginChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "hierarchicalChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "statusChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "dataChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 4, "name": "update", "returnType": "void"}, {"access": "private", "index": 5, "name": "replyFinished", "returnType": "void"}, {"access": "private", "arguments": [{"name": "category", "type": "QPlaceCategory"}, {"name": "parentId", "type": "QString"}], "index": 6, "name": "addedCategory", "returnType": "void"}, {"access": "private", "arguments": [{"name": "category", "type": "QPlaceCategory"}, {"name": "parentId", "type": "QString"}], "index": 7, "name": "updatedCategory", "returnType": "void"}, {"access": "private", "arguments": [{"name": "categoryId", "type": "QString"}, {"name": "parentId", "type": "QString"}], "index": 8, "name": "removedCategory", "returnType": "void"}, {"access": "private", "index": 9, "name": "connectNotificationSignals", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractItemModel"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativesupportedcategoriesmodel_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "GeocodeModel"}], "className": "QDeclarativeGeocodeModel", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}, {"isClass": false, "isFlag": false, "name": "GeocodeError", "values": ["NoError", "EngineNotSetError", "CommunicationError", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UnsupportedOptionError", "CombinationError", "UnknownE<PERSON>r", "UnknownParameterError", "MissingRequiredParameterError"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 37, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 15, "name": "get", "returnType": "QDeclarativeGeoLocation*"}, {"access": "public", "index": 16, "name": "reset", "returnType": "void"}, {"access": "public", "index": 17, "name": "cancel", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "autoUpdate", "notify": "autoUpdateChanged", "read": "autoUpdate", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoUpdate"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "limit", "notify": "limitChanged", "read": "limit", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLimit"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "offset", "notify": "offsetChanged", "read": "offset", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setOffset"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "query", "notify": "queryChanged", "read": "query", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "bounds", "notify": "boundsChanged", "read": "bounds", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setBounds"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "GeocodeError", "user": false}], "qualifiedClassName": "QDeclarativeGeocodeModel", "signals": [{"access": "public", "index": 0, "name": "countChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pluginChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "statusChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "errorChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "locationsChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "autoUpdateChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "boundsChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "queryChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "limitChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "offsetChanged", "returnType": "void"}], "slots": [{"access": "public", "index": 10, "name": "update", "returnType": "void"}, {"access": "protected", "index": 11, "name": "queryContentChanged", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}], "index": 12, "name": "geocodeFinished", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "reply", "type": "QGeoCodeReply*"}, {"name": "error", "type": "QGeoCodeReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "index": 13, "name": "geocodeError", "returnType": "void"}, {"access": "protected", "index": 14, "name": "pluginReady", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractListModel"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativegeocodemodel_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.AddedInVersion", "value": "1543"}, {"name": "QML.Element", "value": "GeoJsonData"}], "className": "QDeclarativeGeoJsonData", "lineNumber": 37, "methods": [{"access": "public", "index": 2, "name": "clear", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QQuickItem*"}], "index": 3, "name": "addItem", "returnType": "void"}, {"access": "public", "index": 4, "name": "open", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 5, "name": "openUrl", "returnType": "bool"}, {"access": "public", "index": 6, "name": "save", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "url", "type": "QUrl"}], "index": 7, "name": "saveAs", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "map", "type": "QDeclarativeGeoMap*"}], "index": 8, "name": "setModelToMapContents", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "model", "notify": "modelChanged", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "sourceUrl", "notify": "sourceUrlChanged", "read": "sourceUrl", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "openUrl"}], "qualifiedClassName": "QDeclarativeGeoJsonData", "signals": [{"access": "public", "index": 0, "name": "modelChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "sourceUrlChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdeclarativegeojsondata_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "RouteModel"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoRouteModel", "enums": [{"isClass": false, "isFlag": false, "name": "Status", "values": ["<PERSON><PERSON>", "Ready", "Loading", "Error"]}, {"isClass": false, "isFlag": false, "name": "RouteError", "values": ["NoError", "EngineNotSetError", "CommunicationError", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UnsupportedOptionError", "UnknownE<PERSON>r", "UnknownParameterError", "MissingRequiredParameterError"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 39, "methods": [{"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 14, "name": "get", "returnType": "QGeoRoute"}, {"access": "public", "index": 15, "name": "reset", "returnType": "void"}, {"access": "public", "index": 16, "name": "cancel", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "query", "notify": "queryChanged", "read": "query", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoRouteQuery*", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "count", "notify": "countChanged", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "autoUpdate", "notify": "autoUpdateChanged", "read": "autoUpdate", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoUpdate"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "status", "notify": "statusChanged", "read": "status", "required": false, "scriptable": true, "stored": true, "type": "Status", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "RouteError", "user": false}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "measurementSystem", "notify": "measurementSystemChanged", "read": "measurementSystem", "required": false, "scriptable": true, "stored": true, "type": "QLocale::MeasurementSystem", "user": false, "write": "setMeasurementSystem"}], "qualifiedClassName": "QDeclarativeGeoRouteModel", "signals": [{"access": "public", "index": 0, "name": "countChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "pluginChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "queryChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "autoUpdateChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "statusChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "errorChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "routesChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "measurementSystemChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "abortRequested", "returnType": "void"}], "slots": [{"access": "public", "index": 9, "name": "update", "returnType": "void"}, {"access": "private", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}], "index": 10, "name": "routingFinished", "returnType": "void"}, {"access": "private", "arguments": [{"name": "reply", "type": "QGeoRouteReply*"}, {"name": "error", "type": "QGeoRouteReply::<PERSON><PERSON>r"}, {"name": "errorString", "type": "QString"}], "index": 11, "name": "routingError", "returnType": "void"}, {"access": "private", "index": 12, "name": "queryDetailsChanged", "returnType": "void"}, {"access": "private", "index": 13, "name": "pluginReady", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QAbstractListModel"}, {"access": "public", "name": "QQmlParserStatus"}]}, {"classInfos": [{"name": "QML.Element", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoRouteQuery", "enums": [{"isClass": false, "isFlag": false, "name": "TravelMode", "values": ["CarTravel", "PedestrianTravel", "BicycleTravel", "PublicTransitTravel", "TruckTravel"]}, {"alias": "TravelMode", "isClass": false, "isFlag": true, "name": "TravelModes", "values": ["CarTravel", "PedestrianTravel", "BicycleTravel", "PublicTransitTravel", "TruckTravel"]}, {"isClass": false, "isFlag": false, "name": "FeatureType", "values": ["NoFeature", "TollFeature", "HighwayFeature", "PublicTransitFeature", "FerryFeature", "TunnelFeature", "DirtRoadFeature", "ParksFeature", "MotorPoolLaneFeature", "TrafficFeature"]}, {"isClass": false, "isFlag": false, "name": "FeatureWeight", "values": ["NeutralFeatureWeight", "PreferFeatureWeight", "RequireFeatureWeight", "AvoidFeatureWeight", "DisallowFeatureWeight"]}, {"isClass": false, "isFlag": false, "name": "RouteOptimization", "values": ["ShortestRoute", "FastestRoute", "MostEconomicRoute", "MostScenicRoute"]}, {"alias": "RouteOptimization", "isClass": false, "isFlag": true, "name": "RouteOptimizations", "values": ["ShortestRoute", "FastestRoute", "MostEconomicRoute", "MostScenicRoute"]}, {"isClass": false, "isFlag": false, "name": "SegmentDetail", "values": ["NoSegmentData", "BasicSegmentData"]}, {"alias": "SegmentDetail", "isClass": false, "isFlag": true, "name": "SegmentDetails", "values": ["NoSegmentData", "BasicSegmentData"]}, {"isClass": false, "isFlag": false, "name": "ManeuverDetail", "values": ["NoManeuvers", "BasicManeuvers"]}, {"alias": "ManeuverDetail", "isClass": false, "isFlag": true, "name": "ManeuverDetails", "values": ["NoManeuvers", "BasicManeuvers"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 154, "methods": [{"access": "public", "arguments": [{"name": "w", "type": "QGeoCoordinate"}], "index": 12, "name": "addWaypoint", "returnType": "void"}, {"access": "public", "arguments": [{"name": "waypoint", "type": "QGeoCoordinate"}], "index": 13, "name": "removeWaypoint", "returnType": "void"}, {"access": "public", "index": 14, "name": "clearWaypoints", "returnType": "void"}, {"access": "public", "arguments": [{"name": "area", "type": "QGeoRectangle"}], "index": 15, "name": "addExcludedArea", "returnType": "void"}, {"access": "public", "arguments": [{"name": "area", "type": "QGeoRectangle"}], "index": 16, "name": "removeExcludedArea", "returnType": "void"}, {"access": "public", "index": 17, "name": "clearExcludedAreas", "returnType": "void"}, {"access": "public", "arguments": [{"name": "featureType", "type": "FeatureType"}, {"name": "featureWeight", "type": "FeatureWeight"}], "index": 18, "name": "setFeatureWeight", "returnType": "void"}, {"access": "public", "arguments": [{"name": "featureType", "type": "FeatureType"}], "index": 19, "name": "featureWeight", "returnType": "int"}, {"access": "public", "index": 20, "name": "resetFeatureWeights", "returnType": "void"}, {"access": "private", "index": 21, "name": "doCoordinateChanged", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "numberAlternativeRoutes", "notify": "numberAlternativeRoutesChanged", "read": "numberAlternativeRoutes", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setNumberAlternativeRoutes"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "travelModes", "notify": "travelModesChanged", "read": "travelModes", "required": false, "scriptable": true, "stored": true, "type": "TravelModes", "user": false, "write": "setTravelModes"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "routeOptimizations", "notify": "routeOptimizationsChanged", "read": "routeOptimizations", "required": false, "scriptable": true, "stored": true, "type": "RouteOptimizations", "user": false, "write": "setRouteOptimizations"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "segmentDetail", "notify": "segmentDetailChanged", "read": "segmentDetail", "required": false, "scriptable": true, "stored": true, "type": "SegmentDetail", "user": false, "write": "setSegmentDetail"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "maneuverDetail", "notify": "maneuverDetailChanged", "read": "maneuverDetail", "required": false, "scriptable": true, "stored": true, "type": "ManeuverDetail", "user": false, "write": "setManeuverDetail"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "waypoints", "notify": "waypointsChanged", "read": "waypoints", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoCoordinate>", "user": false, "write": "setWaypoints"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "<PERSON><PERSON><PERSON><PERSON>", "notify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoRectangle>", "user": false, "write": "setExcludedAreas"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "featureTypes", "notify": "featureTypesChanged", "read": "featureTypes", "required": false, "scriptable": true, "stored": true, "type": "QList<int>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "departureTime", "notify": "departureTimeChanged", "read": "departureTime", "required": false, "revision": 1293, "scriptable": true, "stored": true, "type": "QDateTime", "user": false, "write": "setDepartureTime"}], "qualifiedClassName": "QDeclarativeGeoRouteQuery", "signals": [{"access": "public", "index": 0, "name": "numberAlternativeRoutesChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "travelModesChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "routeOptimizationsChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "waypointsChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 5, "name": "featureTypesChanged", "returnType": "void"}, {"access": "public", "index": 6, "name": "maneuverDetailChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "segmentDetailChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "queryDetailsChanged", "returnType": "void"}, {"access": "public", "index": 9, "name": "departureTimeChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 10, "name": "excludedAreaCoordinateChanged", "returnType": "void"}, {"access": "private", "index": 11, "name": "waypointChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}], "inputFile": "qdeclarativegeoroutemodel_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Foreign", "value": "QDeclarativePluginParameter"}, {"name": "QML.Element", "value": "PluginParameter"}], "className": "QDeclarativePluginParameterForeign", "gadget": true, "lineNumber": 36, "qualifiedClassName": "QDeclarativePluginParameterForeign"}, {"classInfos": [{"name": "QML.Element", "value": "Plugin"}, {"name": "QML.AddedInVersion", "value": "1280"}, {"name": "DefaultProperty", "value": "parameters"}], "className": "QDeclarativeGeoServiceProvider", "enums": [{"isClass": false, "isFlag": false, "name": "RoutingFeature", "values": ["NoRoutingFeatures", "OnlineRoutingFeature", "OfflineRoutingFeature", "LocalizedRoutingFeature", "RouteUpdatesFeature", "AlternativeRoutesFeature", "ExcludeAreasRoutingFeature", "AnyRoutingFeatures"]}, {"alias": "RoutingFeature", "isClass": false, "isFlag": true, "name": "RoutingFeatures", "values": ["NoRoutingFeatures", "OnlineRoutingFeature", "OfflineRoutingFeature", "LocalizedRoutingFeature", "RouteUpdatesFeature", "AlternativeRoutesFeature", "ExcludeAreasRoutingFeature", "AnyRoutingFeatures"]}, {"isClass": false, "isFlag": false, "name": "GeocodingFeature", "values": ["NoGeocodingFeatures", "OnlineGeocodingFeature", "OfflineGeocodingFeature", "ReverseGeocodingFeature", "LocalizedGeocodingFeature", "AnyGeocodingFeatures"]}, {"alias": "GeocodingFeature", "isClass": false, "isFlag": true, "name": "GeocodingFeatures", "values": ["NoGeocodingFeatures", "OnlineGeocodingFeature", "OfflineGeocodingFeature", "ReverseGeocodingFeature", "LocalizedGeocodingFeature", "AnyGeocodingFeatures"]}, {"isClass": false, "isFlag": false, "name": "MappingFeature", "values": ["NoMappingFeatures", "OnlineMappingFeature", "OfflineMappingFeature", "LocalizedMappingFeature", "AnyMappingFeatures"]}, {"alias": "MappingFeature", "isClass": false, "isFlag": true, "name": "MappingFeatures", "values": ["NoMappingFeatures", "OnlineMappingFeature", "OfflineMappingFeature", "LocalizedMappingFeature", "AnyMappingFeatures"]}, {"isClass": false, "isFlag": false, "name": "PlacesFeature", "values": ["NoPlacesFeatures", "OnlinePlacesFeature", "OfflinePlacesFeature", "SavePlaceFeature", "RemovePlaceFeature", "SaveCategoryFeature", "RemoveCategoryFeature", "PlaceRecommendationsFeature", "SearchSuggestionsFeature", "LocalizedPlacesFeature", "NotificationsFeature", "PlaceMatchingFeature", "AnyPlacesFeatures"]}, {"alias": "PlacesFeature", "isClass": false, "isFlag": true, "name": "PlacesFeatures", "values": ["NoPlacesFeatures", "OnlinePlacesFeature", "OfflinePlacesFeature", "SavePlaceFeature", "RemovePlaceFeature", "SaveCategoryFeature", "RemoveCategoryFeature", "PlaceRecommendationsFeature", "SearchSuggestionsFeature", "LocalizedPlacesFeature", "NotificationsFeature", "PlaceMatchingFeature", "AnyPlacesFeatures"]}, {"alias": "NavigationFeature", "isClass": false, "isFlag": true, "name": "NavigationFeatures", "values": ["NoNavigationFeatures", "OnlineNavigationFeature", "OfflineNavigationFeature", "AnyNavigationFeatures"]}], "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 43, "methods": [{"access": "public", "arguments": [{"name": "feature", "type": "RoutingFeatures"}], "index": 5, "isConst": true, "name": "supportsRouting", "returnType": "bool"}, {"access": "public", "index": 6, "isCloned": true, "isConst": true, "name": "supportsRouting", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "feature", "type": "GeocodingFeatures"}], "index": 7, "isConst": true, "name": "supportsGeocoding", "returnType": "bool"}, {"access": "public", "index": 8, "isCloned": true, "isConst": true, "name": "supportsGeocoding", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "feature", "type": "MappingFeatures"}], "index": 9, "isConst": true, "name": "supportsMapping", "returnType": "bool"}, {"access": "public", "index": 10, "isCloned": true, "isConst": true, "name": "supportsMapping", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "feature", "type": "PlacesFeatures"}], "index": 11, "isConst": true, "name": "supportsPlaces", "returnType": "bool"}, {"access": "public", "index": 12, "isCloned": true, "isConst": true, "name": "supportsPlaces", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "feature", "type": "NavigationFeature"}], "index": 13, "isConst": true, "name": "supportsNavigation", "returnType": "bool", "revision": 1291}, {"access": "public", "index": 14, "isCloned": true, "isConst": true, "name": "supportsNavigation", "returnType": "bool", "revision": 1291}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "name", "notify": "nameChanged", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "availableServiceProviders", "read": "availableServiceProviders", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "parameters", "read": "parameters", "required": false, "scriptable": true, "stored": true, "type": "QQmlListProperty<QDeclarativePluginParameter>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "required", "read": "requirements", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProviderRequirements*", "user": false, "write": "setRequirements"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "locales", "notify": "localesChanged", "read": "locales", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setLocales"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "preferred", "notify": "preferredChanged", "read": "preferred", "required": false, "scriptable": true, "stored": true, "type": "QStringList", "user": false, "write": "setPreferred"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "allowExperimental", "notify": "allowExperimentalChanged", "read": "allowExperimental", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAllowExperimental"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "isAttached", "notify": "attached", "read": "isAttached", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}], "qualifiedClassName": "QDeclarativeGeoServiceProvider", "signals": [{"access": "public", "arguments": [{"name": "name", "type": "QString"}], "index": 0, "name": "nameChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "localesChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "attached", "returnType": "void"}, {"access": "public", "arguments": [{"name": "preferences", "type": "QStringList"}], "index": 3, "name": "preferredChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "allow", "type": "bool"}], "index": 4, "name": "allowExperimentalChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}, {"access": "public", "name": "QQmlParserStatus"}]}, {"classInfos": [{"name": "QML.Element", "value": "PluginRequirements"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "PluginRequirements is not intended instantiable by developer."}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoServiceProviderRequirements", "lineNumber": 195, "methods": [{"access": "public", "arguments": [{"name": "provider", "type": "const QGeoServiceProvider*"}], "index": 6, "isConst": true, "name": "matches", "returnType": "bool"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "mapping", "notify": "mappingRequirementsChanged", "read": "mappingRequirements", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider::MappingFeatures", "user": false, "write": "setMappingRequirements"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "routing", "notify": "routingRequirementsChanged", "read": "routingRequirements", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider::RoutingFeatures", "user": false, "write": "setRoutingRequirements"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "geocoding", "notify": "geocodingRequirementsChanged", "read": "geocodingRequirements", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider::GeocodingFeatures", "user": false, "write": "setGeocodingRequirements"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "places", "notify": "placesRequirementsChanged", "read": "placesRequirements", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider::PlacesFeatures", "user": false, "write": "setPlacesRequirements"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "navigation", "notify": "navigationRequirementsChanged", "read": "navigationRequirements", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider::NavigationFeatures", "user": false, "write": "setNavigationRequirements"}], "qualifiedClassName": "QDeclarativeGeoServiceProviderRequirements", "signals": [{"access": "public", "arguments": [{"name": "features", "type": "QDeclarativeGeoServiceProvider::MappingFeatures"}], "index": 0, "name": "mappingRequirementsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "features", "type": "QDeclarativeGeoServiceProvider::RoutingFeatures"}], "index": 1, "name": "routingRequirementsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "features", "type": "QDeclarativeGeoServiceProvider::GeocodingFeatures"}], "index": 2, "name": "geocodingRequirementsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "features", "type": "QDeclarativeGeoServiceProvider::PlacesFeatures"}], "index": 3, "name": "placesRequirementsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "features", "type": "QDeclarativeGeoServiceProvider::NavigationFeatures"}], "index": 4, "name": "navigationRequirementsChanged", "returnType": "void"}, {"access": "public", "index": 5, "name": "requirementsChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qdeclarativegeoserviceprovider_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "QtLocation"}, {"name": "QML.AddedInVersion", "value": "1542"}, {"name": "RegisterEnumClassesUnscoped", "value": "false"}], "className": "QLocation", "enums": [{"isClass": true, "isFlag": false, "name": "ReferenceSurface", "type": "uint8_t", "values": ["Map", "Globe"]}], "lineNumber": 25, "namespace": true, "qualifiedClassName": "QLocation"}], "inputFile": "qlocationglobal_p.h", "outputRevision": 69}, {"classes": [{"className": "QPlaceContentReply", "lineNumber": 14, "object": true, "qualifiedClassName": "QPlaceContentReply", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}], "inputFile": "qplacecontentreply.h", "outputRevision": 69}, {"classes": [{"className": "QPlaceDetailsReply", "lineNumber": 13, "object": true, "qualifiedClassName": "QPlaceDetailsReply", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}], "inputFile": "qplacedetailsreply.h", "outputRevision": 69}, {"classes": [{"className": "QPlaceIdReply", "lineNumber": 12, "object": true, "qualifiedClassName": "QPlaceIdReply", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}], "inputFile": "qplaceidreply.h", "outputRevision": 69}, {"classes": [{"className": "QPlaceManager", "lineNumber": 31, "object": true, "qualifiedClassName": "QPlaceManager", "signals": [{"access": "public", "arguments": [{"name": "reply", "type": "QPlaceReply*"}], "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QPlaceReply*"}, {"name": "error", "type": "QPlaceReply::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "index": 1, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QPlaceReply*"}, {"name": "error", "type": "QPlaceReply::<PERSON><PERSON><PERSON>"}], "index": 2, "isCloned": true, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "placeId", "type": "QString"}], "index": 3, "name": "placeAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "placeId", "type": "QString"}], "index": 4, "name": "placeUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "placeId", "type": "QString"}], "index": 5, "name": "placeRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "category", "type": "QPlaceCategory"}, {"name": "parentId", "type": "QString"}], "index": 6, "name": "categoryAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "category", "type": "QPlaceCategory"}, {"name": "parentId", "type": "QString"}], "index": 7, "name": "categoryUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "categoryId", "type": "QString"}, {"name": "parentId", "type": "QString"}], "index": 8, "name": "categoryRemoved", "returnType": "void"}, {"access": "public", "index": 9, "name": "dataChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplacemanager.h", "outputRevision": 69}, {"classes": [{"className": "QPlaceManagerEngine", "lineNumber": 18, "object": true, "qualifiedClassName": "QPlaceManagerEngine", "signals": [{"access": "public", "arguments": [{"name": "reply", "type": "QPlaceReply*"}], "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QPlaceReply*"}, {"name": "error", "type": "QPlaceReply::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "index": 1, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"type": "QPlaceReply*"}, {"name": "error", "type": "QPlaceReply::<PERSON><PERSON><PERSON>"}], "index": 2, "isCloned": true, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "placeId", "type": "QString"}], "index": 3, "name": "placeAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "placeId", "type": "QString"}], "index": 4, "name": "placeUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "placeId", "type": "QString"}], "index": 5, "name": "placeRemoved", "returnType": "void"}, {"access": "public", "arguments": [{"name": "category", "type": "QPlaceCategory"}, {"name": "parentCategoryId", "type": "QString"}], "index": 6, "name": "categoryAdded", "returnType": "void"}, {"access": "public", "arguments": [{"name": "category", "type": "QPlaceCategory"}, {"name": "parentCategoryId", "type": "QString"}], "index": 7, "name": "categoryUpdated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "categoryId", "type": "QString"}, {"name": "parentCategoryId", "type": "QString"}], "index": 8, "name": "categoryRemoved", "returnType": "void"}, {"access": "public", "index": 9, "name": "dataChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplacemanagerengine.h", "outputRevision": 69}, {"classes": [{"className": "QPlaceMatchReply", "lineNumber": 14, "object": true, "qualifiedClassName": "QPlaceMatchReply", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}], "inputFile": "qplacematchreply.h", "outputRevision": 69}, {"classes": [{"className": "QPlaceReply", "lineNumber": 14, "object": true, "qualifiedClassName": "QPlaceReply", "signals": [{"access": "public", "index": 0, "name": "finished", "returnType": "void"}, {"access": "public", "index": 1, "name": "contentUpdated", "returnType": "void"}, {"access": "public", "index": 2, "name": "aborted", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QPlaceReply::<PERSON><PERSON><PERSON>"}, {"name": "errorString", "type": "QString"}], "index": 3, "name": "errorOccurred", "returnType": "void"}, {"access": "public", "arguments": [{"name": "error", "type": "QPlaceReply::<PERSON><PERSON><PERSON>"}], "index": 4, "isCloned": true, "name": "errorOccurred", "returnType": "void"}], "slots": [{"access": "public", "index": 5, "name": "abort", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qplacereply.h", "outputRevision": 69}, {"classes": [{"className": "QPlaceSearchReply", "lineNumber": 15, "object": true, "qualifiedClassName": "QPlaceSearchReply", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}], "inputFile": "qplacesearchreply.h", "outputRevision": 69}, {"classes": [{"className": "QPlaceSearchSuggestionReply", "lineNumber": 15, "object": true, "qualifiedClassName": "QPlaceSearchSuggestionReply", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}], "inputFile": "qplacesearchsuggestionreply.h", "outputRevision": 69}, {"classes": [{"className": "QPlaceDetailsReplyUnsupported", "lineNumber": 28, "object": true, "qualifiedClassName": "QPlaceDetailsReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceDetailsReply"}]}, {"className": "QPlaceContentReplyUnsupported", "lineNumber": 52, "object": true, "qualifiedClassName": "QPlaceContentReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceContentReply"}]}, {"className": "QPlaceSearchReplyUnsupported", "lineNumber": 76, "object": true, "qualifiedClassName": "QPlaceSearchReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceSearchReply"}]}, {"className": "QPlaceSearchSuggestionReplyUnsupported", "lineNumber": 100, "object": true, "qualifiedClassName": "QPlaceSearchSuggestionReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceSearchSuggestionReply"}]}, {"className": "QPlaceIdReplyUnsupported", "lineNumber": 124, "object": true, "qualifiedClassName": "QPlaceIdReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceIdReply"}]}, {"className": "QPlaceReplyUnsupported", "lineNumber": 148, "object": true, "qualifiedClassName": "QPlaceReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceReply"}]}, {"className": "QPlaceMatchReplyUnsupported", "lineNumber": 171, "object": true, "qualifiedClassName": "QPlaceMatchReplyUnsupported", "superClasses": [{"access": "public", "name": "QPlaceMatchReply"}]}], "inputFile": "unsupportedreplies_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapCircle"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeCircleMapItem", "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "center", "notify": "centerChanged", "read": "center", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setCenter"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "radius", "notify": "radiusChanged", "read": "radius", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setRadius"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "border", "read": "border", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeMapLineProperties*", "user": false}], "qualifiedClassName": "QDeclarativeCircleMapItem", "signals": [{"access": "public", "arguments": [{"name": "center", "type": "QGeoCoordinate"}], "index": 0, "name": "centerChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "radius", "type": "qreal"}], "index": 1, "name": "radiusChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 2, "name": "colorChanged", "returnType": "void"}], "slots": [{"access": "protected", "index": 3, "name": "markSourceDirtyAndUpdate", "returnType": "void"}, {"access": "protected", "index": 4, "name": "onLinePropertiesChanged", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "event", "type": "QGeoMapViewportChangeEvent"}], "index": 5, "name": "afterViewportChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeGeoMapItemBase"}]}], "inputFile": "qdeclarativecirclemapitem_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "Map"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoMap", "interfaces": [[{"className": "QQmlParserStatus", "id": "\"org.qt-project.Qt.QQmlParserStatus\""}]], "lineNumber": 40, "methods": [{"access": "public", "arguments": [{"name": "bearing", "type": "qreal"}, {"name": "coordinate", "type": "QGeoCoordinate"}], "index": 30, "name": "setBearing", "returnType": "void"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}, {"name": "point", "type": "QPointF"}], "index": 31, "name": "alignCoordinateToPoint", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QDeclarativeGeoMapItemBase*"}], "index": 32, "name": "removeMapItem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "item", "type": "QDeclarativeGeoMapItemBase*"}], "index": 33, "name": "addMapItem", "returnType": "void"}, {"access": "public", "arguments": [{"name": "itemGroup", "type": "QDeclarativeGeoMapItemGroup*"}], "index": 34, "name": "addMapItemGroup", "returnType": "void"}, {"access": "public", "arguments": [{"name": "itemGroup", "type": "QDeclarativeGeoMapItemGroup*"}], "index": 35, "name": "removeMapItemGroup", "returnType": "void"}, {"access": "public", "arguments": [{"name": "itemView", "type": "QDeclarativeGeoMapItemView*"}], "index": 36, "name": "removeMapItemView", "returnType": "void"}, {"access": "public", "arguments": [{"name": "itemView", "type": "QDeclarativeGeoMapItemView*"}], "index": 37, "name": "addMapItemView", "returnType": "void"}, {"access": "public", "index": 38, "name": "clearMapItems", "returnType": "void"}, {"access": "public", "arguments": [{"name": "position", "type": "QPointF"}, {"name": "clipToViewPort", "type": "bool"}], "index": 39, "isConst": true, "name": "toCoordinate", "returnType": "QGeoCoordinate"}, {"access": "public", "arguments": [{"name": "position", "type": "QPointF"}], "index": 40, "isCloned": true, "isConst": true, "name": "toCoordinate", "returnType": "QGeoCoordinate"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}, {"name": "clipToViewPort", "type": "bool"}], "index": 41, "isConst": true, "name": "fromCoordinate", "returnType": "QPointF"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "index": 42, "isCloned": true, "isConst": true, "name": "fromCoordinate", "returnType": "QPointF"}, {"access": "public", "arguments": [{"name": "items", "type": "QVariantList"}], "index": 43, "name": "fitViewportToMapItems", "returnType": "void"}, {"access": "public", "index": 44, "isCloned": true, "name": "fitViewportToMapItems", "returnType": "void"}, {"access": "public", "index": 45, "name": "fitViewportToVisibleMapItems", "returnType": "void"}, {"access": "public", "arguments": [{"name": "dx", "type": "int"}, {"name": "dy", "type": "int"}], "index": 46, "name": "pan", "returnType": "void"}, {"access": "public", "index": 47, "name": "prefetchData", "returnType": "void"}, {"access": "public", "index": 48, "name": "clearData", "returnType": "void"}, {"access": "public", "arguments": [{"name": "shape", "type": "QGeoShape"}, {"name": "margins", "type": "Q<PERSON><PERSON><PERSON>"}], "index": 49, "name": "fitViewportToGeoShape", "returnType": "void", "revision": 1293}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "plugin", "notify": "pluginChanged", "read": "plugin", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoServiceProvider*", "user": false, "write": "setPlugin"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "minimumZoomLevel", "notify": "minimumZoomLevelChanged", "read": "minimumZoomLevel", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMinimumZoomLevel"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "maximumZoomLevel", "notify": "maximumZoomLevelChanged", "read": "maximumZoomLevel", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMaximumZoomLevel"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "zoomLevel", "notify": "zoomLevelChanged", "read": "zoomLevel", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setZoomLevel"}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "tilt", "notify": "tiltChanged", "read": "tilt", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setTilt"}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "minimumTilt", "notify": "minimumTiltChanged", "read": "minimumTilt", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMinimumTilt"}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "maximumTilt", "notify": "maximumTiltChanged", "read": "maximumTilt", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMaximumTilt"}, {"constant": false, "designable": true, "final": false, "index": 7, "name": "bearing", "notify": "bearingChanged", "read": "bearing", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setBearing"}, {"constant": false, "designable": true, "final": false, "index": 8, "name": "fieldOfView", "notify": "fieldOfViewChanged", "read": "fieldOfView", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setFieldOfView"}, {"constant": false, "designable": true, "final": false, "index": 9, "name": "minimumFieldOfView", "notify": "minimumFieldOfViewChanged", "read": "minimumFieldOfView", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMinimumFieldOfView"}, {"constant": false, "designable": true, "final": false, "index": 10, "name": "maximumFieldOfView", "notify": "minimumFieldOfViewChanged", "read": "maximumFieldOfView", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMaximumFieldOfView"}, {"constant": false, "designable": true, "final": false, "index": 11, "name": "activeMapType", "notify": "activeMapTypeChanged", "read": "activeMapType", "required": false, "scriptable": true, "stored": true, "type": "QGeoMapType", "user": false, "write": "setActiveMapType"}, {"constant": false, "designable": true, "final": false, "index": 12, "name": "supportedMapTypes", "notify": "supportedMapTypesChanged", "read": "supportedMapTypes", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoMapType>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 13, "name": "center", "notify": "centerChanged", "read": "center", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setCenter"}, {"constant": false, "designable": true, "final": false, "index": 14, "name": "mapItems", "notify": "mapItemsChanged", "read": "mapItems", "required": false, "scriptable": true, "stored": true, "type": "QList<QObject*>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 15, "name": "error", "notify": "errorChanged", "read": "error", "required": false, "scriptable": true, "stored": true, "type": "QGeoServiceProvider::<PERSON><PERSON><PERSON>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 16, "name": "errorString", "notify": "errorChanged", "read": "errorString", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": false, "designable": true, "final": false, "index": 17, "name": "visibleRegion", "notify": "visibleRegionChanged", "read": "visibleRegion", "required": false, "scriptable": true, "stored": true, "type": "QGeoShape", "user": false, "write": "setVisibleRegion"}, {"constant": false, "designable": true, "final": false, "index": 18, "name": "copyrightsVisible", "notify": "copyrightsVisibleChanged", "read": "copyrightsVisible", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setCopyrightsVisible"}, {"constant": false, "designable": true, "final": false, "index": 19, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": false, "designable": true, "final": false, "index": 20, "name": "mapReady", "notify": "mapReadyChanged", "read": "mapReady", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": false, "designable": true, "final": false, "index": 21, "name": "visibleArea", "notify": "visibleAreaChanged", "read": "visibleArea", "required": false, "revision": 1292, "scriptable": true, "stored": true, "type": "QRectF", "user": false, "write": "setVisibleArea"}], "qualifiedClassName": "QDeclarativeGeoMap", "signals": [{"access": "public", "arguments": [{"name": "plugin", "type": "QDeclarativeGeoServiceProvider*"}], "index": 0, "name": "pluginChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "zoomLevel", "type": "qreal"}], "index": 1, "name": "zoomLevelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "index": 2, "name": "centerChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "activeMapTypeChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "supportedMapTypesChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minimumZoomLevel", "type": "qreal"}], "index": 5, "name": "minimumZoomLevelChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "maximumZoomLevel", "type": "qreal"}], "index": 6, "name": "maximumZoomLevelChanged", "returnType": "void"}, {"access": "public", "index": 7, "name": "mapItemsChanged", "returnType": "void"}, {"access": "public", "index": 8, "name": "errorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "link", "type": "QString"}], "index": 9, "name": "copyrightLinkActivated", "returnType": "void"}, {"access": "public", "arguments": [{"name": "visible", "type": "bool"}], "index": 10, "name": "copyrightsVisibleChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 11, "name": "colorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bearing", "type": "qreal"}], "index": 12, "name": "bearingChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "tilt", "type": "qreal"}], "index": 13, "name": "tiltChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "fieldOfView", "type": "qreal"}], "index": 14, "name": "fieldOfViewChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minimumTilt", "type": "qreal"}], "index": 15, "name": "minimumTiltChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "maximumTilt", "type": "qreal"}], "index": 16, "name": "maximumTiltChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "minimumFieldOfView", "type": "qreal"}], "index": 17, "name": "minimumFieldOfViewChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "maximumFieldOfView", "type": "qreal"}], "index": 18, "name": "maximumFieldOfViewChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "copyrightsImage", "type": "QImage"}], "index": 19, "name": "copyrightsImageChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "copyrightsHtml", "type": "QString"}], "index": 20, "name": "copyrightsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "ready", "type": "bool"}], "index": 21, "name": "mapReadyChanged", "returnType": "void"}, {"access": "public", "index": 22, "name": "visibleAreaChanged", "returnType": "void"}, {"access": "public", "index": 23, "name": "visibleRegionChanged", "returnType": "void", "revision": 1294}], "slots": [{"access": "private", "index": 24, "name": "mappingManagerInitialized", "returnType": "void"}, {"access": "private", "index": 25, "name": "pluginReady", "returnType": "void"}, {"access": "private", "index": 26, "name": "onSupportedMapTypesChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "oldCameraCapabilities", "type": "QGeoCameraCapabilities"}], "index": 27, "name": "onCameraCapabilitiesChanged", "returnType": "void"}, {"access": "private", "index": 28, "name": "onAttachedCopyrightNoticeVisibilityChanged", "returnType": "void"}, {"access": "private", "arguments": [{"name": "cameraData", "type": "QGeoCameraData"}], "index": 29, "name": "onCameraDataChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "qdeclarativegeomap_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapCopyrightNotice"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoMapCopyrightNotice", "lineNumber": 33, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "mapSource", "notify": "mapSourceChanged", "read": "mapSource", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeGeoMap*", "user": false, "write": "setMapSource"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "styleSheet", "notify": "styleSheetChanged", "read": "styleSheet", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setStyleSheet"}], "qualifiedClassName": "QDeclarativeGeoMapCopyrightNotice", "signals": [{"access": "public", "arguments": [{"name": "link", "type": "QString"}], "index": 0, "name": "linkActivated", "returnType": "void"}, {"access": "public", "index": 1, "name": "mapSourceChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 2, "name": "backgroundColorChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "styleSheet", "type": "QString"}], "index": 3, "name": "styleSheetChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "copyrightsVisibleChanged", "returnType": "void"}], "slots": [{"access": "public", "arguments": [{"name": "copyrightsImage", "type": "QImage"}], "index": 5, "name": "copyrightsImageChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "copyrightsHtml", "type": "QString"}], "index": 6, "name": "copyrightsChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "styleSheet", "type": "QString"}], "index": 7, "name": "onCopyrightsStyleSheetChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickPaintedItem"}]}], "inputFile": "qdeclarativegeomapcopyrightsnotice_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "GeoMapItemBase"}, {"name": "QML.AddedInVersion", "value": "1280"}, {"name": "QML.Creatable", "value": "false"}, {"name": "QML.UncreatableReason", "value": "GeoMapItemBase is not intended instantiable by developer."}], "className": "QDeclarativeGeoMapItemBase", "lineNumber": 48, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "geoShape", "read": "geoShape", "required": false, "scriptable": true, "stored": false, "type": "QGeoShape", "user": false, "write": "setGeoShape"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "autoFadeIn", "read": "autoFadeIn", "required": false, "revision": 1294, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoFadeIn"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "referenceSurface", "notify": "referenceSurfaceChanged", "read": "referenceSurface", "required": false, "revision": 1542, "scriptable": true, "stored": true, "type": "QLocation::ReferenceSurface", "user": false, "write": "setReferenceSurface"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "lodThreshold", "notify": "lodThresholdChanged", "read": "lodThreshold", "required": false, "revision": 1295, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setLodThreshold"}], "qualifiedClassName": "QDeclarativeGeoMapItemBase", "signals": [{"access": "public", "index": 0, "name": "mapItemOpacityChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "addTransitionFinished", "returnType": "void", "revision": 1292}, {"access": "public", "index": 2, "name": "removeTransitionFinished", "returnType": "void", "revision": 1292}, {"access": "public", "index": 3, "name": "referenceSurfaceChanged", "returnType": "void"}, {"access": "public", "index": 4, "name": "lodThresholdChanged", "returnType": "void"}], "slots": [{"access": "protected", "index": 5, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "event", "type": "QGeoMapViewportChangeEvent"}], "index": 6, "name": "afterViewportChanged", "returnType": "void"}, {"access": "protected", "index": 7, "name": "polishAndUpdate", "returnType": "void"}, {"access": "private", "arguments": [{"name": "camera", "type": "QGeoCameraData"}], "index": 8, "name": "baseCameraDataChanged", "returnType": "void"}, {"access": "private", "index": 9, "name": "visibleAreaChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}, {"className": "QDeclarativeGeoMapPainterPath", "lineNumber": 150, "object": true, "qualifiedClassName": "QDeclarativeGeoMapPainterPath", "superClasses": [{"access": "public", "name": "QQuickCurve"}]}], "inputFile": "qdeclarativegeomapitembase_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapItemGroup"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoMapItemGroup", "lineNumber": 25, "object": true, "qualifiedClassName": "QDeclarativeGeoMapItemGroup", "signals": [{"access": "public", "index": 0, "name": "mapItemOpacityChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "addTransitionFinished", "returnType": "void"}, {"access": "public", "index": 2, "name": "removeTransitionFinished", "returnType": "void"}], "slots": [{"access": "protected", "index": 3, "name": "onMapSizeChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QQuickItem"}]}], "inputFile": "qdeclarativegeomapitemgroup_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapItemView"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoMapItemView", "lineNumber": 43, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "model", "notify": "modelChanged", "read": "model", "required": false, "scriptable": true, "stored": true, "type": "Q<PERSON><PERSON><PERSON>", "user": false, "write": "setModel"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "delegate", "notify": "delegate<PERSON><PERSON><PERSON>", "read": "delegate", "required": false, "scriptable": true, "stored": true, "type": "QQmlComponent*", "user": false, "write": "setDelegate"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "autoFitViewport", "notify": "autoFitViewportChanged", "read": "autoFitViewport", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setAutoFitViewport"}, {"constant": false, "designable": true, "final": false, "index": 3, "member": "m_enter", "name": "add", "required": false, "revision": 1292, "scriptable": true, "stored": true, "type": "QQuickTransition*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "member": "m_exit", "name": "remove", "required": false, "revision": 1292, "scriptable": true, "stored": true, "type": "QQuickTransition*", "user": false}, {"constant": false, "designable": true, "final": false, "index": 5, "name": "mapItems", "read": "mapItems", "required": false, "revision": 1292, "scriptable": true, "stored": true, "type": "QList<QQuickItem*>", "user": false}, {"constant": false, "designable": true, "final": false, "index": 6, "name": "incubateDelegates", "notify": "incubateDelegatesChanged", "read": "incubateDelegates", "required": false, "revision": 1292, "scriptable": true, "stored": true, "type": "bool", "user": false, "write": "setIncubateDelegates"}], "qualifiedClassName": "QDeclarativeGeoMapItemView", "signals": [{"access": "public", "index": 0, "name": "modelChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "delegate<PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "public", "index": 2, "name": "autoFitViewportChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "incubateDelegatesChanged", "returnType": "void"}], "slots": [{"access": "private", "arguments": [{"name": "object", "type": "QObject*"}], "index": 4, "name": "destroyingItem", "returnType": "void"}, {"access": "private", "arguments": [{"name": "index", "type": "int"}, {"name": "object", "type": "QObject*"}], "index": 5, "name": "initItem", "returnType": "void"}, {"access": "private", "arguments": [{"name": "index", "type": "int"}, {"name": "object", "type": "QObject*"}], "index": 6, "name": "createdItem", "returnType": "void"}, {"access": "private", "arguments": [{"name": "changeSet", "type": "QQmlChangeSet"}, {"name": "reset", "type": "bool"}], "index": 7, "name": "modelUpdated", "returnType": "void"}, {"access": "private", "index": 8, "name": "exitTransitionFinished", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeGeoMapItemGroup"}]}], "inputFile": "qdeclarativegeomapitemview_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapQuickItem"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeGeoMapQuickItem", "lineNumber": 42, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "coordinate", "notify": "coordinateChanged", "read": "coordinate", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setCoordinate"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "anchorPoint", "notify": "anchorPointChanged", "read": "anchorPoint", "required": false, "scriptable": true, "stored": true, "type": "QPointF", "user": false, "write": "setAnchorPoint"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "zoomLevel", "notify": "zoomLevelChanged", "read": "zoomLevel", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setZoomLevel"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "sourceItem", "notify": "sourceItemChanged", "read": "sourceItem", "required": false, "scriptable": true, "stored": true, "type": "QQuickItem*", "user": false, "write": "setSourceItem"}], "qualifiedClassName": "QDeclarativeGeoMapQuickItem", "signals": [{"access": "public", "index": 0, "name": "coordinateChanged", "returnType": "void"}, {"access": "public", "index": 1, "name": "sourceItemChanged", "returnType": "void"}, {"access": "public", "index": 2, "name": "anchorPointChanged", "returnType": "void"}, {"access": "public", "index": 3, "name": "zoomLevelChanged", "returnType": "void"}], "slots": [{"access": "protected", "index": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "event", "type": "QGeoMapViewportChangeEvent"}], "index": 5, "name": "afterViewportChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeGeoMapItemBase"}]}], "inputFile": "qdeclarativegeomapquickitem_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapPolygon"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativePolygonMapItem", "lineNumber": 26, "methods": [{"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "index": 5, "name": "addCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "index": 6, "name": "removeCoordinate", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "path", "notify": "pathChanged", "read": "path", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoCoordinate>", "user": false, "write": "set<PERSON>ath"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "border", "read": "border", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeMapLineProperties*", "user": false}], "qualifiedClassName": "QDeclarativePolygonMapItem", "signals": [{"access": "public", "index": 0, "name": "pathChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 1, "name": "colorChanged", "returnType": "void"}], "slots": [{"access": "protected", "index": 2, "name": "markSourceDirtyAndUpdate", "returnType": "void"}, {"access": "protected", "index": 3, "name": "onLinePropertiesChanged", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "event", "type": "QGeoMapViewportChangeEvent"}], "index": 4, "name": "afterViewportChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeGeoMapItemBase"}]}], "inputFile": "qdeclarativepolygonmapitem_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "anonymous"}], "className": "QDeclarativeMapLineProperties", "lineNumber": 30, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "width", "notify": "widthChanged", "read": "width", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}], "qualifiedClassName": "QDeclarativeMapLineProperties", "signals": [{"access": "public", "arguments": [{"name": "width", "type": "qreal"}], "index": 0, "name": "widthChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 1, "name": "colorChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}, {"classInfos": [{"name": "QML.Element", "value": "MapPolyline"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativePolylineMapItem", "lineNumber": 57, "methods": [{"access": "public", "index": 3, "isConst": true, "name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "int"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "index": 4, "name": "addCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "coordinate", "type": "QGeoCoordinate"}], "index": 5, "name": "insertCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}, {"name": "coordinate", "type": "QGeoCoordinate"}], "index": 6, "name": "replaceCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 7, "isConst": true, "name": "coordinateAt", "returnType": "QGeoCoordinate"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "index": 8, "name": "containsCoordinate", "returnType": "bool"}, {"access": "public", "arguments": [{"name": "coordinate", "type": "QGeoCoordinate"}], "index": 9, "name": "removeCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "index", "type": "int"}], "index": 10, "name": "removeCoordinate", "returnType": "void"}, {"access": "public", "arguments": [{"name": "path", "type": "QGeoPath"}], "index": 11, "name": "set<PERSON>ath", "returnType": "void"}], "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "path", "notify": "pathChanged", "read": "path", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoCoordinate>", "user": false, "write": "set<PERSON>ath"}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "line", "read": "line", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeMapLineProperties*", "user": false}], "qualifiedClassName": "QDeclarativePolylineMapItem", "signals": [{"access": "public", "index": 0, "name": "pathChanged", "returnType": "void"}], "slots": [{"access": "protected", "index": 1, "name": "updateAfterLinePropertiesChanged", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "event", "type": "QGeoMapViewportChangeEvent"}], "index": 2, "name": "afterViewportChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeGeoMapItemBase"}]}], "inputFile": "qdeclarativepolylinemapitem_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapRectangle"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeRectangleMapItem", "lineNumber": 31, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "topLeft", "notify": "topLeftChanged", "read": "topLeft", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setTopLeft"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "bottomRight", "notify": "bottomRightChanged", "read": "bottomRight", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false, "write": "setBottomRight"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "color", "notify": "colorChanged", "read": "color", "required": false, "scriptable": true, "stored": true, "type": "QColor", "user": false, "write": "setColor"}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "border", "read": "border", "required": false, "scriptable": true, "stored": true, "type": "QDeclarativeMapLineProperties*", "user": false}], "qualifiedClassName": "QDeclarativeRectangleMapItem", "signals": [{"access": "public", "arguments": [{"name": "topLeft", "type": "QGeoCoordinate"}], "index": 0, "name": "topLeftChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "bottomRight", "type": "QGeoCoordinate"}], "index": 1, "name": "bottomRightChanged", "returnType": "void"}, {"access": "public", "arguments": [{"name": "color", "type": "QColor"}], "index": 2, "name": "colorChanged", "returnType": "void"}], "slots": [{"access": "protected", "index": 3, "name": "markSourceDirtyAndUpdate", "returnType": "void"}, {"access": "protected", "index": 4, "name": "onLinePropertiesChanged", "returnType": "void"}, {"access": "protected", "arguments": [{"name": "event", "type": "QGeoMapViewportChangeEvent"}], "index": 5, "name": "afterViewportChanged", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativeGeoMapItemBase"}]}], "inputFile": "qdeclarativerectanglemapitem_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "MapRoute"}, {"name": "QML.AddedInVersion", "value": "1280"}], "className": "QDeclarativeRouteMapItem", "lineNumber": 28, "object": true, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "route", "notify": "routeChanged", "read": "route", "required": false, "scriptable": true, "stored": true, "type": "QGeoRoute", "user": false, "write": "setRoute"}], "qualifiedClassName": "QDeclarativeRouteMapItem", "signals": [{"access": "public", "arguments": [{"name": "route", "type": "QGeoRoute"}], "index": 0, "name": "routeChanged", "returnType": "void"}], "slots": [{"access": "private", "index": 1, "name": "updateRoutePath", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QDeclarativePolylineMapItem"}]}], "inputFile": "qdeclarativeroutemapitem_p.h", "outputRevision": 69}, {"classes": [{"className": "QGeoCameraCapabilities", "gadget": true, "lineNumber": 28, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "minimumZoomLevel", "read": "minimumZoomLevelAt256", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "maximumZoomLevel", "read": "maximumZoomLevelAt256", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "minimumTilt", "read": "minimumTilt", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "maximumTilt", "read": "maximumTilt", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "minimumFieldOfView", "read": "minimumFieldOfView", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "maximumFieldOfView", "read": "maximumFieldOfView", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}], "qualifiedClassName": "QGeoCameraCapabilities"}], "inputFile": "qgeocameracapabilities_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "routeManeuver"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QGeoManeuver", "enums": [{"isClass": false, "isFlag": false, "name": "InstructionDirection", "values": ["NoDirection", "DirectionForward", "DirectionBearRight", "DirectionLightRight", "DirectionRight", "DirectionHardRight", "DirectionUTurnRight", "DirectionUTurnLeft", "DirectionHardLeft", "DirectionLeft", "DirectionLightLeft", "DirectionBearLeft"]}], "gadget": true, "lineNumber": 19, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "valid", "read": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "position", "read": "position", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "instructionText", "read": "instructionText", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "direction", "read": "direction", "required": false, "scriptable": true, "stored": true, "type": "InstructionDirection", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "timeToNextInstruction", "read": "timeToNextInstruction", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "distanceToNextInstruction", "read": "distanceToNextInstruction", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "waypoint", "read": "waypoint", "required": false, "scriptable": true, "stored": true, "type": "QGeoCoordinate", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "extendedAttributes", "read": "extendedAttributes", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false}], "qualifiedClassName": "QGeoManeuver"}], "inputFile": "qgeomaneuver.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "mapType"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QGeoMapType", "enums": [{"isClass": false, "isFlag": false, "name": "MapStyle", "values": ["NoMap", "StreetMap", "SatelliteMapDay", "SatelliteMapNight", "TerrainMap", "HybridMap", "TransitMap", "GrayStreetMap", "PedestrianMap", "CarNavigationMap", "CycleMap", "CustomMap"]}], "gadget": true, "lineNumber": 31, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "style", "read": "style", "required": false, "scriptable": true, "stored": true, "type": "MapStyle", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "description", "read": "description", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "mobile", "read": "mobile", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 4, "name": "night", "read": "night", "required": false, "scriptable": true, "stored": true, "type": "bool", "user": false}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "cameraCapabilities", "read": "cameraCapabilities", "required": false, "scriptable": true, "stored": true, "type": "QGeoCameraCapabilities", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "metadata", "read": "metadata", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false}], "qualifiedClassName": "QGeoMapType"}, {"classInfos": [{"name": "QML.Foreign", "value": "QGeoMapType"}, {"name": "QML.ForeignIsNamespace", "value": "true"}, {"name": "QML.Element", "value": "MapType"}], "className": "QGeoMapTypeForeignNamespace", "lineNumber": 98, "namespace": true, "qualifiedClassName": "QGeoMapTypeForeignNamespace"}], "inputFile": "qgeomaptype_p.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "route"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QGeoRoute", "gadget": true, "lineNumber": 23, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "routeId", "read": "routeId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "bounds", "read": "bounds", "required": false, "scriptable": true, "stored": true, "type": "QGeoRectangle", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "travelTime", "read": "travelTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "distance", "read": "distance", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": false, "designable": true, "final": false, "index": 4, "name": "path", "read": "path", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoCoordinate>", "user": false, "write": "set<PERSON>ath"}, {"constant": true, "designable": true, "final": false, "index": 5, "name": "routeLegs", "read": "routeLegs", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoRoute>", "user": false}, {"constant": true, "designable": true, "final": false, "index": 6, "name": "extendedAttributes", "read": "extendedAttributes", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false}, {"constant": true, "designable": true, "final": false, "index": 7, "name": "legIndex", "read": "legIndex", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 8, "name": "overallRoute", "read": "overallRoute", "required": false, "scriptable": true, "stored": true, "type": "QGeoRoute", "user": false}, {"constant": true, "designable": true, "final": false, "index": 9, "name": "segmentsCount", "read": "segmentsCount", "required": false, "scriptable": true, "stored": true, "type": "qsizetype", "user": false}, {"constant": true, "designable": true, "final": false, "index": 10, "name": "segments", "read": "segments", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoRouteSegment>", "user": false}], "qualifiedClassName": "QGeoRoute"}], "inputFile": "qgeoroute.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "routeSegment"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QGeoRouteSegment", "gadget": true, "lineNumber": 20, "properties": [{"constant": true, "designable": true, "final": false, "index": 0, "name": "travelTime", "read": "travelTime", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false}, {"constant": true, "designable": true, "final": false, "index": 1, "name": "distance", "read": "distance", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false}, {"constant": true, "designable": true, "final": false, "index": 2, "name": "path", "read": "path", "required": false, "scriptable": true, "stored": true, "type": "QList<QGeoCoordinate>", "user": false}, {"constant": true, "designable": true, "final": false, "index": 3, "name": "maneuver", "read": "maneuver", "required": false, "scriptable": true, "stored": true, "type": "QGeoManeuver", "user": false}], "qualifiedClassName": "QGeoRouteSegment"}], "inputFile": "qgeoroutesegment.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "placeAttribute"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QPlaceAttribute", "gadget": true, "lineNumber": 19, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "label", "read": "label", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "text", "read": "text", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setText"}], "qualifiedClassName": "QPlaceAttribute"}], "inputFile": "qplaceattribute.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "contactDetail"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QPlaceContactDetail", "gadget": true, "lineNumber": 19, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "label", "read": "label", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "<PERSON><PERSON><PERSON><PERSON>"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "value", "read": "value", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setValue"}], "qualifiedClassName": "QPlaceContactDetail"}], "inputFile": "qplacecontactdetail.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "icon"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QPlaceIcon", "gadget": true, "lineNumber": 22, "methods": [{"access": "public", "arguments": [{"name": "size", "type": "QSize"}], "index": 0, "isConst": true, "name": "url", "returnType": "QUrl"}, {"access": "public", "index": 1, "isCloned": true, "isConst": true, "name": "url", "returnType": "QUrl"}], "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "parameters", "read": "parameters", "required": false, "scriptable": true, "stored": true, "type": "QVariantMap", "user": false, "write": "setParameters"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "manager", "read": "manager", "required": false, "scriptable": true, "stored": true, "type": "QPlaceManager*", "user": false, "write": "setManager"}], "qualifiedClassName": "QPlaceIcon"}], "inputFile": "qplaceicon.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "ratings"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QPlaceRatings", "gadget": true, "lineNumber": 17, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "average", "read": "average", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setAverage"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "maximum", "read": "maximum", "required": false, "scriptable": true, "stored": true, "type": "qreal", "user": false, "write": "setMaximum"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "count", "read": "count", "required": false, "scriptable": true, "stored": true, "type": "int", "user": false, "write": "setCount"}], "qualifiedClassName": "QPlaceRatings"}], "inputFile": "qplaceratings.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "supplier"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QPlaceSupplier", "gadget": true, "lineNumber": 19, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "supplierId", "read": "supplierId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setSupplierId"}, {"constant": false, "designable": true, "final": false, "index": 2, "name": "url", "read": "url", "required": false, "scriptable": true, "stored": true, "type": "QUrl", "user": false, "write": "setUrl"}, {"constant": false, "designable": true, "final": false, "index": 3, "name": "icon", "read": "icon", "required": false, "scriptable": true, "stored": true, "type": "QPlaceIcon", "user": false, "write": "setIcon"}], "qualifiedClassName": "QPlaceSupplier"}], "inputFile": "qplacesupplier.h", "outputRevision": 69}, {"classes": [{"classInfos": [{"name": "QML.Element", "value": "user"}, {"name": "QML.Creatable", "value": "true"}, {"name": "QML.CreationMethod", "value": "structured"}], "className": "QPlaceUser", "gadget": true, "lineNumber": 17, "properties": [{"constant": false, "designable": true, "final": false, "index": 0, "name": "userId", "read": "userId", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setUserId"}, {"constant": false, "designable": true, "final": false, "index": 1, "name": "name", "read": "name", "required": false, "scriptable": true, "stored": true, "type": "QString", "user": false, "write": "setName"}], "qualifiedClassName": "QPlaceUser"}], "inputFile": "qplaceuser.h", "outputRevision": 69}, {"classes": [{"className": "RetryFuture", "lineNumber": 156, "object": true, "qualifiedClassName": "RetryFuture", "slots": [{"access": "public", "index": 0, "name": "retry", "returnType": "void"}], "superClasses": [{"access": "public", "name": "QObject"}]}], "inputFile": "qgeotilerequestmanager.cpp", "outputRevision": 69}]