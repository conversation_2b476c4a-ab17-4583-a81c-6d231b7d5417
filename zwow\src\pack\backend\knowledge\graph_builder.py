"""
知识图谱构建器
从文档中提取实体和关系，构建知识图谱
"""

import os
import json
import logging
import re
from collections import Counter, defaultdict
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
import sys

# 添加项目根目录到路径
current_file = Path(__file__)
project_root = current_file.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.pack.config.settings_loader import get_setting, get_absolute_path
    from .document_processor import DocumentProcessor
except ImportError as e:
    print(f"导入模块失败: {e}")
    def get_setting(key, default=None): return default
    def get_absolute_path(path): return path
    class DocumentProcessor:
        def __init__(self): pass
        def process_documents(self, path): return []

# 配置日志
logger = logging.getLogger(__name__)

class GraphBuilder:
    """知识图谱构建器类"""
    
    def __init__(self):
        """初始化知识图谱构建器"""
        self.document_processor = DocumentProcessor()
        
        # 获取配置
        self.min_entity_freq = get_setting("knowledge_graph.entity_extraction.min_entity_freq", 2)
        self.max_entity_length = get_setting("knowledge_graph.entity_extraction.max_entity_length", 10)
        self.min_relation_freq = get_setting("knowledge_graph.relation_extraction.min_relation_freq", 2)
        self.max_distance = get_setting("knowledge_graph.relation_extraction.max_distance", 50)
        
        # 自定义实体和关系
        self.custom_entities = get_setting("knowledge_graph.entity_extraction.custom_entities", [])
        self.custom_relations = get_setting("knowledge_graph.relation_extraction.custom_relations", 
                                           ["包含", "属于", "应用于", "等同于"])
        
        # 计数器
        self.entity_counter = Counter()
        self.relation_counter = Counter()
        
        # 图结构
        self.entities = {}
        self.relations = []
        
        logger.info("知识图谱构建器已初始化")
    
    def build_knowledge_graph(self, source_path: str, output_path: str) -> bool:
        """
        构建知识图谱
        
        Args:
            source_path: 源文档目录路径
            output_path: 输出图谱路径
            
        Returns:
            bool: 构建是否成功
        """
        try:
            logger.info(f"开始构建知识图谱，源路径: {source_path}")
            
            # 处理文档
            documents = self.document_processor.process_documents(source_path)
            if not documents:
                logger.warning("没有找到可处理的文档")
                return False
            
            logger.info(f"找到 {len(documents)} 个文档块")
            
            # 提取实体和关系
            self._extract_entities_and_relations(documents)
            
            # 过滤低频项
            self._filter_low_frequency_items()
            
            # 构建图结构
            self._build_graph_structure()
            
            # 保存知识图谱
            success = self._save_knowledge_graph(output_path)
            
            if success:
                logger.info(f"知识图谱构建成功，保存到: {output_path}")
            else:
                logger.error("保存知识图谱失败")
            
            return success
            
        except Exception as e:
            logger.error(f"构建知识图谱时出错: {str(e)}")
            return False
    
    def _extract_entities_and_relations(self, documents: List[Dict]) -> None:
        """
        从文档中提取实体和关系
        
        Args:
            documents: 文档列表
        """
        logger.info("开始提取实体和关系")
        
        for doc in documents:
            content = doc.get('content', '')
            if not content:
                continue
            
            # 提取实体
            entities = self._extract_entities(content)
            for entity in entities:
                self.entity_counter[entity] += 1
            
            # 提取关系
            relations = self._extract_relations(content, entities)
            for relation in relations:
                self.relation_counter[relation] += 1
        
        logger.info(f"提取完成，实体: {len(self.entity_counter)}, 关系: {len(self.relation_counter)}")
    
    def _extract_entities(self, text: str) -> List[str]:
        """
        从文本中提取实体
        
        Args:
            text: 输入文本
            
        Returns:
            List[str]: 实体列表
        """
        entities = []
        
        # 添加自定义实体
        for entity in self.custom_entities:
            if entity in text:
                entities.append(entity)
        
        # 简单的实体提取规则
        # 1. 提取中文词汇（2-10个字符）
        chinese_pattern = r'[\u4e00-\u9fff]{2,10}'
        chinese_entities = re.findall(chinese_pattern, text)
        entities.extend(chinese_entities)
        
        # 2. 提取英文词汇
        english_pattern = r'\b[A-Za-z]{2,20}\b'
        english_entities = re.findall(english_pattern, text)
        entities.extend(english_entities)
        
        # 3. 提取数字+单位的组合
        number_unit_pattern = r'\d+[a-zA-Z\u4e00-\u9fff]+'
        number_entities = re.findall(number_unit_pattern, text)
        entities.extend(number_entities)
        
        # 过滤长度
        entities = [e for e in entities if len(e) <= self.max_entity_length]
        
        # 去重
        return list(set(entities))
    
    def _extract_relations(self, text: str, entities: List[str]) -> List[str]:
        """
        从文本中提取关系
        
        Args:
            text: 输入文本
            entities: 实体列表
            
        Returns:
            List[str]: 关系列表
        """
        relations = []
        
        # 为每对实体寻找关系
        for i, entity1 in enumerate(entities):
            for j, entity2 in enumerate(entities):
                if i >= j:  # 避免重复和自关联
                    continue
                
                # 查找两个实体之间的关系
                relation = self._find_relation_between_entities(text, entity1, entity2)
                if relation:
                    relation_key = f"{entity1}|{relation}|{entity2}"
                    relations.append(relation_key)
        
        return relations
    
    def _find_relation_between_entities(self, text: str, entity1: str, entity2: str) -> Optional[str]:
        """
        查找两个实体之间的关系
        
        Args:
            text: 文本
            entity1: 实体1
            entity2: 实体2
            
        Returns:
            Optional[str]: 关系类型
        """
        # 查找实体在文本中的位置
        pos1 = text.find(entity1)
        pos2 = text.find(entity2)
        
        if pos1 == -1 or pos2 == -1:
            return None
        
        # 确保距离在合理范围内
        if abs(pos1 - pos2) > self.max_distance:
            return None
        
        # 获取两个实体之间的文本
        start_pos = min(pos1, pos2)
        end_pos = max(pos1 + len(entity1), pos2 + len(entity2))
        between_text = text[start_pos:end_pos]
        
        # 检查自定义关系
        for relation in self.custom_relations:
            if relation in between_text:
                return relation
        
        # 简单的关系模式匹配
        relation_patterns = {
            "包含": [r"包含", r"含有", r"包括"],
            "属于": [r"属于", r"是", r"为"],
            "应用于": [r"应用于", r"用于", r"适用于"],
            "等同于": [r"等同于", r"相当于", r"就是"]
        }
        
        for relation_type, patterns in relation_patterns.items():
            for pattern in patterns:
                if re.search(pattern, between_text):
                    return relation_type
        
        # 默认关系
        return "相关"
    
    def _filter_low_frequency_items(self) -> None:
        """过滤低频实体和关系"""
        logger.info("过滤低频实体和关系")
        
        # 过滤低频实体
        original_entity_count = len(self.entity_counter)
        self.entity_counter = Counter({
            entity: count for entity, count in self.entity_counter.items()
            if count >= self.min_entity_freq
        })
        logger.info(f"实体过滤: {original_entity_count} -> {len(self.entity_counter)}")
        
        # 过滤低频关系
        original_relation_count = len(self.relation_counter)
        self.relation_counter = Counter({
            relation: count for relation, count in self.relation_counter.items()
            if count >= self.min_relation_freq
        })
        logger.info(f"关系过滤: {original_relation_count} -> {len(self.relation_counter)}")
    
    def _build_graph_structure(self) -> None:
        """构建图结构"""
        logger.info("构建图结构")
        
        # 构建实体字典
        for entity, weight in self.entity_counter.items():
            self.entities[entity] = {
                'id': entity,
                'label': entity,
                'weight': weight,
                'type': self._classify_entity(entity)
            }
        
        # 构建关系列表
        for relation_key, weight in self.relation_counter.items():
            try:
                parts = relation_key.split("|", 2)
                if len(parts) == 3:
                    head, relation, tail = parts
                    if head in self.entities and tail in self.entities:
                        self.relations.append({
                            'source': head,
                            'target': tail,
                            'relation': relation,
                            'weight': weight
                        })
            except Exception:
                continue
        
        logger.info(f"图结构构建完成: {len(self.entities)} 个实体, {len(self.relations)} 条关系")
    
    def _classify_entity(self, entity: str) -> str:
        """
        分类实体类型
        
        Args:
            entity: 实体名称
            
        Returns:
            str: 实体类型
        """
        # 简单的实体分类规则
        if re.match(r'\d+', entity):
            return "数值"
        elif re.match(r'[A-Za-z]+', entity):
            return "英文"
        elif len(entity) <= 3:
            return "概念"
        else:
            return "实体"
    
    def _save_knowledge_graph(self, output_path: str) -> bool:
        """
        保存知识图谱
        
        Args:
            output_path: 输出路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            output_path = get_absolute_path(output_path)
            os.makedirs(output_path, exist_ok=True)
            
            # 保存实体
            entities_path = os.path.join(output_path, "entities.json")
            with open(entities_path, 'w', encoding='utf-8') as f:
                json.dump(self.entities, f, ensure_ascii=False, indent=2)
            
            # 保存关系
            relations_path = os.path.join(output_path, "relations.json")
            with open(relations_path, 'w', encoding='utf-8') as f:
                json.dump(self.relations, f, ensure_ascii=False, indent=2)
            
            # 保存统计信息
            stats = {
                'entity_count': len(self.entities),
                'relation_count': len(self.relations),
                'top_entities': dict(self.entity_counter.most_common(10)),
                'top_relations': dict(self.relation_counter.most_common(10))
            }
            stats_path = os.path.join(output_path, "stats.json")
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            
            # 生成可视化HTML（简化版）
            self._generate_visualization(output_path)
            
            logger.info(f"知识图谱已保存到: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存知识图谱时出错: {str(e)}")
            return False
    
    def _generate_visualization(self, output_path: str) -> None:
        """
        生成知识图谱可视化HTML
        
        Args:
            output_path: 输出路径
        """
        try:
            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>知识图谱可视化</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .stats {{ background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .entity {{ background: #e3f2fd; padding: 5px; margin: 2px; display: inline-block; border-radius: 3px; }}
        .relation {{ background: #fff3e0; padding: 5px; margin: 2px; display: block; border-radius: 3px; }}
    </style>
</head>
<body>
    <h1>知识图谱可视化</h1>
    
    <div class="stats">
        <h2>统计信息</h2>
        <p>实体数量: {len(self.entities)}</p>
        <p>关系数量: {len(self.relations)}</p>
    </div>
    
    <div>
        <h2>主要实体</h2>
        {''.join([f'<span class="entity">{entity} ({weight})</span>' 
                 for entity, weight in self.entity_counter.most_common(20)])}
    </div>
    
    <div>
        <h2>主要关系</h2>
        {''.join([f'<div class="relation">{relation} ({weight})</div>' 
                 for relation, weight in self.relation_counter.most_common(20)])}
    </div>
    
</body>
</html>
            """
            
            html_path = os.path.join(output_path, "visualization.html")
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"可视化文件已生成: {html_path}")
            
        except Exception as e:
            logger.error(f"生成可视化文件时出错: {str(e)}")
    
    def load_knowledge_graph(self, graph_path: str) -> Tuple[Optional[Dict], Optional[List[Dict]]]:
        """
        加载知识图谱
        
        Args:
            graph_path: 图谱路径
            
        Returns:
            Tuple: (实体字典, 关系列表)
        """
        try:
            graph_path = get_absolute_path(graph_path)
            
            entities_path = os.path.join(graph_path, "entities.json")
            relations_path = os.path.join(graph_path, "relations.json")
            
            if not os.path.exists(entities_path) or not os.path.exists(relations_path):
                logger.warning(f"知识图谱文件不存在: {graph_path}")
                return None, None
            
            # 加载实体
            with open(entities_path, 'r', encoding='utf-8') as f:
                entities = json.load(f)
            
            # 加载关系
            with open(relations_path, 'r', encoding='utf-8') as f:
                relations = json.load(f)
            
            logger.info(f"知识图谱加载成功: {graph_path}")
            return entities, relations
            
        except Exception as e:
            logger.error(f"加载知识图谱时出错: {str(e)}")
            return None, None
