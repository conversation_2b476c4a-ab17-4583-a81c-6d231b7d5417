"""
用户数据操作模块
提供用户表的CRUD操作
"""
import logging
from typing import Dict, List, Tuple, Optional, Any
from .db_manager import DatabaseManager

logger = logging.getLogger(__name__)

class UserOperations:
    """用户数据操作类"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """
        初始化用户数据操作
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager or DatabaseManager()
    
    def add_user(self, username: str, password: str, api_key: str = "") -> int:
        """
        添加新用户
        
        Args:
            username: 用户名
            password: 密码
            api_key: 可选的API密钥，新结构中不再使用
            
        Returns:
            int: 新用户ID，失败返回-1
        """
        # 检查用户是否已存在
        existing_user = self.get_user_by_username(username)
        if existing_user:
            logger.warning(f"用户已存在: {username}")
            return -1
        
        # 新结构中使用plg_usn和plg_pwd字段
        query = """
        INSERT INTO users (plg_usn, plg_pwd)
        VALUES (?, ?)
        """
        
        if not self.db_manager.connect():
            return -1
        
        try:
            self.db_manager.cursor.execute(query, (username, password))
            self.db_manager.conn.commit()
            return self.db_manager.get_last_insert_id()
        except Exception as e:
            logger.error(f"添加用户时出错: {str(e)}")
            self.db_manager.conn.rollback()
            return -1
        finally:
            self.db_manager.close()
    
    def update_user(self, user_id: int, data: Dict[str, Any]) -> bool:
        """
        更新用户信息
        
        Args:
            user_id: 用户ID
            data: 更新数据字典
            
        Returns:
            bool: 操作是否成功
        """
        if not data:
            return False
        
        # 映射字段名到数据库字段
        field_mapping = {
            'username': 'plg_usn',
            'password': 'plg_pwd'
        }
        
        # 转换字段名
        db_data = {}
        for key, value in data.items():
            db_key = field_mapping.get(key, key)
            db_data[db_key] = value
        
        # 构建更新语句
        set_clause = ", ".join([f"{key} = ?" for key in db_data.keys()])
        query = f"UPDATE users SET {set_clause} WHERE id = ?"
        
        # 准备参数
        params = list(db_data.values())
        params.append(user_id)
        
        return self.db_manager.execute_update(query, tuple(params))
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict]:
        """
        通过ID获取用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[Dict]: 用户信息字典
        """
        # 新结构中使用plg_usn和plg_pwd字段
        query = """
        SELECT id, plg_usn, plg_pwd, created_at
        FROM users
        WHERE id = ?
        """
        
        results = self.db_manager.execute_query(query, (user_id,))
        if not results:
            return None
        
        return {
            'id': results[0][0],
            'username': results[0][1],  # 保留兼容性的键名
            'password': results[0][2],  # 保留兼容性的键名
            'created_at': results[0][3]
        }
    
    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """
        通过用户名获取用户
        
        Args:
            username: 用户名
            
        Returns:
            Optional[Dict]: 用户信息字典
        """
        # 新结构中使用plg_usn字段
        query = """
        SELECT id, plg_usn, plg_pwd, created_at
        FROM users
        WHERE plg_usn = ?
        """
        
        results = self.db_manager.execute_query(query, (username,))
        if not results:
            return None
        
        return {
            'id': results[0][0],
            'username': results[0][1],  # 保留兼容性的键名
            'password': results[0][2],  # 保留兼容性的键名
            'created_at': results[0][3]
        }
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Optional[Dict]: 认证成功返回用户信息，失败返回None
        """
        # 新结构中使用plg_usn和plg_pwd字段
        query = """
        SELECT id, plg_usn, plg_pwd, created_at
        FROM users
        WHERE plg_usn = ? AND plg_pwd = ?
        """
        
        results = self.db_manager.execute_query(query, (username, password))
        if not results:
            return None
        
        return {
            'id': results[0][0],
            'username': results[0][1],  
            'password': results[0][2],  
            'created_at': results[0][3]
        }
    
    def delete_user(self, user_id: int) -> bool:
        """
        删除用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 操作是否成功
        """
        query = "DELETE FROM users WHERE id = ?"
        return self.db_manager.execute_update(query, (user_id,))
    
    def get_all_users(self) -> List[Dict]:
        """
        获取所有用户
        
        Returns:
            List[Dict]: 用户信息列表
        """
        # 新结构中使用plg_usn和plg_pwd字段
        query = """
        SELECT id, plg_usn, plg_pwd, created_at
        FROM users
        """
        
        results = self.db_manager.execute_query(query)
        users = []
        
        for row in results:
            users.append({
                'id': row[0],
                'username': row[1],  # 保留兼容性的键名
                'password': row[2],  # 保留兼容性的键名
                'created_at': row[3]
            })
        
        return users
    
    def increment_call_count(self, user_id: int, increment: int = 1) -> bool:
        """
        增加API调用次数（兼容性方法，新结构中可能不需要）
        
        Args:
            user_id: 用户ID
            increment: 增加的次数
            
        Returns:
            bool: 操作是否成功
        """
        # 在新结构中，这个方法可能不需要，但保留以兼容旧代码
        logger.info(f"用户 {user_id} API调用次数增加 {increment}（新结构中此操作被忽略）")
        return True
    
    def user_exists(self, username: str) -> bool:
        """
        检查用户是否存在
        
        Args:
            username: 用户名
            
        Returns:
            bool: 用户是否存在
        """
        user = self.get_user_by_username(username)
        return user is not None
    
    def get_user_count(self) -> int:
        """
        获取用户总数
        
        Returns:
            int: 用户总数
        """
        query = "SELECT COUNT(*) FROM users"
        results = self.db_manager.execute_query(query)
        return results[0][0] if results else 0
